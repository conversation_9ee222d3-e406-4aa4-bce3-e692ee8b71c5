// BOOT.EFI Analysis Script
// @description Comprehensive analysis of BOOT.EFI
// @category Analysis

import ghidra.app.script.GhidraScript;
import ghidra.program.model.listing.*;
import ghidra.program.model.symbol.*;
import ghidra.program.model.mem.*;
import ghidra.program.model.data.*;
import ghidra.app.decompiler.*;
import ghidra.program.model.address.*;
import ghidra.program.util.*;
import java.io.*;

public class AnalyzeBootEFI extends GhidraScript {
    
    @Override
    public void run() throws Exception {
        
        Program program = getCurrentProgram();
        Listing listing = program.getListing();
        SymbolTable symbolTable = program.getSymbolTable();
        Memory memory = program.getMemory();
        
        println("=== BOOT.EFI Comprehensive Analysis ===");
        
        // 1. Basic Program Information
        println("1. Program Information:");
        println("  Name: " + program.getName());
        println("  Image Base: " + program.getImageBase());
        println("  Min Address: " + program.getMinAddress());
        println("  Max Address: " + program.getMaxAddress());
        println("  Memory Size: " + memory.getSize() + " bytes");
        
        // 2. Search for all strings
        println("\n2. String Analysis:");
        int stringCount = 0;
        DataIterator dataIterator = listing.getDefinedData(true);
        while (dataIterator.hasNext() && !monitor.isCancelled()) {
            Data data = dataIterator.next();
            if (data.hasStringValue()) {
                Object value = data.getValue();
                if (value != null && value.toString().length() > 3) {
                    println("  " + data.getAddress() + ": " + value);
                    stringCount++;
                    if (stringCount > 100) break; // Limit output
                }
            }
        }
        println("  Total strings found: " + stringCount);
        
        // 3. Function Analysis
        println("\n3. Function Analysis:");
        FunctionManager funcManager = program.getFunctionManager();
        FunctionIterator functions = funcManager.getFunctions(true);
        int funcCount = 0;
        while (functions.hasNext() && !monitor.isCancelled()) {
            Function func = functions.next();
            if (funcCount < 30) { // Limit output
                println("  " + func.getEntryPoint() + ": " + func.getName() + 
                       " (Size: " + func.getBody().getNumAddresses() + ")");
            }
            funcCount++;
        }
        println("  Total functions: " + funcCount);
        
        // 4. Search for verification-related patterns
        println("\n4. Verification Pattern Analysis:");
        String[] keywords = {"check", "verify", "MBR", "error", "fail", "device", 
                           "General_UDisk", "DmarInsert", "DMAR", "ACPI", "VTD"};
        
        for (String keyword : keywords) {
            Address[] addresses = findBytes(null, keyword.getBytes(), 20);
            if (addresses.length > 0) {
                println("  '" + keyword + "' found " + addresses.length + " times:");
                for (int i = 0; i < Math.min(addresses.length, 5); i++) {
                    println("    " + addresses[i]);
                }
            }
        }
        
        // 5. Memory Block Analysis
        println("\n5. Memory Block Analysis:");
        MemoryBlock[] blocks = memory.getBlocks();
        for (MemoryBlock block : blocks) {
            println("  " + block.getName() + ": " + block.getStart() + " - " + 
                   block.getEnd() + " (Size: " + block.getSize() + ", " +
                   (block.isExecute() ? "EXEC" : "") + 
                   (block.isRead() ? "READ" : "") + 
                   (block.isWrite() ? "write" : "") + ")");
        }
        
        // 6. Entry Point Analysis
        println("\n6. Entry Point Analysis:");
        Address entryPoint = program.getImageBase();
        if (entryPoint != null) {
            println("  Entry Point: " + entryPoint);
            
            // Try to find the main function
            Function entryFunc = funcManager.getFunctionAt(entryPoint);
            if (entryFunc != null) {
                println("  Entry Function: " + entryFunc.getName());
                
                // Decompile entry function
                DecompInterface decompiler = new DecompInterface();
                decompiler.openProgram(program);
                
                try {
                    DecompileResults results = decompiler.decompileFunction(entryFunc, 30, monitor);
                    if (results.decompileCompleted()) {
                        println("\n7. Entry Function Decompilation:");
                        String decompiledCode = results.getDecompiledFunction().getC();
                        println(decompiledCode.substring(0, Math.min(decompiledCode.length(), 3000)));
                    }
                } catch (Exception e) {
                    println("  Decompilation failed: " + e.getMessage());
                } finally {
                    decompiler.dispose();
                }
            }
        }
        
        // 7. Search for specific hardware verification patterns
        println("\n8. Hardware Verification Analysis:");
        
        // Look for error code 12 (0x0C)
        byte[] errorPattern = {(byte)0xB8, 0x0C, 0x00, 0x00, 0x00}; // mov eax, 12
        Address[] errorAddrs = findBytes(null, errorPattern, 10);
        if (errorAddrs.length > 0) {
            println("  Error code 12 assignments found " + errorAddrs.length + " times:");
            for (Address addr : errorAddrs) {
                println("    " + addr);
            }
        }
        
        // Look for comparison patterns
        byte[] cmpPattern = {(byte)0x83, (byte)0xF8, 0x0C}; // cmp eax, 12
        Address[] cmpAddrs = findBytes(null, cmpPattern, 10);
        if (cmpAddrs.length > 0) {
            println("  Error code 12 comparisons found " + cmpAddrs.length + " times:");
            for (Address addr : cmpAddrs) {
                println("    " + addr);
            }
        }
        
        // 8. Export Analysis Results
        println("\n9. Exporting detailed analysis to file...");
        exportAnalysisResults(program, funcManager);
        
        println("\n=== BOOT.EFI Analysis Complete ===");
    }
    
    private void exportAnalysisResults(Program program, FunctionManager funcManager) {
        try {
            File outputFile = new File("D:\\新建文件夹 (2)\\新建文件夹\\boot_efi_ghidra_analysis.txt");
            PrintWriter writer = new PrintWriter(new FileWriter(outputFile));
            
            writer.println("=== BOOT.EFI Ghidra Complete Analysis ===");
            writer.println("Generated on: " + new java.util.Date());
            writer.println();
            
            // Export all functions with their decompiled code
            writer.println("=== All Functions Decompilation ===");
            
            DecompInterface decompiler = new DecompInterface();
            decompiler.openProgram(program);
            
            FunctionIterator functions = funcManager.getFunctions(true);
            int count = 0;
            while (functions.hasNext() && count < 50) { // Limit to prevent huge files
                Function func = functions.next();
                writer.println("\n--- Function: " + func.getName() + " @ " + func.getEntryPoint() + " ---");
                
                try {
                    DecompileResults results = decompiler.decompileFunction(func, 30, monitor);
                    if (results.decompileCompleted()) {
                        String code = results.getDecompiledFunction().getC();
                        writer.println(code);
                    } else {
                        writer.println("Decompilation failed");
                    }
                } catch (Exception e) {
                    writer.println("Decompilation error: " + e.getMessage());
                }
                count++;
            }
            
            decompiler.dispose();
            writer.close();
            println("Analysis exported to: " + outputFile.getAbsolutePath());
            
        } catch (Exception e) {
            println("Export failed: " + e.getMessage());
        }
    }
}