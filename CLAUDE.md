# CLAUDE.md

This file provides guidance to <PERSON> (claude.ai/code) when working with code in this repository.

## System Overview

This is a Ventoy-based multiboot USB system with UEFI injection capabilities and hardware binding protection. It is NOT a traditional software development project but rather a bootable system with security mechanisms.

## Key Components

### EFI Boot System
- `EFI/BOOT/` - Contains multiple architecture EFI bootloaders (x64, ARM64, i386, MIPS)
- `BOOT.EFI` (23KB) - Primary boot file with hardware verification logic
- Multi-architecture support via separate EFI files for different CPU types

### GRUB Configuration
- `grub/grub.cfg` - Main GRUB configuration with Ventoy integration
- `grub/ventoy_grub.cfg` - Ventoy-specific boot menu entries
- Architecture-specific modules in `grub/{arch}/` directories
- Chainloader commands for EFI file execution

### Ventoy Core Files
- `ventoy/ventoy.json` - Main configuration (timeout: 10s, language: zh_CN, theme settings)
- `ventoy/ventoy_x64.efi` - Core Ventoy execution engine with verification logic
- `ventoy/*.cpio` - Compressed archives for different architectures
- `ventoy/vtoyutil_*.efi` - Utility programs for system operations

## Hardware Binding Mechanism

### UUID Protection
- **Binding Location**: MBR offset 0x1B8 (440 bytes)
- **Binding Data**: `e2a0354cc1a40000` (8-byte disk signature)
- **Verification**: Multiple EFI files contain hardware ID checking
- **Error Message**: "Error message: MBR check failed" when binding verification fails

### Security Analysis Commands
```bash
# Read current disk signature
dd if=/dev/sdb bs=1 skip=440 count=8 | xxd

# Check MBR structure
dd if=/dev/sdb bs=512 count=1 | od -x

# Extract EFI file for analysis
dd if="EFI/BOOT/BOOT.EFI" bs=1 count=23552 | od -c
```

## File Structure Analysis

### Critical Paths
- Boot flow: `EFI/BOOT/BOOT.EFI` → `grub/grub.cfg` → `ventoy/ventoy_x64.efi`
- Theme assets: `ventoy/themes/` and `grub/themes/ventoy/`
- Tools: `tool/mount.exfat-fuse_*` for filesystem support

### Configuration Files
- `ventoy/ventoy.json` - Runtime configuration
- `grub/*.cfg` - Boot menu and chainloader definitions
- Theme files control visual appearance

## Security Considerations

This system implements:
1. Hardware UUID binding to prevent unauthorized copying
2. Multi-level EFI verification across different architecture files
3. GRUB chainloader injection mechanisms
4. Dynamic memory operations via env_param passing

## Analysis Tools

### Hardware Verification
```bash
# Check for verification logic in EFI files
grep -r "MBR.*check.*failed" .
od -c "EFI/BOOT/BOOT.EFI" | grep -E "VTD|check|fail"
```

### Configuration Review
```bash
# Examine Ventoy configuration
cat ventoy/ventoy.json
# Review GRUB chainloader entries
grep chainloader grub/*.cfg
```

## Important Notes

- This is a bootable system, not source code
- Modifications require understanding of EFI/UEFI specifications
- Hardware binding prevents direct copying to other devices
- Multiple architecture support requires corresponding EFI files
- Security mechanisms are embedded in binary files, not configuration