/*
 * Decompiled from: EFI/BOOT/BOOTX64.EFI
 * Generated by Advanced EFI Decompiler
 * This is pseudocode and may not compile directly
 */

#include <Uefi.h>
#include <Library/UefiLib.h>
#include <Library/UefiBootServicesTableLib.h>
#include <Library/UefiRuntimeServicesTableLib.h>

// Forward declarations
EFI_STATUS sub_00401ab1(VOID);
EFI_STATUS sub_004050fa(VOID);
EFI_STATUS sub_0040a881(VOID);
EFI_STATUS sub_0040a895(VOID);
EFI_STATUS sub_0040a8a9(VOID);
EFI_STATUS sub_0040ec80(VOID);
EFI_STATUS sub_0040ec80(VOID);

// Function at 0x00401ab1
// Calls: 4 functions
// Jumps: 0 locations
EFI_STATUS sub_00401ab1(VOID) {
    EFI_STATUS Status = EFI_SUCCESS;

    // 0x00401ab1: 55 push rbp
    // Function prologue
    // 0x00401ab2: 0e db 0x0e
    // db operation
    // 0x00401ab3: e0 db 0xe0
    // db operation
    // 0x00401ab4: 02 db 0x02
    // db operation
    // 0x00401ab5: 02 db 0x02
    // db operation
    // 0x00401ab6: 82 db 0x82
    // db operation
    // 0x00401ab7: 0e db 0x0e
    // db operation
    // 0x00401ab8: 80 db 0x80
    // db operation
    // 0x00401ab9: 03 db 0x03
    // db operation
    // 0x00401aba: 70 db 0x70
    // db operation
    // 0x00401abb: 0e db 0x0e
    // db operation
    // 0x00401abc: e0 db 0xe0
    // db operation
    // 0x00401abd: 02 db 0x02
    // db operation
    // 0x00401abe: 5D pop rbp
    // Function epilogue
    // 0x00401abf: 0e db 0x0e
    // db operation
    // 0x00401ac0: 80 db 0x80
    // db operation
    // 0x00401ac1: 03 db 0x03
    // db operation
    // 0x00401ac2: 71 db 0x71
    // db operation
    // 0x00401ac3: 0e db 0x0e
    // db operation
    // 0x00401ac4: e0 db 0xe0
    // db operation
    // 0x00401ac5: 02 db 0x02
    // db operation
    // 0x00401ac6: 50 push rax
    // Push rax
    // 0x00401ac7: 0e db 0x0e
    // db operation
    // 0x00401ac8: 80 db 0x80
    // db operation
    // 0x00401ac9: 03 db 0x03
    // db operation
    // 0x00401aca: 49 db 0x49
    // db operation
    // 0x00401acb: 0e db 0x0e
    // db operation
    // 0x00401acc: e0 db 0xe0
    // db operation
    // 0x00401acd: 02 db 0x02
    // db operation
    // 0x00401ace: 60 db 0x60
    // db operation

    return Status;
}

// Function at 0x004050fa
// Calls: 0 functions
// Jumps: 0 locations
EFI_STATUS sub_004050fa(VOID) {
    EFI_STATUS Status = EFI_SUCCESS;

    // 0x004050fa: 55 push rbp
    // Function prologue
    // 0x004050fb: C3 ret 
    return Status;

    return Status;
}

// Function at 0x0040a881
// Calls: 0 functions
// Jumps: 0 locations
EFI_STATUS sub_0040a881(VOID) {
    EFI_STATUS Status = EFI_SUCCESS;

    // 0x0040a881: 55 push rbp
    // Function prologue
    // 0x0040a882: 03 db 0x03
    // db operation
    // 0x0040a883: 00 db 0x00
    // db operation
    // 0x0040a884: 05 db 0x05
    // db operation
    // 0x0040a885: 00 db 0x00
    // db operation
    // 0x0040a886: 00 db 0x00
    // db operation
    // 0x0040a887: 00 db 0x00
    // db operation
    // 0x0040a888: 00 db 0x00
    // db operation
    // 0x0040a889: 00 db 0x00
    // db operation
    // 0x0040a88a: 00 db 0x00
    // db operation
    // 0x0040a88b: 00 db 0x00
    // db operation
    // 0x0040a88c: 10 db 0x10
    // db operation
    // 0x0040a88d: 00 db 0x00
    // db operation
    // 0x0040a88e: 00 db 0x00
    // db operation
    // 0x0040a88f: 00 db 0x00
    // db operation
    // 0x0040a890: 90 nop 
    // No operation
    // 0x0040a891: a4 db 0xa4
    // db operation
    // 0x0040a892: 00 db 0x00
    // db operation
    // 0x0040a893: 00 db 0x00
    // db operation
    // 0x0040a894: 10 db 0x10
    // db operation

    return Status;
}

// Function at 0x0040a895
// Calls: 0 functions
// Jumps: 0 locations
EFI_STATUS sub_0040a895(VOID) {
    EFI_STATUS Status = EFI_SUCCESS;

    // 0x0040a895: 55 push rbp
    // Function prologue
    // 0x0040a896: 03 db 0x03
    // db operation
    // 0x0040a897: 00 db 0x00
    // db operation
    // 0x0040a898: 19 db 0x19
    // db operation
    // 0x0040a899: 00 db 0x00
    // db operation
    // 0x0040a89a: 00 db 0x00
    // db operation
    // 0x0040a89b: 00 db 0x00
    // db operation
    // 0x0040a89c: 00 db 0x00
    // db operation
    // 0x0040a89d: 00 db 0x00
    // db operation
    // 0x0040a89e: 00 db 0x00
    // db operation
    // 0x0040a89f: 00 db 0x00
    // db operation
    // 0x0040a8a0: 10 db 0x10
    // db operation
    // 0x0040a8a1: 00 db 0x00
    // db operation
    // 0x0040a8a2: 00 db 0x00
    // db operation
    // 0x0040a8a3: 00 db 0x00
    // db operation
    // 0x0040a8a4: a4 db 0xa4
    // db operation
    // 0x0040a8a5: a4 db 0xa4
    // db operation
    // 0x0040a8a6: 00 db 0x00
    // db operation
    // 0x0040a8a7: 00 db 0x00
    // db operation
    // 0x0040a8a8: 15 db 0x15
    // db operation

    return Status;
}

// Function at 0x0040a8a9
// Calls: 0 functions
// Jumps: 0 locations
EFI_STATUS sub_0040a8a9(VOID) {
    EFI_STATUS Status = EFI_SUCCESS;

    // 0x0040a8a9: 55 push rbp
    // Function prologue
    // 0x0040a8aa: 03 db 0x03
    // db operation
    // 0x0040b758: 45 db 0x45
    // db operation
    // 0x0040b759: 0b db 0x0b
    // db operation
    // 0x0040b75a: 65 db 0x65
    // db operation
    // 0x0040b75b: 0e db 0x0e
    // db operation
    // 0x0040b75c: 28 db 0x28
    // db operation
    // 0x0040b75d: 43C3 ret 
    return Status;

    return Status;
}

// Function at 0x0040ec80
// Calls: 5 functions
// Jumps: 0 locations
EFI_STATUS sub_0040ec80(VOID) {
    EFI_STATUS Status = EFI_SUCCESS;

    // 0x0040ec80: 55 push rbp
    // Function prologue
    // 0x0040ec81: 0e db 0x0e
    // db operation
    // 0x0040ec82: 08 db 0x08
    // db operation
    // 0x0040ec83: 00 db 0x00
    // db operation
    // 0x0040ec84: 10 db 0x10
    // db operation
    // 0x0040ec85: 00 db 0x00
    // db operation
    // 0x0040ec86: 00 db 0x00
    // db operation
    // 0x0040ec87: 00 db 0x00
    // db operation
    // 0x0040ec88: 88 db 0x88
    // db operation
    // 0x0040ec89: E8000034DF call 0x-208b1372
    Status = sub_-208b1372();
    // 0x0040ec8e: 03 db 0x03
    // db operation
    // 0x0040ec8f: 00 db 0x00
    // db operation
    // 0x0040ec90: 05 db 0x05
    // db operation
    // 0x0040ec91: 00 db 0x00
    // db operation
    // 0x0040ec92: 00 db 0x00
    // db operation
    // 0x0040ec93: 00 db 0x00
    // db operation
    // 0x0040ec25: 00 db 0x00
    // db operation
    // 0x0040ec26: 00 db 0x00
    // db operation
    // 0x0040ec27: 00 db 0x00
    // db operation
    // 0x0040ec28: 00 db 0x00
    // db operation
    // 0x0040ec29: 00 db 0x00
    // db operation
    // 0x0040ec2a: 00 db 0x00
    // db operation
    // 0x0040ec2b: 00 db 0x00
    // db operation
    // 0x0040ec2c: 10 db 0x10
    // db operation
    // 0x0040ec2d: 00 db 0x00
    // db operation
    // 0x0040ec2e: 00 db 0x00
    // db operation
    // 0x0040ec2f: 00 db 0x00
    // db operation
    // 0x0040ec30: 30 db 0x30
    // db operation
    // 0x0040ec31: E8000043DF call 0x-207c13ca
    Status = sub_-207c13ca();
    // 0x0040ec36: 03 db 0x03
    // db operation

    return Status;
}

// Function at 0x0040ec80
// Calls: 2 functions
// Jumps: 0 locations
EFI_STATUS sub_0040ec80(VOID) {
    EFI_STATUS Status = EFI_SUCCESS;

    // 0x0040ec80: 55 push rbp
    // Function prologue
    // 0x0040ec81: 0e db 0x0e
    // db operation
    // 0x0040ec82: 08 db 0x08
    // db operation
    // 0x0040ec83: 00 db 0x00
    // db operation
    // 0x0040ec84: 10 db 0x10
    // db operation
    // 0x0040ec85: 00 db 0x00
    // db operation
    // 0x0040ec86: 00 db 0x00
    // db operation
    // 0x0040ec87: 00 db 0x00
    // db operation
    // 0x0040ec88: 88 db 0x88
    // db operation
    // 0x0040ec89: E8000034DF call 0x-208b1372
    Status = sub_-208b1372();
    // 0x0040ec8e: 03 db 0x03
    // db operation
    // 0x0040ec8f: 00 db 0x00
    // db operation
    // 0x0040ec90: 05 db 0x05
    // db operation
    // 0x0040ec91: 00 db 0x00
    // db operation
    // 0x0040ec92: 00 db 0x00
    // db operation
    // 0x0040ec93: 00 db 0x00
    // db operation
    // 0x0040ec94: 00 db 0x00
    // db operation
    // 0x0040ec95: 00 db 0x00
    // db operation
    // 0x0040ec96: 00 db 0x00
    // db operation
    // 0x0040ec97: 00 db 0x00
    // db operation
    // 0x0040ec98: 20 db 0x20
    // db operation
    // 0x0040ec99: 00 db 0x00
    // db operation
    // 0x0040ec9a: 00 db 0x00
    // db operation
    // 0x0040ec9b: 00 db 0x00
    // db operation
    // 0x0040ec9c: 9c db 0x9c
    // db operation
    // 0x0040ec9d: E8000025DF call 0x-209a135e
    Status = sub_-209a135e();
    // 0x0040eca2: 03 db 0x03
    // db operation
    // 0x0040eca3: 00 db 0x00
    // db operation

    return Status;
}

// Main entry point (assumed)
EFI_STATUS
EFIAPI
UefiMain (
  IN EFI_HANDLE        ImageHandle,
  IN EFI_SYSTEM_TABLE  *SystemTable
  ) {
    EFI_STATUS Status;

    // Initialize UEFI Library
    Status = UefiBootServicesTableLib->HandleProtocol(
               ImageHandle,
               &gEfiLoadedImageProtocolGuid,
               (VOID**)&LoadedImage
               );

    // Call main function
    Status = sub_00401ab1();

    return Status;
}