/**
 * VT-d Emulator - Intel Virtualization Technology for Directed I/O Emulator
 * 
 * This EFI application creates a fake ACPI DMAR table to emulate VT-d support
 * on systems that don't have hardware VT-d capabilities.
 * 
 * Based on reverse engineering of boot.efi DmarInsert function at 0x00004270
 */

#include <Uefi.h>
#include <Library/UefiLib.h>
#include <Library/UefiBootServicesTableLib.h>
#include <Library/MemoryAllocationLib.h>
#include <Library/BaseMemoryLib.h>
#include <Protocol/AcpiTable.h>

// ACPI DMAR Table Structures
#pragma pack(1)

typedef struct {
    UINT32 Signature;                  // "DMAR"
    UINT32 Length;                     // Table length
    UINT8  Revision;                   // Revision
    UINT8  Checksum;                   // Checksum
    UINT8  OEMID[6];                   // OEM ID
    UINT8  OEMTableID[8];              // OEM Table ID
    UINT32 OEMRevision;                // OEM Revision
    UINT32 CreatorID;                  // Creator ID
    UINT32 CreatorRevision;            // Creator Revision
    UINT8  HostAddressWidth;           // Host Address Width
    UINT8  Flags;                      // Flags
    UINT8  Reserved[10];               // Reserved
} ACPI_DMAR_HEADER;

typedef struct {
    UINT16 Type;                       // Entry type (0 = DRHD)
    UINT16 Length;                     // Entry length
    UINT8  Flags;                      // Flags
    UINT8  Reserved;                   // Reserved
    UINT16 Segment;                    // PCI Segment
    UINT64 RegisterBaseAddress;        // Register Base Address
} DRHD_ENTRY;

typedef struct {
    UINT16 Type;                       // Entry type (1 = RMRR)
    UINT16 Length;                     // Entry length
    UINT16 Reserved;                   // Reserved
    UINT16 Segment;                    // PCI Segment
    UINT64 ReservedMemoryRegionBaseAddress;   // Base Address
    UINT64 ReservedMemoryRegionLimitAddress;  // Limit Address
} RMRR_ENTRY;

typedef struct {
    UINT8  Type;                       // Device type
    UINT8  Length;                     // Length
    UINT16 Reserved;                   // Reserved
    UINT8  EnumerationID;              // Enumeration ID
    UINT8  StartBusNumber;             // Start Bus Number
} DEVICE_SCOPE_ENTRY;

#pragma pack()

// CPU Vendor Detection
#define CPU_VENDOR_INTEL    1
#define CPU_VENDOR_AMD      2
#define CPU_VENDOR_UNKNOWN  0

/**
 * Detect CPU vendor using CPUID instruction
 */
UINT32 DetectCpuVendor(VOID) {
    UINT32 Eax, Ebx, Ecx, Edx;
    CHAR8 VendorString[13];
    
    // Execute CPUID with EAX=0 to get vendor string
    AsmCpuid(0, &Eax, &Ebx, &Ecx, &Edx);
    
    // Construct vendor string
    *(UINT32*)&VendorString[0] = Ebx;
    *(UINT32*)&VendorString[4] = Edx;
    *(UINT32*)&VendorString[8] = Ecx;
    VendorString[12] = '\0';
    
    if (CompareMem(VendorString, "GenuineIntel", 12) == 0) {
        Print(L"Detected Intel CPU\n");
        return CPU_VENDOR_INTEL;
    } else if (CompareMem(VendorString, "AuthenticAMD", 12) == 0) {
        Print(L"Detected AMD CPU\n");
        return CPU_VENDOR_AMD;
    }
    
    Print(L"Unknown CPU vendor: %a\n", VendorString);
    return CPU_VENDOR_UNKNOWN;
}

/**
 * Calculate ACPI table checksum
 */
UINT8 CalculateAcpiChecksum(VOID *Table, UINT32 Length) {
    UINT8 *Bytes = (UINT8*)Table;
    UINT8 Checksum = 0;
    UINT32 i;
    
    for (i = 0; i < Length; i++) {
        Checksum += Bytes[i];
    }
    
    return (UINT8)(0x100 - Checksum);
}

/**
 * Build fake DMAR table based on CPU vendor
 */
EFI_STATUS BuildDmarTable(VOID **DmarTable, UINT32 *TableSize) {
    ACPI_DMAR_HEADER *Dmar;
    DRHD_ENTRY *Drhd;
    RMRR_ENTRY *Rmrr;
    DEVICE_SCOPE_ENTRY *DeviceScope;
    UINT32 CpuVendor;
    UINT32 TotalSize;
    UINT8 *CurrentPtr;
    
    CpuVendor = DetectCpuVendor();
    
    // Calculate total table size
    TotalSize = sizeof(ACPI_DMAR_HEADER) + 
                sizeof(DRHD_ENTRY) + 
                sizeof(DEVICE_SCOPE_ENTRY) +
                sizeof(RMRR_ENTRY) +
                sizeof(DEVICE_SCOPE_ENTRY);
    
    // Allocate memory for DMAR table
    Dmar = AllocatePool(TotalSize);
    if (Dmar == NULL) {
        Print(L"Failed to allocate memory for DMAR table\n");
        return EFI_OUT_OF_RESOURCES;
    }
    
    ZeroMem(Dmar, TotalSize);
    CurrentPtr = (UINT8*)Dmar;
    
    // Build DMAR header
    Dmar->Signature = SIGNATURE_32('D', 'M', 'A', 'R');
    Dmar->Length = TotalSize;
    Dmar->Revision = 1;
    Dmar->Checksum = 0; // Will be calculated later
    CopyMem(Dmar->OEMID, "INTEL ", 6);
    CopyMem(Dmar->OEMTableID, "VTDEMU  ", 8);
    Dmar->OEMRevision = 1;
    Dmar->CreatorID = SIGNATURE_32('V', 'T', 'D', 'E');
    Dmar->CreatorRevision = 1;
    Dmar->HostAddressWidth = 46; // 64-bit system standard
    Dmar->Flags = 0x01; // INTR_REMAP supported
    
    CurrentPtr += sizeof(ACPI_DMAR_HEADER);
    
    // Add DRHD entry (DMA Remapping Hardware Unit Definition)
    Drhd = (DRHD_ENTRY*)CurrentPtr;
    Drhd->Type = 0; // DRHD
    Drhd->Length = sizeof(DRHD_ENTRY) + sizeof(DEVICE_SCOPE_ENTRY);
    Drhd->Flags = 0x01; // INCLUDE_PCI_ALL
    Drhd->Segment = 0;
    
    // Set IOMMU register base address based on CPU vendor
    if (CpuVendor == CPU_VENDOR_INTEL) {
        Drhd->RegisterBaseAddress = 0xFED90000; // Intel standard IOMMU base
    } else if (CpuVendor == CPU_VENDOR_AMD) {
        Drhd->RegisterBaseAddress = 0xFED80000; // AMD IOMMU base
    } else {
        Drhd->RegisterBaseAddress = 0xFED90000; // Default to Intel
    }
    
    CurrentPtr += sizeof(DRHD_ENTRY);
    
    // Add device scope entry for DRHD
    DeviceScope = (DEVICE_SCOPE_ENTRY*)CurrentPtr;
    DeviceScope->Type = 1; // PCI Endpoint Device
    DeviceScope->Length = sizeof(DEVICE_SCOPE_ENTRY);
    DeviceScope->EnumerationID = 0;
    DeviceScope->StartBusNumber = 0;
    
    CurrentPtr += sizeof(DEVICE_SCOPE_ENTRY);
    
    // Add RMRR entry (Reserved Memory Region Reporting)
    Rmrr = (RMRR_ENTRY*)CurrentPtr;
    Rmrr->Type = 1; // RMRR
    Rmrr->Length = sizeof(RMRR_ENTRY) + sizeof(DEVICE_SCOPE_ENTRY);
    Rmrr->Segment = 0;
    Rmrr->ReservedMemoryRegionBaseAddress = 0x0; // Base of reserved region
    Rmrr->ReservedMemoryRegionLimitAddress = 0xFFFFF; // 1MB limit
    
    CurrentPtr += sizeof(RMRR_ENTRY);
    
    // Add device scope entry for RMRR
    DeviceScope = (DEVICE_SCOPE_ENTRY*)CurrentPtr;
    DeviceScope->Type = 1; // PCI Endpoint Device
    DeviceScope->Length = sizeof(DEVICE_SCOPE_ENTRY);
    DeviceScope->EnumerationID = 0;
    DeviceScope->StartBusNumber = 0;
    
    // Calculate and set checksum
    Dmar->Checksum = CalculateAcpiChecksum(Dmar, TotalSize);
    
    *DmarTable = Dmar;
    *TableSize = TotalSize;
    
    Print(L"DMAR table built successfully\n");
    Print(L"  Size: %d bytes\n", TotalSize);
    Print(L"  IOMMU Base: 0x%lx\n", Drhd->RegisterBaseAddress);
    Print(L"  Host Address Width: %d bits\n", Dmar->HostAddressWidth);
    
    return EFI_SUCCESS;
}

/**
 * Main entry point - DmarInsert equivalent
 */
EFI_STATUS EFIAPI UefiMain(
    IN EFI_HANDLE ImageHandle,
    IN EFI_SYSTEM_TABLE *SystemTable
) {
    EFI_STATUS Status;
    EFI_ACPI_TABLE_PROTOCOL *AcpiTable;
    VOID *DmarTable;
    UINT32 TableSize;
    UINTN TableKey;
    
    Print(L"\n=== VT-d Emulator v1.0 ===\n");
    Print(L"Emulating Intel VT-d support...\n\n");
    
    // Locate ACPI Table Protocol
    Status = gBS->LocateProtocol(
        &gEfiAcpiTableProtocolGuid,
        NULL,
        (VOID**)&AcpiTable
    );
    
    if (EFI_ERROR(Status)) {
        Print(L"ERROR: Failed to locate ACPI Table Protocol: %r\n", Status);
        Print(L"This system may not support ACPI table manipulation.\n");
        return Status;
    }
    
    Print(L"ACPI Table Protocol located successfully\n");
    
    // Build fake DMAR table
    Status = BuildDmarTable(&DmarTable, &TableSize);
    if (EFI_ERROR(Status)) {
        Print(L"ERROR: Failed to build DMAR table: %r\n", Status);
        return Status;
    }
    
    // Install DMAR table into system ACPI tables
    Status = AcpiTable->InstallAcpiTable(
        AcpiTable,
        DmarTable,
        TableSize,
        &TableKey
    );
    
    if (EFI_ERROR(Status)) {
        Print(L"ERROR: Failed to install DMAR table: %r\n", Status);
        FreePool(DmarTable);
        return Status;
    }
    
    Print(L"\n✓ SUCCESS: VT-d DMAR table installed!\n");
    Print(L"  Table Key: %d\n", TableKey);
    Print(L"  Table Size: %d bytes\n", TableSize);
    Print(L"\nThe system now appears to support Intel VT-d.\n");
    Print(L"Virtualization software should detect VT-d as available.\n");
    
    // Clean up
    FreePool(DmarTable);
    
    Print(L"\nPress any key to continue...\n");
    SystemTable->ConIn->Reset(SystemTable->ConIn, FALSE);
    
    EFI_INPUT_KEY Key;
    while (SystemTable->ConIn->ReadKeyStroke(SystemTable->ConIn, &Key) == EFI_NOT_READY) {
        gBS->Stall(10000); // Wait 10ms
    }
    
    return EFI_SUCCESS;
}
