## @file
#  VT-d Emulator EFI Application
#
#  This application creates a fake ACPI DMAR table to emulate VT-d support
#  on systems without hardware VT-d capabilities.
#
##

[Defines]
  INF_VERSION                    = 0x00010005
  BASE_NAME                      = VtdEmulator
  FILE_GUID                      = 12345678-1234-1234-1234-123456789ABC
  MODULE_TYPE                    = UEFI_APPLICATION
  VERSION_STRING                 = 1.0
  ENTRY_POINT                    = UefiMain

[Sources]
  VtdEmulator.c

[Packages]
  MdePkg/MdePkg.dec

[LibraryClasses]
  UefiApplicationEntryPoint
  UefiLib
  UefiBootServicesTableLib
  MemoryAllocationLib
  BaseMemoryLib
  BaseLib

[Protocols]
  gEfiAcpiTableProtocolGuid                     ## CONSUMES

[FeaturePcd]

[Pcd]

[UserExtensions.TianoCore."ExtraFiles"]
  VtdEmulatorExtra.uni
