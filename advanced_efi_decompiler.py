#!/usr/bin/env python3
"""
高级EFI文件反编译工具
Advanced EFI File Decompiler
"""

import os
import sys
import struct
import hashlib
import json
import re
from pathlib import Path
from typing import Dict, List, Any, Optional, Tuple

class AdvancedEFIDecompiler:
    """高级EFI文件反编译器"""
    
    def __init__(self, file_path: str):
        self.file_path = file_path
        self.file_data = None
        self.base_address = 0x400000  # 默认基址
        
    def load_file(self) -> bool:
        """加载EFI文件"""
        try:
            with open(self.file_path, 'rb') as f:
                self.file_data = f.read()
            return True
        except Exception as e:
            print(f"Error loading file {self.file_path}: {e}")
            return False
    
    def find_code_sections(self) -> List[Tuple[int, int, str]]:
        """查找代码段"""
        if not self.file_data:
            return []
        
        code_sections = []
        
        # 查找可能的代码模式
        # 1. 查找函数序言模式 (push rbp; mov rbp, rsp)
        prologue_pattern = rb'\x55\x48\x89\xe5'  # push rbp; mov rbp, rsp
        
        for match in re.finditer(prologue_pattern, self.file_data):
            offset = match.start()
            # 向前查找合理的函数开始
            start_offset = max(0, offset - 16)
            # 向后查找函数结束（ret指令）
            end_search = min(len(self.file_data), offset + 1000)
            
            for i in range(offset, end_search):
                if self.file_data[i:i+1] == b'\xc3':  # ret
                    code_sections.append((start_offset, i + 1, f"func_{offset:08x}"))
                    break
        
        # 2. 查找其他代码模式
        # 查找call指令模式
        call_pattern = rb'\xe8'  # call rel32
        for match in re.finditer(call_pattern, self.file_data):
            if match.start() + 5 <= len(self.file_data):
                offset = match.start()
                start_offset = max(0, offset - 32)
                end_offset = min(len(self.file_data), offset + 100)
                code_sections.append((start_offset, end_offset, f"call_site_{offset:08x}"))
        
        # 去重并排序
        unique_sections = []
        seen = set()
        for start, end, name in sorted(code_sections):
            key = (start, end)
            if key not in seen:
                seen.add(key)
                unique_sections.append((start, end, name))
        
        return unique_sections[:50]  # 限制数量
    
    def disassemble_x64_advanced(self, data: bytes, start_addr: int = 0) -> List[Dict[str, Any]]:
        """高级x64反汇编"""
        instructions = []
        offset = 0
        
        while offset < len(data) - 1:
            try:
                instr = self._decode_x64_instruction(data, offset, start_addr + offset)
                if instr:
                    instructions.append(instr)
                    offset += instr["size"]
                else:
                    # 未识别的字节
                    instructions.append({
                        "address": f"0x{start_addr + offset:08x}",
                        "bytes": f"{data[offset]:02x}",
                        "mnemonic": "db",
                        "operands": f"0x{data[offset]:02x}",
                        "size": 1,
                        "type": "data"
                    })
                    offset += 1
                    
                if len(instructions) >= 1000:  # 限制指令数量
                    break
                    
            except Exception as e:
                offset += 1
                continue
        
        return instructions
    
    def _decode_x64_instruction(self, data: bytes, offset: int, address: int) -> Optional[Dict[str, Any]]:
        """解码x64指令"""
        if offset >= len(data):
            return None
        
        byte = data[offset]
        
        # REX前缀处理
        rex_prefix = None
        if 0x40 <= byte <= 0x4F:
            rex_prefix = byte
            offset += 1
            if offset >= len(data):
                return None
            byte = data[offset]
        
        # 单字节指令
        single_byte_ops = {
            0x50: ("push", "rax", 1),
            0x51: ("push", "rcx", 1),
            0x52: ("push", "rdx", 1),
            0x53: ("push", "rbx", 1),
            0x54: ("push", "rsp", 1),
            0x55: ("push", "rbp", 1),
            0x56: ("push", "rsi", 1),
            0x57: ("push", "rdi", 1),
            0x58: ("pop", "rax", 1),
            0x59: ("pop", "rcx", 1),
            0x5A: ("pop", "rdx", 1),
            0x5B: ("pop", "rbx", 1),
            0x5C: ("pop", "rsp", 1),
            0x5D: ("pop", "rbp", 1),
            0x5E: ("pop", "rsi", 1),
            0x5F: ("pop", "rdi", 1),
            0x90: ("nop", "", 1),
            0xC3: ("ret", "", 1),
            0xCC: ("int3", "", 1),
            0xF4: ("hlt", "", 1),
            0xFA: ("cli", "", 1),
            0xFB: ("sti", "", 1),
        }
        
        if byte in single_byte_ops:
            mnemonic, operands, size = single_byte_ops[byte]
            total_size = size + (1 if rex_prefix else 0)
            
            return {
                "address": f"0x{address:08x}",
                "bytes": data[offset - (1 if rex_prefix else 0):offset + size].hex().upper(),
                "mnemonic": mnemonic,
                "operands": operands,
                "size": total_size,
                "type": "instruction",
                "rex": rex_prefix
            }
        
        # 多字节指令
        if byte == 0xE8 and offset + 4 < len(data):  # call rel32
            rel_offset = struct.unpack('<l', data[offset+1:offset+5])[0]
            target_addr = address + 5 + rel_offset
            total_size = 5 + (1 if rex_prefix else 0)
            
            return {
                "address": f"0x{address:08x}",
                "bytes": data[offset - (1 if rex_prefix else 0):offset + 5].hex().upper(),
                "mnemonic": "call",
                "operands": f"0x{target_addr:08x}",
                "size": total_size,
                "type": "call",
                "target": target_addr,
                "rex": rex_prefix
            }
        
        if byte == 0xE9 and offset + 4 < len(data):  # jmp rel32
            rel_offset = struct.unpack('<l', data[offset+1:offset+5])[0]
            target_addr = address + 5 + rel_offset
            total_size = 5 + (1 if rex_prefix else 0)
            
            return {
                "address": f"0x{address:08x}",
                "bytes": data[offset - (1 if rex_prefix else 0):offset + 5].hex().upper(),
                "mnemonic": "jmp",
                "operands": f"0x{target_addr:08x}",
                "size": total_size,
                "type": "jump",
                "target": target_addr,
                "rex": rex_prefix
            }
        
        if byte == 0xEB and offset + 1 < len(data):  # jmp rel8
            rel_offset = struct.unpack('<b', data[offset+1:offset+2])[0]
            target_addr = address + 2 + rel_offset
            total_size = 2 + (1 if rex_prefix else 0)
            
            return {
                "address": f"0x{address:08x}",
                "bytes": data[offset - (1 if rex_prefix else 0):offset + 2].hex().upper(),
                "mnemonic": "jmp",
                "operands": f"0x{target_addr:08x}",
                "size": total_size,
                "type": "jump",
                "target": target_addr,
                "rex": rex_prefix
            }
        
        # MOV指令（简化版本）
        if byte == 0x89 and offset + 2 < len(data):  # mov r/m32, r32
            modrm = data[offset + 1]
            total_size = 2 + (1 if rex_prefix else 0)
            
            return {
                "address": f"0x{address:08x}",
                "bytes": data[offset - (1 if rex_prefix else 0):offset + 2].hex().upper(),
                "mnemonic": "mov",
                "operands": f"[ModRM: 0x{modrm:02x}]",
                "size": total_size,
                "type": "instruction",
                "rex": rex_prefix
            }
        
        if byte == 0x8B and offset + 2 < len(data):  # mov r32, r/m32
            modrm = data[offset + 1]
            total_size = 2 + (1 if rex_prefix else 0)
            
            return {
                "address": f"0x{address:08x}",
                "bytes": data[offset - (1 if rex_prefix else 0):offset + 2].hex().upper(),
                "mnemonic": "mov",
                "operands": f"[ModRM: 0x{modrm:02x}]",
                "size": total_size,
                "type": "instruction",
                "rex": rex_prefix
            }
        
        return None
    
    def analyze_control_flow(self, instructions: List[Dict[str, Any]]) -> Dict[str, Any]:
        """分析控制流"""
        functions = []
        current_function = None
        call_targets = set()
        jump_targets = set()
        
        for instr in instructions:
            # 收集调用和跳转目标
            if instr["type"] == "call" and "target" in instr:
                call_targets.add(instr["target"])
            elif instr["type"] == "jump" and "target" in instr:
                jump_targets.add(instr["target"])
            
            # 检测函数开始
            if instr["mnemonic"] == "push" and instr["operands"] == "rbp":
                if current_function:
                    functions.append(current_function)
                
                current_function = {
                    "start_address": instr["address"],
                    "instructions": [],
                    "calls": [],
                    "jumps": [],
                    "local_labels": set()
                }
            
            if current_function:
                current_function["instructions"].append(instr)
                
                if instr["type"] == "call":
                    current_function["calls"].append(instr.get("target", "unknown"))
                elif instr["type"] == "jump":
                    current_function["jumps"].append(instr.get("target", "unknown"))
                
                # 函数结束
                if instr["mnemonic"] == "ret":
                    current_function["end_address"] = instr["address"]
                    functions.append(current_function)
                    current_function = None
        
        return {
            "functions": functions,
            "call_targets": sorted(call_targets),
            "jump_targets": sorted(jump_targets),
            "total_functions": len(functions)
        }
    
    def generate_c_code(self, control_flow: Dict[str, Any]) -> str:
        """生成C代码"""
        c_lines = []
        
        # 头文件
        c_lines.extend([
            "/*",
            f" * Decompiled from: {self.file_path}",
            f" * Generated by Advanced EFI Decompiler",
            " * This is pseudocode and may not compile directly",
            " */",
            "",
            "#include <Uefi.h>",
            "#include <Library/UefiLib.h>",
            "#include <Library/UefiBootServicesTableLib.h>",
            "#include <Library/UefiRuntimeServicesTableLib.h>",
            "",
            "// Forward declarations",
        ])
        
        # 函数声明
        for i, func in enumerate(control_flow["functions"][:20]):  # 限制函数数量
            func_name = f"sub_{func['start_address'].replace('0x', '')}"
            c_lines.append(f"EFI_STATUS {func_name}(VOID);")
        
        c_lines.append("")
        
        # 函数实现
        for i, func in enumerate(control_flow["functions"][:10]):  # 限制实现数量
            func_name = f"sub_{func['start_address'].replace('0x', '')}"
            
            c_lines.extend([
                f"// Function at {func['start_address']}",
                f"// Calls: {len(func['calls'])} functions",
                f"// Jumps: {len(func['jumps'])} locations",
                f"EFI_STATUS {func_name}(VOID) {{",
                "    EFI_STATUS Status = EFI_SUCCESS;",
                ""
            ])
            
            # 分析指令并生成伪代码
            for j, instr in enumerate(func["instructions"][:30]):  # 限制指令数量
                comment = f"    // {instr['address']}: {instr['bytes']} {instr['mnemonic']} {instr['operands']}"
                c_lines.append(comment)
                
                # 指令转换为C代码
                if instr["mnemonic"] == "push":
                    if instr["operands"] == "rbp":
                        c_lines.append("    // Function prologue")
                    else:
                        c_lines.append(f"    // Push {instr['operands']}")
                
                elif instr["mnemonic"] == "pop":
                    if instr["operands"] == "rbp":
                        c_lines.append("    // Function epilogue")
                    else:
                        c_lines.append(f"    // Pop {instr['operands']}")
                
                elif instr["mnemonic"] == "call":
                    target = instr.get("target", "unknown")
                    if isinstance(target, int):
                        c_lines.append(f"    Status = sub_{target:08x}();")
                    else:
                        c_lines.append(f"    // Call to {target}")
                
                elif instr["mnemonic"] == "mov":
                    c_lines.append("    // Data movement operation")
                
                elif instr["mnemonic"] == "jmp":
                    target = instr.get("target", "unknown")
                    c_lines.append(f"    goto label_{target};")
                
                elif instr["mnemonic"] == "ret":
                    c_lines.append("    return Status;")
                    break
                
                elif instr["mnemonic"] == "nop":
                    c_lines.append("    // No operation")
                
                else:
                    c_lines.append(f"    // {instr['mnemonic']} operation")
            
            c_lines.extend([
                "",
                "    return Status;",
                "}",
                ""
            ])
        
        # 主入口点（如果找到）
        c_lines.extend([
            "// Main entry point (assumed)",
            "EFI_STATUS",
            "EFIAPI",
            "UefiMain (",
            "  IN EFI_HANDLE        ImageHandle,",
            "  IN EFI_SYSTEM_TABLE  *SystemTable",
            "  ) {",
            "    EFI_STATUS Status;",
            "",
            "    // Initialize UEFI Library",
            "    Status = UefiBootServicesTableLib->HandleProtocol(",
            "               ImageHandle,",
            "               &gEfiLoadedImageProtocolGuid,",
            "               (VOID**)&LoadedImage",
            "               );",
            "",
            "    // Call main function",
            f"    Status = sub_{control_flow['functions'][0]['start_address'].replace('0x', '')}();" if control_flow['functions'] else "    // No functions found",
            "",
            "    return Status;",
            "}"
        ])
        
        return "\n".join(c_lines)
    
    def full_decompile(self) -> Dict[str, Any]:
        """完整反编译"""
        if not self.load_file():
            return {"error": "Failed to load file"}
        
        print(f"开始反编译: {self.file_path}")
        
        # 查找代码段
        code_sections = self.find_code_sections()
        print(f"发现 {len(code_sections)} 个代码段")
        
        all_instructions = []
        section_analysis = {}
        
        # 反汇编每个代码段
        for start, end, name in code_sections:
            print(f"反汇编代码段: {name} (0x{start:08x} - 0x{end:08x})")
            
            code_data = self.file_data[start:end]
            instructions = self.disassemble_x64_advanced(code_data, self.base_address + start)
            
            section_analysis[name] = {
                "start_offset": start,
                "end_offset": end,
                "size": end - start,
                "instructions": instructions
            }
            
            all_instructions.extend(instructions)
        
        # 分析控制流
        print("分析控制流...")
        control_flow = self.analyze_control_flow(all_instructions)
        
        # 生成C代码
        print("生成C伪代码...")
        c_code = self.generate_c_code(control_flow)
        
        return {
            "file_path": self.file_path,
            "file_size": len(self.file_data),
            "code_sections": section_analysis,
            "control_flow": control_flow,
            "c_pseudocode": c_code,
            "statistics": {
                "total_instructions": len(all_instructions),
                "total_functions": control_flow["total_functions"],
                "code_sections": len(code_sections)
            }
        }

def main():
    """主函数"""
    print("=== 高级EFI文件反编译工具 ===")
    print("=== Advanced EFI File Decompiler ===\n")
    
    target_file = "EFI/BOOT/BOOTX64.EFI"
    
    if not os.path.exists(target_file):
        print(f"文件不存在: {target_file}")
        return
    
    decompiler = AdvancedEFIDecompiler(target_file)
    result = decompiler.full_decompile()
    
    if "error" in result:
        print(f"反编译失败: {result['error']}")
        return
    
    # 保存结果
    base_name = target_file.replace('/', '_').replace('.', '_')
    
    # 保存完整分析结果
    json_file = f"{base_name}_decompiled.json"
    with open(json_file, 'w', encoding='utf-8') as f:
        # 移除C代码以减小JSON文件大小，并转换set为list
        result_copy = result.copy()
        result_copy.pop('c_pseudocode', None)

        # 转换set为list以支持JSON序列化
        if 'control_flow' in result_copy:
            cf = result_copy['control_flow']
            if 'call_targets' in cf:
                cf['call_targets'] = list(cf['call_targets'])
            if 'jump_targets' in cf:
                cf['jump_targets'] = list(cf['jump_targets'])

            for func in cf.get('functions', []):
                if 'local_labels' in func:
                    func['local_labels'] = list(func['local_labels'])

        json.dump(result_copy, f, indent=2, ensure_ascii=False)
    
    # 保存C伪代码
    c_file = f"{base_name}_decompiled.c"
    with open(c_file, 'w', encoding='utf-8') as f:
        f.write(result["c_pseudocode"])
    
    # 保存反汇编列表
    asm_file = f"{base_name}_disassembly.txt"
    with open(asm_file, 'w', encoding='utf-8') as f:
        f.write(f"Disassembly of {target_file}\n")
        f.write("=" * 60 + "\n\n")
        
        for section_name, section_data in result["code_sections"].items():
            f.write(f"Section: {section_name}\n")
            f.write(f"Offset: 0x{section_data['start_offset']:08x} - 0x{section_data['end_offset']:08x}\n")
            f.write(f"Size: {section_data['size']} bytes\n")
            f.write("-" * 40 + "\n")
            
            for instr in section_data["instructions"][:100]:  # 限制输出
                f.write(f"{instr['address']}: {instr['bytes']:<20} {instr['mnemonic']:<8} {instr['operands']}\n")
            
            f.write("\n")
    
    print(f"\n反编译完成!")
    print(f"统计信息:")
    print(f"  - 总指令数: {result['statistics']['total_instructions']}")
    print(f"  - 发现函数: {result['statistics']['total_functions']}")
    print(f"  - 代码段数: {result['statistics']['code_sections']}")
    print(f"\n输出文件:")
    print(f"  - JSON分析: {json_file}")
    print(f"  - C伪代码: {c_file}")
    print(f"  - 反汇编: {asm_file}")

if __name__ == "__main__":
    main()
