#!/usr/bin/env python3

def analyze_boot_efi_verification(filename):
    """深入分析BOOT.EFI中的验证逻辑"""
    
    try:
        with open(filename, 'rb') as f:
            data = f.read()
            
            print("=== BOOT.EFI验证系统分析 ===\n")
            
            # 1. 搜索验证相关的字符串
            verification_patterns = [
                b"check",
                b"verify", 
                b"valid",
                b"fail",
                b"error",
                b"MBR",
                b"disk",
                b"device",
                b"USB",
                b"VID",
                b"PID",
                b"hardware"
            ]
            
            print("1. 搜索验证相关字符串:")
            for pattern in verification_patterns:
                pos = data.find(pattern)
                if pos != -1:
                    print(f"  找到 '{pattern.decode('ascii', errors='ignore')}' @ 0x{pos:04X}")
                    # 显示上下文
                    start = max(0, pos - 30)
                    end = min(len(data), pos + len(pattern) + 30)
                    context = data[start:end]
                    ascii_context = ''.join(chr(b) if 32 <= b <= 126 else '.' for b in context)
                    print(f"    上下文: {ascii_context}")
            
            # 2. 搜索可能的错误代码和返回值
            print(f"\n2. 搜索可能的错误代码:")
            
            # 搜索数字12作为返回值
            for i in range(len(data) - 4):
                # mov eax, 12 (B8 0C 00 00 00)
                if data[i:i+5] == b'\xB8\x0C\x00\x00\x00':
                    print(f"  找到 'mov eax, 12' @ 0x{i:04X}")
                    context = data[i-10:i+15]
                    print(f"    上下文: {context.hex().upper()}")
                
                # mov rax, 12 (48 C7 C0 0C 00 00 00)
                if data[i:i+7] == b'\x48\xC7\xC0\x0C\x00\x00\x00':
                    print(f"  找到 'mov rax, 12' @ 0x{i:04X}")
                    context = data[i-10:i+17]
                    print(f"    上下文: {context.hex().upper()}")
            
            # 3. 搜索函数调用模式
            print(f"\n3. 搜索函数调用模式:")
            
            # 搜索call指令 (E8 XX XX XX XX)
            call_count = 0
            for i in range(len(data) - 5):
                if data[i] == 0xE8:  # call near
                    call_count += 1
                    if call_count <= 10:  # 只显示前10个
                        offset = int.from_bytes(data[i+1:i+5], 'little', signed=True)
                        target = (i + 5 + offset) & 0xFFFFFFFF
                        print(f"  call @ 0x{i:04X} -> 0x{target:04X}")
            
            print(f"  总共找到 {call_count} 个call指令")
            
            # 4. 搜索特定的硬件检查函数
            print(f"\n4. 搜索特定的硬件相关代码:")
            
            # 搜索可能的USB VID/PID检查
            usb_patterns = [
                b"General_UDisk",
                b"Silicon-Power", 
                b"UFD_2.0"
            ]
            
            for pattern in usb_patterns:
                pos = data.find(pattern)
                if pos != -1:
                    print(f"  找到硬件字符串 '{pattern.decode('ascii', errors='ignore')}' @ 0x{pos:04X}")
                    
            # 5. 搜索环境变量设置
            print(f"\n5. 搜索可能的环境变量或标志设置:")
            env_patterns = [
                b"VTOY_",
                b"VENTOY_", 
                b"VT_"
            ]
            
            for pattern in env_patterns:
                pos = data.find(pattern)
                if pos != -1:
                    print(f"  找到环境变量 '{pattern.decode('ascii', errors='ignore')}' @ 0x{pos:04X}")
                    start = max(0, pos - 20)
                    end = min(len(data), pos + 50)
                    context = data[start:end]
                    ascii_context = ''.join(chr(b) if 32 <= b <= 126 else '.' for b in context)
                    print(f"    上下文: {ascii_context}")
            
            print(f"\n=== BOOT.EFI分析完成 ===")
            
    except FileNotFoundError:
        print(f"文件未找到: {filename}")
    except Exception as e:
        print(f"分析过程中出错: {e}")

if __name__ == "__main__":
    filename = r"D:\新建文件夹 (2)\新建文件夹\EFI\BOOT\BOOT.EFI"
    analyze_boot_efi_verification(filename)