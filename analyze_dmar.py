#!/usr/bin/env python3

with open('EFI/BOOT/BOOT.EFI', 'rb') as f:
    data = f.read()

# 搜索DMAR签名
dmar_pos = data.find(b'DMAR')
count = 0
while dmar_pos != -1 and count < 10:  # 限制搜索次数
    print(f'DMAR found at offset: 0x{dmar_pos:x}')
    
    if dmar_pos + 36 <= len(data):
        # 尝试解析ACPI表头
        header = data[dmar_pos:dmar_pos+36]
        if len(header) >= 36:
            signature = header[0:4]
            length = int.from_bytes(header[4:8], byteorder='little')
            revision = header[8]
            checksum = header[9]
            oemid = header[10:16]
            
            print(f'  Signature: {signature}')
            print(f'  Length: {length}')
            print(f'  Revision: {revision}')
            print(f'  Checksum: 0x{checksum:02x}')
            print(f'  OEM ID: {oemid}')
            print()
            
            # 显示前64字节的原始数据
            print(f'Raw data (first 64 bytes):')
            raw_data = data[dmar_pos:dmar_pos+64]
            for i in range(0, len(raw_data), 16):
                hex_bytes = ' '.join(f'{b:02x}' for b in raw_data[i:i+16])
                ascii_repr = ''.join(chr(b) if 32 <= b <= 126 else '.' for b in raw_data[i:i+16])
                print(f'{dmar_pos+i:08x}: {hex_bytes:<48} {ascii_repr}')
            print()
    
    # 搜索下一个DMAR
    dmar_pos = data.find(b'DMAR', dmar_pos + 1)
    count += 1

print("Analysis complete.")