#!/usr/bin/env python3

def analyze_grub_verification(filename):
    """深入分析grubx64_real.efi中的验证逻辑"""
    
    try:
        with open(filename, 'rb') as f:
            data = f.read()
            
            print("分析GRUB验证逻辑...\n")
            
            # 找到错误消息的位置
            error_msg = b"This is NOT a standard Ventoy device"
            pos = data.find(error_msg)
            
            if pos != -1:
                print(f"找到错误消息位置: 0x{pos:04X}")
                
                # 向前搜索可能的验证函数开始
                # 寻找函数序言模式
                search_start = max(0, pos - 1000)
                search_end = pos
                
                print("\n搜索验证函数的可能位置:")
                for i in range(search_start, search_end, 4):
                    # 寻找函数开始的模式
                    chunk = data[i:i+8]
                    # x86-64 函数序言通常是: push rbp; mov rbp, rsp
                    if chunk[:3] == b'\x55\x48\x89' or chunk[:2] == b'\x48\x83':
                        print(f"可能的函数开始: 0x{i:04X}, 字节: {chunk.hex().upper()}")
                
                # 搜索字符串引用
                print(f"\n搜索错误消息前的代码:")
                context_start = max(0, pos - 200)
                context = data[context_start:pos + 50]
                print(f"上下文 (0x{context_start:04X} - 0x{pos+50:04X}):")
                
                # 以16字节为一行显示
                for i in range(0, len(context), 16):
                    offset = context_start + i
                    line = context[i:i+16]
                    hex_str = ' '.join(f'{b:02x}' for b in line)
                    ascii_str = ''.join(chr(b) if 32 <= b <= 126 else '.' for b in line)
                    print(f"{offset:08x}: {hex_str:<48} |{ascii_str}|")
            
            # 搜索可能的硬件检查函数
            print(f"\n搜索硬件相关的字符串:")
            hardware_patterns = [
                b"General_UDisk",
                b"Silicon-Power", 
                b"VID_",
                b"PID_",
                b"USB\\",
                b"USBSTOR",
                b"vendor",
                b"product"
            ]
            
            for pattern in hardware_patterns:
                pos = data.find(pattern)
                if pos != -1:
                    print(f"找到 '{pattern.decode('utf-8', errors='ignore')}' 位置: 0x{pos:04X}")
                    # 显示上下文
                    start = max(0, pos - 30)
                    end = min(len(data), pos + len(pattern) + 30)
                    context = data[start:end]
                    print(f"上下文: {context}")
                    print("-" * 60)
            
            # 搜索数字12的相关代码
            print(f"\n搜索错误代码12相关的比较指令:")
            for i in range(len(data) - 10):
                # 搜索可能的比较指令
                if data[i:i+2] == b'\x83\xf8' and data[i+2] == 0x0c:  # cmp eax, 12
                    print(f"找到 'cmp eax, 12' 在 0x{i:04X}")
                    context = data[i-10:i+20]
                    print(f"上下文: {context.hex().upper()}")
                    
                elif data[i:i+3] == b'\x48\x83\xf8' and data[i+3] == 0x0c:  # cmp rax, 12
                    print(f"找到 'cmp rax, 12' 在 0x{i:04X}")
                    context = data[i-10:i+20]
                    print(f"上下文: {context.hex().upper()}")
            
            print("\n分析完成")
            
    except FileNotFoundError:
        print(f"文件未找到: {filename}")
    except Exception as e:
        print(f"分析过程中出错: {e}")

if __name__ == "__main__":
    filename = r"D:\新建文件夹 (2)\新建文件夹\EFI\BOOT\grubx64_real.efi"
    analyze_grub_verification(filename)