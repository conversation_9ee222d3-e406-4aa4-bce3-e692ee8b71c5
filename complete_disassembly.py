#!/usr/bin/env python3
import struct

def complete_disassembly(filename):
    """对BOOT.EFI进行完整的反汇编分析"""
    
    try:
        with open(filename, 'rb') as f:
            data = f.read()
            
        # 创建输出文件
        with open("boot_efi_complete_disassembly.txt", "w", encoding="utf-8") as output:
            
            output.write("=== BOOT.EFI 完整反汇编分析 ===\n")
            output.write(f"文件大小: {len(data)} 字节\n\n")
            
            # 1. PE文件头分析
            output.write("=== PE文件头分析 ===\n")
            if data[:2] == b'MZ':
                pe_offset = struct.unpack('<I', data[0x3C:0x40])[0]
                output.write(f"PE头偏移: 0x{pe_offset:04X}\n")
                
                if pe_offset < len(data) - 24 and data[pe_offset:pe_offset+4] == b'PE\x00\x00':
                    machine = struct.unpack('<H', data[pe_offset+4:pe_offset+6])[0]
                    sections = struct.unpack('<H', data[pe_offset+6:pe_offset+8])[0]
                    timestamp = struct.unpack('<I', data[pe_offset+8:pe_offset+12])[0]
                    characteristics = struct.unpack('<H', data[pe_offset+22:pe_offset+24])[0]
                    
                    output.write(f"机器类型: 0x{machine:04X} (AMD64)\n")
                    output.write(f"节数量: {sections}\n")
                    output.write(f"时间戳: 0x{timestamp:08X}\n")
                    output.write(f"特征: 0x{characteristics:04X}\n\n")
                    
                    # 分析节表
                    optional_header_size = struct.unpack('<H', data[pe_offset+20:pe_offset+22])[0]
                    section_table_offset = pe_offset + 24 + optional_header_size
                    
                    output.write("节表信息:\n")
                    for i in range(sections):
                        section_offset = section_table_offset + i * 40
                        if section_offset + 40 <= len(data):
                            section_name = data[section_offset:section_offset+8].rstrip(b'\x00').decode('ascii', errors='ignore')
                            virtual_size = struct.unpack('<I', data[section_offset+8:section_offset+12])[0]
                            virtual_addr = struct.unpack('<I', data[section_offset+12:section_offset+16])[0]
                            raw_size = struct.unpack('<I', data[section_offset+16:section_offset+20])[0]
                            raw_addr = struct.unpack('<I', data[section_offset+20:section_offset+24])[0]
                            characteristics = struct.unpack('<I', data[section_offset+36:section_offset+40])[0]
                            
                            output.write(f"  {section_name}: VA=0x{virtual_addr:08X}, Size=0x{virtual_size:08X}, Raw=0x{raw_addr:08X}, Chars=0x{characteristics:08X}\n")
            
            # 2. 完整的十六进制dump
            output.write("\n=== 完整十六进制内容 ===\n")
            for i in range(0, len(data), 16):
                line = data[i:i+16]
                hex_str = ' '.join(f'{b:02x}' for b in line)
                ascii_str = ''.join(chr(b) if 32 <= b <= 126 else '.' for b in line)
                output.write(f"{i:08x}: {hex_str:<48} |{ascii_str}|\n")
            
            # 3. 提取所有字符串
            output.write("\n=== 所有字符串提取 ===\n")
            strings = []
            current_string = ""
            start_pos = 0
            
            for i, byte in enumerate(data):
                if 32 <= byte <= 126:  # 可打印ASCII
                    if not current_string:
                        start_pos = i
                    current_string += chr(byte)
                else:
                    if len(current_string) >= 4:
                        strings.append((start_pos, current_string))
                    current_string = ""
            
            if len(current_string) >= 4:
                strings.append((len(data) - len(current_string), current_string))
            
            for offset, string in strings:
                output.write(f"0x{offset:08X}: {string}\n")
            
            # 4. 指令分析
            output.write("\n=== 指令序列分析 ===\n")
            
            # 查找所有可能的指令
            instructions = []
            
            for i in range(len(data) - 10):
                # x86-64常见指令模式
                byte1 = data[i]
                
                # CALL指令
                if byte1 == 0xE8 and i + 5 <= len(data):
                    offset = struct.unpack('<i', data[i+1:i+5])[0]
                    target = (i + 5 + offset) & 0xFFFFFFFF
                    instructions.append((i, f"call 0x{target:08X}", data[i:i+5]))
                
                # JMP指令
                elif byte1 == 0xE9 and i + 5 <= len(data):
                    offset = struct.unpack('<i', data[i+1:i+5])[0]
                    target = (i + 5 + offset) & 0xFFFFFFFF
                    instructions.append((i, f"jmp 0x{target:08X}", data[i:i+5]))
                
                # 条件跳转指令
                elif byte1 == 0x0F and i + 6 <= len(data):
                    byte2 = data[i+1]
                    if 0x80 <= byte2 <= 0x8F:  # 条件跳转
                        offset = struct.unpack('<i', data[i+2:i+6])[0]
                        target = (i + 6 + offset) & 0xFFFFFFFF
                        jmp_types = {0x84: "je", 0x85: "jne", 0x86: "jbe", 0x87: "ja", 
                                   0x8C: "jl", 0x8D: "jge", 0x8E: "jle", 0x8F: "jg"}
                        jmp_name = jmp_types.get(byte2, f"j{byte2:02X}")
                        instructions.append((i, f"{jmp_name} 0x{target:08X}", data[i:i+6]))
                
                # MOV指令
                elif byte1 == 0xB8 and i + 5 <= len(data):  # mov eax, imm32
                    imm32 = struct.unpack('<I', data[i+1:i+5])[0]
                    instructions.append((i, f"mov eax, 0x{imm32:08X}", data[i:i+5]))
                
                elif byte1 == 0x48 and i + 7 <= len(data):
                    if data[i+1:i+3] == b'\xC7\xC0':  # mov rax, imm32
                        imm32 = struct.unpack('<I', data[i+3:i+7])[0]
                        instructions.append((i, f"mov rax, 0x{imm32:08X}", data[i:i+7]))
                
                # TEST指令
                elif byte1 == 0x85 and i + 2 <= len(data):
                    byte2 = data[i+1]
                    if byte2 == 0xC0:  # test eax, eax
                        instructions.append((i, "test eax, eax", data[i:i+2]))
                
                elif byte1 == 0x48 and i + 3 <= len(data):
                    if data[i+1:i+3] == b'\x85\xC0':  # test rax, rax
                        instructions.append((i, "test rax, rax", data[i:i+3]))
                
                # CMP指令
                elif byte1 == 0x83 and i + 3 <= len(data):
                    if data[i+1] == 0xF8:  # cmp eax, imm8
                        imm8 = data[i+2]
                        instructions.append((i, f"cmp eax, 0x{imm8:02X}", data[i:i+3]))
                
                # PUSH/POP指令
                elif byte1 == 0x55:  # push rbp
                    instructions.append((i, "push rbp", data[i:i+1]))
                elif byte1 == 0x5D:  # pop rbp
                    instructions.append((i, "pop rbp", data[i:i+1]))
                
                # RET指令
                elif byte1 == 0xC3:  # ret
                    instructions.append((i, "ret", data[i:i+1]))
            
            # 输出指令
            for addr, instr, raw_bytes in instructions[:500]:  # 限制输出数量
                hex_bytes = ' '.join(f'{b:02x}' for b in raw_bytes)
                output.write(f"0x{addr:08X}: {hex_bytes:<20} {instr}\n")
            
            # 5. 函数分析
            output.write("\n=== 函数边界分析 ===\n")
            
            functions = []
            
            for i in range(len(data) - 10):
                # 寻找函数序言
                if (data[i:i+3] == b'\x55\x48\x89' or      # push rbp; mov rbp, rsp
                    data[i:i+4] == b'\x48\x83\xEC' or      # sub rsp, imm
                    data[i:i+3] == b'\x48\x89'):           # mov rbp, rsp (变体)
                    
                    # 寻找对应的函数结尾
                    func_end = None
                    for j in range(i + 10, min(i + 1000, len(data))):
                        if data[j] == 0xC3:  # ret指令
                            func_end = j
                            break
                    
                    if func_end:
                        functions.append((i, func_end, func_end - i))
            
            output.write(f"发现 {len(functions)} 个可能的函数:\n")
            for start, end, size in functions[:50]:  # 限制输出
                output.write(f"0x{start:08X} - 0x{end:08X} (大小: {size} 字节)\n")
            
            # 6. 重要数据结构
            output.write("\n=== 重要数据结构 ===\n")
            
            # GUID结构 (16字节)
            guids = []
            for i in range(0, len(data) - 16, 4):
                # 简单的GUID检测 (前4字节不为0，且有一定的模式)
                chunk = data[i:i+16]
                if (chunk[0] != 0 and chunk[4] != 0 and chunk[8] != 0 and 
                    chunk[12] != 0 and chunk != b'\xFF' * 16):
                    # 检查是否看起来像GUID
                    guid_str = '-'.join([
                        chunk[:4].hex().upper(),
                        chunk[4:6].hex().upper(),
                        chunk[6:8].hex().upper(),
                        chunk[8:10].hex().upper(),
                        chunk[10:16].hex().upper()
                    ])
                    guids.append((i, guid_str))
            
            for offset, guid in guids[:20]:  # 限制输出
                output.write(f"0x{offset:08X}: {guid}\n")
            
            output.write("\n=== 反汇编完成 ===\n")
            output.write(f"总计: {len(strings)} 个字符串, {len(instructions)} 条指令, {len(functions)} 个函数\n")
        
        print("完整反汇编已保存到 boot_efi_complete_disassembly.txt")
        print(f"文件大小: {len(data)} 字节")
        print(f"发现: {len(strings)} 个字符串")
        print("反汇编完成！")
        
    except FileNotFoundError:
        print(f"文件未找到: {filename}")
    except Exception as e:
        print(f"反汇编过程中出错: {e}")

if __name__ == "__main__":
    filename = r"D:\新建文件夹 (2)\新建文件夹\EFI\BOOT\BOOT.EFI"
    complete_disassembly(filename)