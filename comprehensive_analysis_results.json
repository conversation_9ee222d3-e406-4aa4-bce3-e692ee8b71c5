{"efi_analysis": {"EFI/BOOT/BOOTX64.EFI": {"pe_structure": {"file_size": 965672, "file_hash": "a60d256c802849a0a5e23fe5298ddcf7f78445cc71f519b64573dcb61af0e6ff", "pe_signature": "0x4550", "machine_type": "0x8664", "sections": [], "entry_point": null, "subsystem": null, "error": "COFF header parsing error: unpack requires a buffer of 16 bytes", "number_of_sections": 10}, "strings": ["!This program cannot be run in DOS mode.", "@.text", "`.reloc", "B/14", "@.data", "@.dynamic", ".rela", "@.sbat", "YZQR", "~CfB", "AWAVE1", "AUATUSH", "L$0L", "D$ H", "D$@H", "D$HH", "D$XH", "D$8H", "H;D$", "t$ L", "ATPL", "t$XH", "t$81", "$tg1", "|$(H", "t$8H", "T$HI", "t$@H", "ATAVL", "H)D$", "h[]A\\A]A^A_", "AWAVH", "AUATI", "x*AVAUL", "[]A\\A]A^A_", "AWAVI", "AUATUSH", "D$0L", "L$8H", "D$ H", "D$0H", "Hcl$", "D$(u", "T$ H", "H[]A\\A]A^A_", "AWAVH", "AUATI", "x5PAVD", "AUUL", "D$pL"], "imports": [{"name": "read_image", "type": "potential_efi_function"}, {"name": "load_image", "type": "potential_efi_function"}, {"name": "start_image", "type": "potential_efi_function"}, {"name": "load_revocations_file", "type": "potential_efi_function"}, {"name": "httpboot.c", "type": "potential_efi_function"}, {"name": "UefiHttpBoot/1.0", "type": "potential_efi_function"}, {"name": "directory services (X.500)", "type": "potential_efi_function"}, {"name": "Microsoft Encrypted File System", "type": "potential_efi_function"}, {"name": "id-mod-kea-profile-88", "type": "potential_efi_function"}, {"name": "id-mod-kea-profile-93", "type": "potential_efi_function"}, {"name": "id-mod-timestamp-protocol", "type": "potential_efi_function"}, {"name": "ipsecEndSystem", "type": "potential_efi_function"}, {"name": "IPSec End System", "type": "potential_efi_function"}, {"name": "id-regCtrl-protocolEncrKey", "type": "potential_efi_function"}, {"name": "serviceLocator", "type": "potential_efi_function"}, {"name": "OCSP Service Locator", "type": "potential_efi_function"}, {"name": "directory services - algorithms", "type": "potential_efi_function"}, {"name": "secure device signature", "type": "potential_efi_function"}, {"name": "protocolInformation", "type": "potential_efi_function"}, {"name": "OCSP_SERVICELOC", "type": "potential_efi_function"}]}, "EFI/BOOT/grub.efi": {"pe_structure": {"file_size": 64120, "file_hash": "290fad7c528fbc694c4963da6c0ec74543fba1e425c8f3e77c3c4218ff0d7bb3", "pe_signature": "0x4550", "machine_type": "0x8664", "sections": [], "entry_point": null, "subsystem": null, "error": "COFF header parsing error: unpack requires a buffer of 16 bytes", "number_of_sections": 7}, "strings": ["!This program cannot be run in DOS mode.", ".text", "`.reloc", "B.data", ".dynamic", ".rela", "@.dynsym", "@.sbat", "YZQR", "ATUSH", "[]A\\", "d$ H", "[]A\\", "[]A\\", "\\$ H", "D$(H", "D$$H", "t5f9", "[]A\\A]", "[]A\\A]", "AWAVAUATUSH", "|$ D", "L$`L", "D$hH", "L$`H", "D$`A", "T$HE", "x2H;", "X[]A\\A]A^A_", "\\$PH", "D$8H", "T$ fD", "\\$0D", "|$0A9", "t$ Mc", "D$8A", "L$4J", "L$4A)", "L$(D9l$", "T$ H", "D9d$0", "T$0E", "D$8H", "T$ fD", "X[]A\\A]A^A_", "T$$H", "S@XZH", "S@HcT$,LcD$0H", "S8HcT$(H", "@[]A\\"], "imports": [{"name": "sbat,1,SBAT Version,sbat,1,https://github.com/rhboot/shim/blob/main/SBAT.md", "type": "potential_efi_function"}, {"name": "IMAGE_PROTOCOL", "type": "potential_efi_function"}, {"name": "SIMPLE_FS_PROTOCOL", "type": "potential_efi_function"}, {"name": "FILE_INFO", "type": "potential_efi_function"}, {"name": "_DevPathMediaProtocol", "type": "potential_efi_function"}, {"name": "_DevPathFilePath", "type": "potential_efi_function"}, {"name": "CatPrintNetworkProtocol", "type": "potential_efi_function"}, {"name": "ShellInterfaceProtocol", "type": "potential_efi_function"}, {"name": "gEfiPlatformDriverOverrideProtocolGuid", "type": "potential_efi_function"}, {"name": "LibCreateProtocolNotifyEvent", "type": "potential_efi_function"}, {"name": "gEfiPciIoProtocolGuid", "type": "potential_efi_function"}, {"name": "gEfiDebugSupportProtocolGuid", "type": "potential_efi_function"}, {"name": "ErrorOutSpliterProtocol", "type": "potential_efi_function"}, {"name": "EndInstanceDevicePath", "type": "potential_efi_function"}, {"name": "Ip4ServiceBindingProtocol", "type": "potential_efi_function"}, {"name": "LibLocateProtocol", "type": "potential_efi_function"}, {"name": "LibRuntimeDebugOut", "type": "potential_efi_function"}, {"name": "TextOutSpliterProtocol", "type": "potential_efi_function"}, {"name": "gEfiDriverBindingProtocolGuid", "type": "potential_efi_function"}, {"name": "AbsolutePointerProtocol", "type": "potential_efi_function"}]}, "EFI/BOOT/BOOTAA64.EFI": {"pe_structure": {"file_size": 2220032, "file_hash": "bf14944a518acb3f2c3abcf0aa5301a0fa65e98590f1b87714cf05f5de6b05dd", "pe_signature": "0x4550", "machine_type": "0xaa64", "sections": [], "entry_point": null, "subsystem": null, "error": "COFF header parsing error: unpack requires a buffer of 16 bytes", "number_of_sections": 4}, "strings": ["!This program cannot be run in DOS mode.", ".text", "`.data", "mods", ".reloc", "RB|@", "8aDL", "8a,F", "8a,F", "Q'8@9&4@9%0@9", "!L@9", "aJ@9", "aF@9", "aB@9", "a>@9", "@@QB$", "\"@ysR", "Q!@A", "QB@A", "@9_p", "Ts\":", " H`8a", "`B@9", "@X`xb", "e*@9d&@9c\"@9b", "@9f&@9e\"@9d", "hJ@9", "g*@9f&@9e\"@9d", "h\"@y", "h.@9", "g\"@yf", "hR@y", "hN@y", "hJ@y", "hF@y", "hB@y", "h>@y", "h:@y", "h6@y", "h2@y", "h.@y", "h*@y", "h&@y", "a\"@9b", "dJ@9cF@9bB@9 ", " H`8a", "gn@9fj@9ef@9db@9b", "a~@9", "az@9", "av@9"], "imports": [{"name": "no such device", "type": "potential_efi_function"}, {"name": "/File((null))", "type": "potential_efi_function"}, {"name": "/File(%s)", "type": "potential_efi_function"}, {"name": "/Protocol(%08x-%04x-%04x-%02x%02x-%02x%02x%02x%02x%02x%02x)", "type": "potential_efi_function"}, {"name": "couldn't terminate EFI services", "type": "potential_efi_function"}, {"name": "Trying to terminate EFI services again", "type": "potential_efi_function"}, {"name": "(%s): Filesystem is %s.", "type": "potential_efi_function"}, {"name": "List devices or files.", "type": "potential_efi_function"}, {"name": "this ELF file is not of the right type", "type": "potential_efi_function"}, {"name": "attempt to read past the end of file", "type": "potential_efi_function"}, {"name": "attempt to seek outside of the file", "type": "potential_efi_function"}, {"name": "invalid file name `%s'", "type": "potential_efi_function"}, {"name": "unknown filesystem", "type": "potential_efi_function"}, {"name": "grub_device_close", "type": "potential_efi_function"}, {"name": "grub_device_iterate", "type": "potential_efi_function"}, {"name": "grub_device_open", "type": "potential_efi_function"}, {"name": "grub_efi_compare_device_paths", "type": "potential_efi_function"}, {"name": "grub_efi_duplicate_device_path", "type": "potential_efi_function"}, {"name": "grub_efi_find_last_device_path", "type": "potential_efi_function"}, {"name": "grub_efi_finish_boot_services", "type": "potential_efi_function"}]}, "EFI/BOOT/BOOTIA32.EFI": {"pe_structure": {"file_size": 742064, "file_hash": "f6f6b9369bcdac99fbc24c457d9010479f47af62b00fb19f1dfd865bfb75d369", "pe_signature": "0x4550", "machine_type": "0x14c", "sections": [], "entry_point": null, "subsystem": null, "error": "COFF header parsing error: unpack requires a buffer of 16 bytes", "number_of_sections": 8}, "strings": ["!This program cannot be run in DOS mode.", ".text", "`.reloc", ".data", "@.dynamic", ".rel", "@.sbat", "<[^_]", "[^_]", "[^_]", "[^_]", "[^_]", "},PP", "QQj ", "[^_]", "[^_]", "[^_]", "[^_]", "PVhb", "WVhv", "WWVP", "VPRh", "@,9H", "[^_]", "tY;F", "QQRWP", "PPRW", "[^_]", "[^_]", "[^_]", "VPh6", "QQWV", "PPWV", "WVPhD", "PPWV", "WVPhO", "PPWV", "WVPhi", "WWRP", "QQWV", "<PERSON><PERSON><PERSON>", "PWVR", "WVRPhx", "[^_]", "RRVW", "[^_]", "PPVW", "u@PP", "RRWP", "QQVW"], "imports": [{"name": "start_image", "type": "potential_efi_function"}, {"name": "load_image", "type": "potential_efi_function"}, {"name": "read_image", "type": "potential_efi_function"}, {"name": "handle_image", "type": "potential_efi_function"}, {"name": "verify_image", "type": "potential_efi_function"}, {"name": "httpboot.c", "type": "potential_efi_function"}, {"name": "UefiHttpBoot/1.0", "type": "potential_efi_function"}, {"name": "get_nic_handle", "type": "potential_efi_function"}, {"name": "httpboot_fetch_buffer", "type": "potential_efi_function"}, {"name": "find_httpboot", "type": "potential_efi_function"}, {"name": "generate_path_from_image_path", "type": "potential_efi_function"}, {"name": "directory services (X.500)", "type": "potential_efi_function"}, {"name": "Microsoft Encrypted File System", "type": "potential_efi_function"}, {"name": "id-mod-kea-profile-88", "type": "potential_efi_function"}, {"name": "id-mod-kea-profile-93", "type": "potential_efi_function"}, {"name": "id-mod-timestamp-protocol", "type": "potential_efi_function"}, {"name": "ipsecEndSystem", "type": "potential_efi_function"}, {"name": "IPSec End System", "type": "potential_efi_function"}, {"name": "id-regCtrl-protocolEncrKey", "type": "potential_efi_function"}, {"name": "serviceLocator", "type": "potential_efi_function"}]}}, "grub_analysis": {"grub/grub.cfg": {"functions": {"ventoy_pause": {"body": "echo \"press Enter to continue ......\"\n    read vtTmpPause", "calls": ["echo", "to", "continue", "press", "Enter", "read"], "variables": [], "commands": ["echo", "read"], "security_relevant": false}, "ventoy_debug_pause": {"body": "if [ -n \"${vtdebug_flag", "calls": ["n"], "variables": ["vtdebug_flag"], "commands": ["if"], "security_relevant": false}, "ventoy_max_resolution": {"body": "vt_enum_video_mode\n    vt_get_video_mode 0 vtCurMode\n    terminal_output console\n    set gfxmode=$vtCurMode\n    terminal_output gfxterm", "calls": ["vt_get_video_mode", "vt_enum_video_mode", "set", "vtCurMode", "console", "terminal_output", "0"], "variables": ["vtCurMode"], "commands": ["terminal_output", "vt_enum_video_mode", "set", "vt_get_video_mode"], "security_relevant": false}, "ventoy_cli_console": {"body": "if [ -z \"$vtoy_display_mode\" ]; then\n        terminal_output  console\n    elif [ \"$vtoy_display_mode\" = \"GUI\" ]; then\n        terminal_output  console\n    fi", "calls": ["console", "terminal_output", "elif", "z"], "variables": ["vtoy_display_mode"], "commands": ["if", "terminal_output", "elif", "fi"], "security_relevant": false}, "ventoy_gui_console": {"body": "if [ -z \"$vtoy_display_mode\" ]; then\n        terminal_output  gfxterm\n    elif [ \"$vtoy_display_mode\" = \"GUI\" ]; then\n        terminal_output  gfxterm\n    fi", "calls": ["terminal_output", "elif", "z", "gfxterm"], "variables": ["vtoy_display_mode"], "commands": ["if", "terminal_output", "elif", "fi"], "security_relevant": false}, "ventoy_acpi_param": {"body": "if [ \"$VTOY_PARAM_NO_ACPI\" != \"1\" ]; then\n        vt_acpi_param \"$1\" \"$2\"\n    fi", "calls": ["vt_acpi_param"], "variables": ["1", "2", "VTOY_PARAM_NO_ACPI"], "commands": ["if", "vt_acpi_param", "fi"], "security_relevant": false}, "ventoy_vcfg_proc": {"body": "if vt_check_custom_boot \"${1", "calls": ["vt_check_custom_boot"], "variables": ["1"], "commands": ["if"], "security_relevant": true}, "ventoy_language": {"body": "configfile $prefix/menulang.cfg", "calls": ["configfile"], "variables": ["prefix"], "commands": ["configfile"], "security_relevant": false}, "ventoy_diagnosis": {"body": "vt_enum_video_mode    \n    configfile $prefix/debug.cfg", "calls": ["vt_enum_video_mode", "configfile"], "variables": ["prefix"], "commands": ["vt_enum_video_mode", "configfile"], "security_relevant": false}, "ventoy_localboot": {"body": "configfile $prefix/localboot.cfg", "calls": ["configfile"], "variables": ["prefix"], "commands": ["configfile"], "security_relevant": false}, "ventoy_ext_menu": {"body": "# 检查Ventoy的grub配置文件是否存在\n    if [ -e $prefix/ventoy_grub.cfg ]; then\n        # 设置Ventoy新上下文标记\n        set ventoy_new_context=1\n        # 加载Ventoy的grub配置文件\n        configfile $prefix/ventoy_grub.cfg\n        # 取消Ventoy新上下文标记\n        unset ventoy_new_context\n    else\n        # 若配置文件不存在，输出提示信息\n        echo \"ventoy_grub.cfg 不存在。\"\n        # 提示用户按回车键退出\n        echo -en \"\\n$VTLANG_ENTER_EXIT ...\"\n        # 等待用户输入（按回车继续）\n        read vtInputKey\n    fi", "calls": ["unset", "e", "取消Ventoy新上下文标记", "en", "VTLANG_ENTER_EXIT", "加载Ventoy的grub配置文件", "ventoy_new_context", "set", "1", "提示用户按回车键退出", "echo", "vtInputKey", "输出提示信息", "检查Ventoy的grub配置文件是否存在", "设置Ventoy新上下文标记", "configfile", "cfg", "read"], "variables": ["prefix", "VTLANG_ENTER_EXIT"], "commands": ["unset", "set", "echo", "fi", "if", "else", "configfile", "read"], "security_relevant": true}, "ventoy_checksum": {"body": "if [ -f \"${vtoy_iso_part", "calls": ["f"], "variables": ["vtoy_iso_part"], "commands": ["if"], "security_relevant": false}, "ventoy_show_help": {"body": "if [ -f $prefix/help.tar.gz ]; then\n        if [ -z \"$vtoy_help_txt_mem_addr\" ]; then\n            vt_load_file_to_mem \"auto\" $prefix/help.tar.gz vtoy_help_txt_mem\n        fi\n\n        loopback vt_help_tarfs mem:${vtoy_help_txt_mem_addr", "calls": ["vtoy_help_txt_mem", "vt_load_file_to_mem", "f", "vt_help_tarfs", "z", "loopback", "gz"], "variables": ["prefix", "vtoy_help_txt_mem_addr"], "commands": ["if", "loopback", "vt_load_file_to_mem", "fi"], "security_relevant": false}, "ventoy_load_menu_lang_file": {"body": "vt_load_file_to_mem \"auto\" $prefix/menu.tar.gz vtoy_menu_lang_mem\n    loopback vt_menu_tarfs mem:${vtoy_menu_lang_mem_addr", "calls": ["vt_load_file_to_mem", "vtoy_menu_lang_mem", "vt_menu_tarfs", "loopback", "gz"], "variables": ["prefix", "vtoy_menu_lang_mem_addr"], "commands": ["loopback", "vt_load_file_to_mem"], "security_relevant": false}, "get_os_type": {"body": "set vtoy_os=Linux\n    export vtoy_os\n\n    if vt_str_begin \"$vt_volume_id\" \"DLC Boot\"; then\n        if [ -f (loop)/DLCBoot.exe ]; then\n            set vtoy_os=Windows\n        fi\n    else\n        for file in \"efi/microsoft/boot/bcd\" \"sources/boot.wim\" \"boot/bcd\" \"bootmgr.efi\" \"boot/etfsboot.com\" ; do        \n            if vt_file_exist_nocase (loop)/$file; then        \n                set vtoy_os=Windows            \n                break\n            fi\n        done\n    fi\n\n    if [ \"$vtoy_os\" = \"Linux\" ]; then\n        if vt_strstr \"$vt_system_id\" \"FreeBSD\"; then\n            set vtoy_os=Unix\n            set vt_unix_type=FreeBSD\n        elif [ -e (loop)/bin/freebsd-version ]; then\n            set vtoy_os=Unix\n            set vt_unix_type=FreeBSD\n        elif [ -e (loop)/boot/kernel/geom_ventoy.ko ]; then\n            set vtoy_os=Unix\n            set vt_unix_type=FreeBSD\n        elif vt_str_begin \"$vt_system_id\" \"DragonFly\"; then\n            set vtoy_os=Unix\n            set vt_unix_type=DragonFly\n            \n            \n        elif [ -e (loop)/boot/kernel/kernel ]; then            \n            if file --is-x86-kfreebsd (loop)/boot/kernel/kernel; then\n                set vtoy_os=Unix\n                set vt_unix_type=FreeBSD\n            elif file --is-x86-knetbsd (loop)/boot/kernel/kernel; then\n                set vtoy_os=Unix\n                set vt_unix_type=NetBSD\n            fi\n        fi\n    fi\n\n    if [ -n \"${vtdebug_flag", "calls": ["FreeBSD", "vt_str_begin", "vt_file_exist_nocase", "file", "kernel", "export", "in", "n", "knetbsd", "version", "e", "NetBSD", "f", "exe", "ko", "kfreebsd", "DLC", "break", "Unix", "Windows", "Linux", "set", "elif", "DragonFly", "vt_strstr", "vtoy_os"], "variables": ["vt_volume_id", "file", "vtoy_os", "vtdebug_flag", "vt_system_id"], "commands": ["done", "set", "elif", "export", "fi", "if", "else", "for", "break"], "security_relevant": false}, "vt_check_compatible_pe": {"body": "#Check for PE without external tools\n    #set compatible if ISO file is less than 80MB\n    if [ $vt_chosen_size -GT 33554432 -a $vt_chosen_size -LE 83886080 ]; then\n        set ventoy_compatible=YES    \n    fi\n\n    return", "calls": ["LE", "file", "compatible", "Check", "external", "ISO", "without", "tools", "GT", "than", "83886080", "a", "YES", "less", "80MB", "33554432", "is", "PE", "set", "vt_chosen_size"], "variables": ["vt_chosen_size"], "commands": ["if", "set", "return", "fi"], "security_relevant": true}, "vt_check_compatible_linux": {"body": "if vt_str_begin \"$vt_volume_id\" \"embootkit\"; then\n        set ventoy_compatible=YES\n    elif [ -e \"$1/casper/tinycore.gz\" ]; then\n        set ventoy_compatible=YES\n    fi\n\n    return", "calls": ["e", "YES", "vt_str_begin", "elif", "set"], "variables": ["vt_volume_id", "1"], "commands": ["set", "elif", "fi", "if", "return"], "security_relevant": false}, "locate_initrd": {"body": "vt_linux_locate_initrd \n\n    if [ -n \"${vtdebug_flag", "calls": ["n", "vt_linux_locate_initrd"], "variables": ["vtdebug_flag"], "commands": ["if", "vt_linux_locate_initrd"], "security_relevant": false}, "locate_wim": {"body": "vt_windows_locate_wim_patch (loop) \"$1\"\n    \n    if [ -n \"${vtdebug_flag", "calls": ["n", "vt_windows_locate_wim_patch"], "variables": ["1", "vtdebug_flag"], "commands": ["if", "vt_windows_locate_wim_patch"], "security_relevant": false}, "distro_specify_wim_patch": {"body": "if [ -d (loop)/h3pe ]; then\n        vt_windows_collect_wim_patch wim /BOOT/H3_10PE.WIM\n        vt_windows_collect_wim_patch wim /BOOT/H3_7PE.WIM\n        vt_windows_collect_wim_patch wim /BOOT/H3_8PE.WIM\n        vt_windows_collect_wim_patch wim /BOOT/H3_81PE.WIM\n    elif [ -d (loop)/2k10/winpe ]; then\n        vt_windows_collect_wim_patch wim /2k10/winpe/w1086pe.wim\n        vt_windows_collect_wim_patch wim /2k10/winpe/w8x86pe.wim\n        vt_windows_collect_wim_patch wim /2k10/winpe/w7x86pe.wim\n    fi", "calls": ["WIM", "h3pe", "d", "elif", "wim", "vt_windows_collect_wim_patch", "winpe"], "variables": [], "commands": ["if", "elif", "vt_windows_collect_wim_patch", "fi"], "security_relevant": false}, "distro_specify_wim_patch_phase2": {"body": "if [ -f (loop)/boot/boot.wim ]; then\n        vt_windows_collect_wim_patch wim /boot/boot.wim\n    elif [ -f (loop)/sources/boot.wim ]; then\n        vt_windows_collect_wim_patch wim /sources/boot.wim\n    fi\n\n    if vt_str_begin \"$vt_volume_id\" \"DLC Boot\"; then\n        for vwfile in \"/DLC1/WinPE/W11x64.wim\" \"/DLC1/WinPE/W10x64.wim\" \"/DLC1/WinPE/W10x86.wim\"; do\n            if [ -f (loop)/$vwfile ]; then\n                vt_windows_collect_wim_patch wim $vwfile\n            fi\n        done\n    fi", "calls": ["vwfile", "vt_str_begin", "f", "elif", "in", "wim", "DLC", "vt_windows_collect_wim_patch"], "variables": ["vt_volume_id", "vwfile"], "commands": ["done", "elif", "fi", "if", "for", "vt_windows_collect_wim_patch"], "security_relevant": false}, "distro_specify_initrd_file": {"body": "if [ -e (loop)/boot/all.rdz ]; then\n        vt_linux_specify_initrd_file /boot/all.rdz\n    elif [ -e (loop)/boot/xen.gz ]; then \n        if [ -e (loop)/install.img ]; then\n            vt_linux_specify_initrd_file /install.img\n        fi\n    elif [ -d (loop)/casper ]; then \n        if [ -e (loop)/casper/initrd ]; then\n            vt_linux_specify_initrd_file /casper/initrd\n        fi\n        if [ -e (loop)/casper/initrd.gz ]; then\n            vt_linux_specify_initrd_file /casper/initrd.gz\n        fi\n        if [ -e (loop)/casper/initrd-oem ]; then\n            vt_linux_specify_initrd_file /casper/initrd-oem\n        fi\n    elif [ -e (loop)/boot/grub/initrd.xz ]; then\n        vt_linux_specify_initrd_file /boot/grub/initrd.xz\n    elif [ -e (loop)/initrd.gz ]; then\n        vt_linux_specify_initrd_file /initrd.gz\n    elif [ -e (loop)/slax/boot/initrfs.img ]; then\n        vt_linux_specify_initrd_file /slax/boot/initrfs.img\n    elif [ -e (loop)/minios/boot/initrfs.img ]; then\n        vt_linux_specify_initrd_file /minios/boot/initrfs.img\n    elif [ -e (loop)/pmagic/initrd.img ]; then\n        vt_linux_specify_initrd_file /pmagic/initrd.img\n    elif [ -e (loop)/boot/initrd.xz ]; then\n        vt_linux_specify_initrd_file /boot/initrd.xz\n    elif [ -e (loop)/boot/initrd.gz ]; then\n        vt_linux_specify_initrd_file /boot/initrd.gz\n    elif [ -f (loop)/boot/initrd ]; then\n        vt_linux_specify_initrd_file /boot/initrd\n    elif [ -f (loop)/boot/x86_64/loader/initrd ]; then\n        vt_linux_specify_initrd_file /boot/x86_64/loader/initrd\n    elif [ -f (loop)/boot/initramfs-x86_64.img ]; then\n        vt_linux_specify_initrd_file /boot/initramfs-x86_64.img\n    elif [ -f (loop)/boot/isolinux/initramfs_data64.cpio.gz ]; then \n        vt_linux_specify_initrd_file /boot/isolinux/initramfs_data64.cpio.gz\n    elif [ -f (loop)/boot/initrd.img ]; then \n        vt_linux_specify_initrd_file /boot/initrd.img\n        \n    fi\n    \n    if [ -f (loop)/isolinux/initrd.gz ]; then \n        vt_linux_specify_initrd_file /isolinux/initrd.gz\n    fi\n    \n    if vt_str_begin \"$vt_volume_id\" \"QUBES\"; then \n        vt_linux_specify_initrd_file /images/pxeboot/initrd.img\n    fi\n    \n    if [ \"$vt_chosen_size\" = \"1133375488\" ]; then\n        if [ -d (loop)/boot/grub/x86_64-efi ]; then\n            vt_cpio_busybox64 \"64h\"\n        fi\n    fi", "calls": ["e", "img", "vt_cpio_busybox64", "vt_str_begin", "oem", "elif", "rdz", "d", "f", "efi", "initrd", "casper", "xz", "vt_linux_specify_initrd_file", "gz"], "variables": ["vt_volume_id", "vt_chosen_size"], "commands": ["vt_cpio_busybox64", "elif", "fi", "if", "vt_linux_specify_initrd_file"], "security_relevant": false}, "distro_specify_initrd_file_phase2": {"body": "if [ -f (loop)/boot/initrd.img ]; then\n        vt_linux_specify_initrd_file /boot/initrd.img\n    elif [ -f (loop)/Setup/initrd.gz ]; then\n        vt_linux_specify_initrd_file /Setup/initrd.gz\n    elif [ -f (loop)/isolinux/initramfs ]; then\n        vt_linux_specify_initrd_file /isolinux/initramfs\n    elif [ -f (loop)/boot/iniramfs.igz ]; then\n        vt_linux_specify_initrd_file /boot/iniramfs.igz\n    elif [ -f (loop)/initrd-x86_64 ]; then\n        vt_linux_specify_initrd_file /initrd-x86_64\n    elif [ -f (loop)/live/initrd.img ]; then \n        vt_linux_specify_initrd_file /live/initrd.img\n    elif [ -f (loop)/initrd.img ]; then \n        vt_linux_specify_initrd_file /initrd.img\n    elif [ -f (loop)/sysresccd/boot/x86_64/sysresccd.img ]; then \n        vt_linux_specify_initrd_file /sysresccd/boot/x86_64/sysresccd.img\n    elif [ -f (loop)/CDlinux/initrd ]; then \n        vt_linux_specify_initrd_file /CDlinux/initrd\n    elif [ -f (loop)/parabola/boot/x86_64/parabolaiso.img ]; then \n        vt_linux_specify_initrd_file /parabola/boot/x86_64/parabolaiso.img\n        if [ -f (loop)/parabola/boot/i686/parabolaiso.img ]; then \n            vt_linux_specify_initrd_file /parabola/boot/i686/parabolaiso.img\n        fi\n    elif [ -f (loop)/parabola/boot/x86_64/initramfs-linux-libre.img ]; then\n        vt_linux_specify_initrd_file /parabola/boot/x86_64/initramfs-linux-libre.img\n        if [ -f (loop)/parabola/boot/i686/initramfs-linux-libre.img ]; then\n            vt_linux_specify_initrd_file /parabola/boot/i686/initramfs-linux-libre.img\n        fi\n    elif [ -f (loop)/hyperbola/boot/x86_64/hyperiso.img ]; then \n        vt_linux_specify_initrd_file /hyperbola/boot/x86_64/hyperiso.img\n        if [ -f (loop)/hyperbola/boot/i686/hyperiso.img ]; then \n            vt_linux_specify_initrd_file /hyperbola/boot/i686/hyperiso.img\n        fi\n    elif [ -f (loop)/EFI/BOOT/initrd.img ]; then \n        #Qubes\n        vt_linux_specify_initrd_file /EFI/BOOT/initrd.img\n        if [ \"$grub_platform\" != \"pc\" ]; then\n            vt_add_replace_file 0 \"initrd.img\"    \n        fi\n    elif [ -f (loop)/initrd ]; then \n        vt_linux_specify_initrd_file /initrd\n    elif [ -f (loop)/live/initrd1 ]; then \n        vt_linux_specify_initrd_file /live/initrd1\n    elif [ -f (loop)/isolinux/initrd.img ]; then \n        vt_linux_specify_initrd_file /isolinux/initrd.img\n    elif [ -f (loop)/isolinux/initrd.gz ]; then \n        vt_linux_specify_initrd_file /isolinux/initrd.gz\n    elif [ -f (loop)/syslinux/kernel/initramfs.gz ]; then \n        vt_linux_specify_initrd_file /syslinux/kernel/initramfs.gz    \n    elif vt_strstr \"$vt_volume_id\" \"Daphile\"; then\n        vt_linux_parse_initrd_isolinux   (loop)/isolinux/\n    elif [ -f (loop)/boot/rootfs.xz ]; then \n        vt_linux_specify_initrd_file /boot/rootfs.xz\n        if [ \"$grub_platform\" != \"pc\" ]; then\n            vt_add_replace_file 0 \"minimal\\\\x86_64\\\\rootfs.xz\"\n        fi\n    elif [ -f (loop)/arch/boot/x86_64/archiso.img ]; then \n        vt_linux_specify_initrd_file /arch/boot/x86_64/archiso.img\n        if [ \"$grub_platform\" != \"pc\" ]; then\n            vt_add_replace_file 0 \"EFI\\\\archiso\\\\archiso.img\"\n        fi\n    elif [ -f (loop)/blackarch/boot/x86_64/archiso.img ]; then \n        vt_linux_specify_initrd_file /blackarch/boot/x86_64/archiso.img\n    elif [ -f (loop)/blackarch/boot/x86_64/initramfs-linux.img ]; then \n        vt_linux_specify_initrd_file /blackarch/boot/x86_64/initramfs-linux.img\n        \n    elif [ -f (loop)/install.amd/initrd.gz ]; then\n        vt_linux_specify_initrd_file /live/initrd2.img\n        vt_linux_specify_initrd_file /install.amd/initrd.gz\n        vt_linux_specify_initrd_file /install.amd/gtk/initrd.gz\n    elif [ -f (loop)/boot/grub/kernels.cfg ]; then\n        vt_linux_parse_initrd_grub  file  (loop)/boot/grub/kernels.cfg\n    elif [ -f (loop)/austrumi/initrd.gz ]; then\n        vt_linux_specify_initrd_file /austrumi/initrd.gz\n        if [ -f (loop)/EFI/BOOT/bootx64.efi ]; then\n            vt_cpio_busybox64 \"64h\"\n        fi\n    elif [ -f (loop)/boot/initfs.x86_64-efi ]; then\n        vt_linux_specify_initrd_file /boot/initfs.x86_64-efi\n        if [ -f (loop)/boot/initfs.i386-pc ]; then\n            vt_linux_specify_initrd_file /boot/initfs.i386-pc\n        fi\n    elif [ -f (loop)/antiX/initrd.gz ]; then\n        vt_linux_specify_initrd_file /antiX/initrd.gz\n    elif [ -f (loop)/360Disk/initrd.gz ]; then\n        vt_linux_specify_initrd_file /360Disk/initrd.gz\n    elif [ -f (loop)/porteus/initrd.xz ]; then\n        vt_linux_specify_initrd_file /porteus/initrd.xz\n    elif [ -f (loop)/pyabr/boot/initrfs.img ]; then\n        vt_linux_specify_initrd_file /pyabr/boot/initrfs.img\n    elif [ -f (loop)/initrd0.img ]; then\n        vt_linux_specify_initrd_file /initrd0.img\n    elif [ -f (loop)/sysresccd/boot/i686/sysresccd.img ]; then\n        vt_linux_specify_initrd_file /sysresccd/boot/i686/sysresccd.img\n    elif [ -f (loop)/boot/full.cz ]; then\n        vt_linux_specify_initrd_file /boot/full.cz\n    elif [ -f (loop)/images/pxeboot/initrd.img ]; then\n        vt_linux_specify_initrd_file /images/pxeboot/initrd.img\n    elif [ -f (loop)/live/initrd ]; then\n        vt_linux_specify_initrd_file /live/initrd\n    elif [ -f (loop)/initramfs-linux.img ]; then\n        vt_linux_specify_initrd_file /initramfs-linux.img\n    elif [ -f (loop)/boot/isolinux/initrd.gz ]; then\n        vt_linux_specify_initrd_file /boot/isolinux/initrd.gz        \n    fi", "calls": ["img", "file", "Qubes", "igz", "vt_cpio_busybox64", "initramfs", "cfg", "vt_linux_specify_initrd_file", "0", "f", "x86_64", "vt_linux_parse_initrd_grub", "initrd", "initrd1", "xz", "vt_linux_parse_initrd_isolinux", "elif", "cz", "efi", "vt_add_replace_file", "vt_strstr", "pc", "gz"], "variables": ["vt_volume_id", "grub_platform"], "commands": ["vt_linux_parse_initrd_isolinux", "vt_cpio_busybox64", "elif", "vt_linux_parse_initrd_grub", "fi", "if", "vt_add_replace_file", "vt_linux_specify_initrd_file"], "security_relevant": false}, "ventoy_get_ghostbsd_ver": {"body": "# fallback to parse version from elf /boot/kernel/kernel\n    set vt_freebsd_ver=xx", "calls": ["version", "fallback", "set", "to", "from", "elf", "kernel", "parse"], "variables": [], "commands": ["set"], "security_relevant": false}, "ventoy_get_furybsd_ver": {"body": "set vt_freebsd_ver=12.x\n    if regexp --set 1:vtFuryVer \"(14|13)\\.[0-9]\" \"$2\"; then\n        set vt_freebsd_ver=${vtFuryVer", "calls": ["vtFuryVer", "set", "x", "regexp"], "variables": ["vtFuryVer", "2"], "commands": ["if", "set"], "security_relevant": false}, "ventoy_get_freenas_ver": {"body": "set vt_freebsd_ver=11.x\n\n    if [ -e (loop)/FreeNAS-MANIFEST ]; then\n        vt_parse_freenas_ver (loop)/FreeNAS-MANIFEST vt_freenas_ver\n        if regexp --set 1:vtNasVer \"^(14|13|12|11)\\.[0-9]\" \"$vt_freenas_ver\"; then\n            set vt_freebsd_ver=${vtNasVer", "calls": ["MANIFEST", "e", "vt_freenas_ver", "set", "vtNasVer", "vt_parse_freenas_ver", "regexp", "x"], "variables": ["vtNasVer", "vt_freenas_ver"], "commands": ["if", "set", "vt_parse_freenas_ver"], "security_relevant": false}, "ventoy_get_truenas_ver": {"body": "set vt_freebsd_ver=12.x\n\n    if [ -e (loop)/TrueNAS-MANIFEST ]; then\n        vt_parse_freenas_ver (loop)/TrueNAS-MANIFEST vt_truenas_ver\n        if regexp --set 1:vtTNasVer \"^(14|13|12|11)\\.[0-9]\" \"$vt_truenas_ver\"; then\n            set vt_freebsd_ver=${vtTNasVer", "calls": ["vt_truenas_ver", "MANIFEST", "e", "vtTNasVer", "set", "vt_parse_freenas_ver", "regexp", "x"], "variables": ["vt_truenas_ver", "vtTNasVer"], "commands": ["if", "set", "vt_parse_freenas_ver"], "security_relevant": false}, "ventoy_get_midnightbsd_ver": {"body": "if vt_str_begin \"$vt_volume_id\" \"1_\"; then\n        set vt_freebsd_ver=11.x\n    elif vt_str_begin \"$vt_volume_id\" \"2_\"; then\n        set vt_freebsd_ver=2.x\n    elif vt_str_begin \"$vt_volume_id\" \"3_\"; then\n        set vt_freebsd_ver=3.x\n    fi", "calls": ["elif", "set", "x", "vt_str_begin"], "variables": ["vt_volume_id"], "commands": ["if", "set", "elif", "fi"], "security_relevant": false}, "ventoy_freebsd_proc": {"body": "set vtFreeBsdDistro=FreeBSD\n    set vt_freebsd_ver=xx\n\n    if [ -e (loop)/boot/kernel/geom_ventoy.ko ]; then\n        vt_unix_ko_fillmap /boot/kernel/geom_ventoy.ko\n        return\n    fi\n\n    if vt_strstr \"$vt_volume_id\" \"GHOSTBSD\"; then\n        ventoy_get_ghostbsd_ver \"$1\" \"${chosen_path", "calls": ["e", "FreeBSD", "set", "ventoy_get_ghostbsd_ver", "xx", "vt_strstr", "return", "ko", "vt_unix_ko_fillmap"], "variables": ["vt_volume_id", "chosen_path", "1"], "commands": ["set", "ventoy_get_ghostbsd_ver", "fi", "if", "return", "vt_unix_ko_fillmap"], "security_relevant": false}, "ventoy_dragonfly_proc": {"body": "unset vt_unix_mod_path\n    for file in \"/boot/kernel/initrd.img.gz\"; do\n        if [ -e (loop)${file", "calls": ["unset", "e", "in", "file", "vt_unix_mod_path"], "variables": ["file"], "commands": ["unset", "if", "for"], "security_relevant": false}, "ventoy_unix_comm_proc": {"body": "vt_unix_reset\n    \n    vt_unix_check_vlnk \"${1", "calls": ["vt_unix_check_vlnk", "vt_unix_reset"], "variables": ["1"], "commands": ["vt_unix_check_vlnk", "vt_unix_reset"], "security_relevant": true}, "uefi_windows_menu_func": {"body": "vt_windows_reset\n\n    unset vt_cur_wimboot_mode\n    if vt_check_mode 4 \"$vt_chosen_name\"; then\n        set vt_cur_wimboot_mode=1\n    fi\n\n    if [ \"$ventoy_compatible\" = \"NO\" -o \"$vt_cur_wimboot_mode\" = \"1\" ]; then\n        if [ \"$ventoy_fs_probe\" = \"iso9660\" ]; then\n            loopback -d loop\n            vt_iso9660_nojoliet 1            \n            loopback loop \"$1$2\"\n        fi\n        \n        for file in \"efi/microsoft/boot/bcd\"; do\n            vt_windows_collect_wim_patch bcd (loop)/$file                \n        done\n\n        vt_windows_count_wim_patch vt_wim_cnt\n        if [ $vt_wim_cnt -eq 0 ]; then\n            distro_specify_wim_patch_phase2\n        fi\n        \n        ventoy_debug_pause        \n        locate_wim \"${chosen_path", "calls": ["unset", "loop", "locate_wim", "d", "4", "file", "vt_wim_cnt", "ventoy_debug_pause", "vt_windows_count_wim_patch", "loopback", "vt_check_mode", "1", "in", "eq", "0", "o", "vt_windows_collect_wim_patch", "distro_specify_wim_patch_phase2", "vt_cur_wimboot_mode", "set", "vt_windows_reset", "bcd", "vt_iso9660_nojoliet"], "variables": ["chosen_path", "vt_cur_wimboot_mode", "ventoy_fs_probe", "2", "1", "file", "vt_wim_cnt", "vt_chosen_name", "ventoy_compatible"], "commands": ["unset", "done", "set", "locate_wim", "vt_windows_reset", "fi", "if", "for", "ventoy_debug_pause", "vt_windows_count_wim_patch", "vt_iso9660_nojoliet", "vt_windows_collect_wim_patch", "loopback", "distro_specify_wim_patch_phase2"], "security_relevant": true}, "uefi_find_replace_initrd": {"body": "if vt_get_efi_vdisk_offset \"${1", "calls": ["vt_get_efi_vdisk_offset"], "variables": ["1"], "commands": ["if"], "security_relevant": false}, "uefi_linux_menu_func": {"body": "if [ \"$ventoy_compatible\" = \"NO\" ]; then    \n        \n        if [ \"$ventoy_fs_probe\" = \"udf\" ]; then\n            loopback -d loop            \n            set ventoy_fs_probe=iso9660\n            loopback loop \"$1$2\"\n        fi\n        \n        vt_load_cpio  $vtoy_path   \"$2\" \"$1\" \"busybox=$ventoy_busybox_ver\"\n        \n        vt_linux_clear_initrd\n        \n        if [ -d (loop)/pmagic ]; then\n            vt_linux_specify_initrd_file /pmagic/initrd.img\n        else\n            for file in \"boot/grub/grub.cfg\" \"EFI/BOOT/grub.cfg\" \"EFI/boot/grub.cfg\" \"efi/boot/grub.cfg\" \"EFI/BOOT/BOOTX64.conf\" \"/grub/grub.cfg\" \"EFI/BOOT/grub/grub.cfg\"; do\n                if [ -e (loop)/$file ]; then                    \n                    vt_linux_parse_initrd_grub  file  (loop)/$file\n                fi\n            done\n        fi\n\n        # special process for special distros\n        if [ -d (loop)/loader/entries ]; then\n            vt_linux_parse_initrd_grub  dir  (loop)/loader/entries/\n        elif [ -d (loop)/boot/grub ]; then\n            vt_linux_parse_initrd_grub  dir  (loop)/boot/grub/\n        fi\n        \n        distro_specify_initrd_file\n        \n        vt_linux_initrd_count vtcount\n        \n        if [ $vtcount -eq 0 ]; then\n            if [ -e (loop)/EFI/boot/livegrub.cfg ]; then\n                vt_linux_parse_initrd_grub  file  (loop)/EFI/boot/livegrub.cfg\n            fi\n            distro_specify_initrd_file_phase2\n            \n            if [ \"$vt_efi_dir\" = \"NO\" ]; then\n                if [ -f (loop)/efi.img ];  then\n                    vt_add_replace_file 0 \"initrd\"\n                fi\n            fi\n        fi\n        \n        locate_initrd\n        \n        if [ -d (loop)/loader/entries ]; then\n            vt_linux_get_main_initrd_index vtindex\n            \n            if [ -d (loop)/arch ]; then\n                if [ -f (loop)/arch/boot/x86_64/archiso.img ]; then\n                    vt_add_replace_file $vtindex \"EFI\\\\archiso\\\\archiso.img\"\n                elif [ -f (loop)/arch/boot/x86_64/initramfs-linux.img ]; then\n                    vt_add_replace_file $vtindex \"arch\\\\boot\\\\x86_64\\\\initramfs-linux.img\"\n                elif [ -f (loop)/boot/initramfs_x86_64.img ]; then\n                    vt_add_replace_file $vtindex \"boot\\\\initramfs_x86_64.img\"\n                fi\n            elif [ -d (loop)/blackarch ]; then\n                if [ -f (loop)/blackarch/boot/x86_64/archiso.img ]; then\n                    vt_add_replace_file $vtindex \"EFI\\\\archiso\\\\archiso.img\"\n                elif [ -f (loop)/blackarch/boot/x86_64/initramfs-linux.img ]; then\n                    vt_add_replace_file $vtindex \"blackarch\\\\boot\\\\x86_64\\\\initramfs-linux.img\"\n                fi\n            elif [ -d (loop)/anarchy ]; then\n                if [ -f (loop)/anarchy/boot/x86_64/initramfs-linux.img ]; then\n                    vt_add_replace_file $vtindex \"anarchy\\\\boot\\\\x86_64\\\\initramfs-linux.img\"\n                fi\n            elif [ -d (loop)/parabola ]; then\n                if [ -f (loop)/parabola/boot/x86_64/parabolaiso.img ]; then\n                    vt_add_replace_file $vtindex \"EFI\\\\parabolaiso\\\\parabolaiso.img\"\n                elif [ -f (loop)/parabola/boot/x86_64/initramfs-linux-libre.img ]; then\n                    vt_add_replace_file $vtindex \"parabola\\\\boot\\\\x86_64\\\\initramfs-linux-libre.img\"\n                fi\n            elif [ -f (loop)/EFI/BOOT/initrd.gz ]; then\n                vt_add_replace_file $vtindex \"EFI\\\\BOOT\\\\initrd.gz\"\n            elif [ -f (loop)/loader/entries/thinstation.conf ]; then\n                vt_add_replace_file $vtindex \"boot\\\\initrd\"\n            elif [ -f (loop)/loader/entries/pisi-efi-x86_64.conf ]; then\n                vt_add_replace_file $vtindex \"EFI\\\\pisi\\\\initrd.img\"\n            fi\n\n            vt_get_replace_file_cnt vt_replace_cnt\n            if [ $vt_replace_cnt -eq 0 ]; then\n                uefi_find_replace_initrd \"$1\" \"$2\" $vtindex\n            fi\n        elif [ -d (loop)/EFI/boot/entries ]; then\n            if [ -f (loop)/parabola/boot/x86_64/parabolaiso.img ]; then\n                vt_add_replace_file 0 \"EFI\\\\parabolaiso\\\\parabolaiso.img\"\n            elif [ -f (loop)/hyperbola/boot/x86_64/hyperiso.img ]; then\n                vt_add_replace_file 0 \"EFI\\\\hyperiso\\\\hyperiso.img\"\n            fi\n        elif [ -d (loop)/EFI/BOOT/entries ]; then\n            vt_linux_get_main_initrd_index vtindex\n\n            if [ -f (loop)/parabola/boot/x86_64/parabolaiso.img ]; then\n                vt_add_replace_file 0 \"EFI\\\\parabolaiso\\\\parabolaiso.img\"\n            elif [ -f (loop)/parabola/boot/x86_64/initramfs-linux-libre.img ]; then\n                vt_add_replace_file $vtindex \"parabola\\\\boot\\\\x86_64\\\\initramfs-linux-libre.img\"\n            fi\n        elif [ -e (loop)/syslinux/alt0/full.cz ]; then\n            vt_add_replace_file 0 \"EFI\\\\BOOT\\\\full.cz\"            \n            set FirstTryBootFile='@EFI@<EMAIL>'\n            \n        elif vt_str_begin \"$vt_volume_id\" \"SolusLive\"; then\n            vt_add_replace_file 0 \"initrd\"\n\n        fi\n        \n    fi\n    \n    vt_linux_chain_data \"${1", "calls": ["img", "special", "distros", "vt_str_begin", "conf", "arch", "d", "file", "vtoy_path", "vt_linux_chain_data", "loopback", "iso9660", "distro_specify_initrd_file_phase2", "in", "eq", "pmagic", "cfg", "vtcount", "vt_linux_specify_initrd_file", "0", "vt_get_replace_file_cnt", "e", "vt_linux_initrd_count", "f", "vt_linux_parse_initrd_grub", "vt_linux_get_main_initrd_index", "process", "uefi_find_replace_initrd", "vt_load_cpio", "entries", "vtindex", "vt_replace_cnt", "parabola", "dir", "set", "elif", "cz", "vt_add_replace_file", "vt_linux_clear_initrd", "locate_initrd", "distro_specify_initrd_file", "grub", "loop", "blackarch", "gz", "anarchy"], "variables": ["vt_replace_cnt", "ventoy_fs_probe", "ventoy_busybox_ver", "2", "vt_volume_id", "1", "file", "vt_efi_dir", "vtoy_path", "vtcount", "ventoy_compatible", "vtindex"], "commands": ["if", "vt_linux_chain_data", "loopback", "distro_specify_initrd_file_phase2", "else", "vt_linux_specify_initrd_file", "vt_get_replace_file_cnt", "vt_linux_initrd_count", "done", "vt_linux_parse_initrd_grub", "vt_linux_get_main_initrd_index", "fi", "uefi_find_replace_initrd", "for", "vt_load_cpio", "set", "elif", "vt_add_replace_file", "vt_linux_clear_initrd", "locate_initrd", "distro_specify_initrd_file"], "security_relevant": false}, "uefi_unix_menu_func": {"body": "ventoy_unix_comm_proc $1 \"${chosen_path", "calls": ["1", "ventoy_unix_comm_proc"], "variables": ["chosen_path", "1"], "commands": ["ventoy_unix_comm_proc"], "security_relevant": false}, "ventoy_reset_nojoliet": {"body": "if vt_str_begin \"$vt_volume_id\" \"ARCARESCUE\"; then\n        vt_iso9660_nojoliet 1\n    else\n        vt_iso9660_nojoliet 0\n    fi\n    \n    vt_append_extra_sector 0", "calls": ["vt_str_begin", "1", "vt_iso9660_nojoliet", "vt_append_extra_sector", "0"], "variables": ["vt_volume_id"], "commands": ["fi", "if", "else", "vt_iso9660_nojoliet", "vt_append_extra_sector"], "security_relevant": false}, "uefi_iso_menu_func": {"body": "if [ -n \"$vtisouefi\" ]; then\n        set LoadIsoEfiDriver=on\n        unset vtisouefi\n    elif vt_check_mode 2 \"$vt_chosen_name\"; then\n        set LoadIsoEfiDriver=on\n    else\n        unset LoadIsoEfiDriver\n    fi\n\n    set chosen_path=\"$2\"\n    vt_select_auto_install \"${chosen_path", "calls": ["unset", "vt_check_mode", "vtisouefi", "2", "set", "elif", "n", "on", "LoadIsoEfiDriver", "vt_select_auto_install"], "variables": ["vt_chosen_name", "chosen_path", "vtisouefi", "2"], "commands": ["unset", "set", "elif", "fi", "if", "else", "vt_select_auto_install"], "security_relevant": true}, "uefi_iso_memdisk": {"body": "echo 'Loading ISO file to memory ...'\n    vt_load_img_memdisk \"${1", "calls": ["ISO", "echo", "file", "to", "Loading", "memory", "vt_load_img_memdisk"], "variables": ["1"], "commands": ["vt_load_img_memdisk", "echo"], "security_relevant": false}, "vtoy_windows_wimboot": {"body": "if [ -f (loop)/x86/sources/boot.wim -a -f (loop)/x64/sources/boot.wim ]; then\n        vt_sel_wimboot vtoy_wimboot_bit\n        if [ \"$vtoy_wimboot_bit\" = \"32\" ]; then\n            set vtoy_wimboot_prefix=(loop)/x86\n        else\n            set vtoy_wimboot_prefix=(loop)/x64\n        fi\n    else\n        set vtoy_wimboot_prefix=(loop)\n        if vt_is_pe64 $vtoy_wimboot_prefix/setup.exe; then\n            set vtoy_wimboot_bit=64\n        else\n            set vtoy_wimboot_bit=32\n        fi\n    fi\n\n    if [ -n \"${vtdebug_flag", "calls": ["a", "x86", "f", "set", "vt_is_pe64", "vtoy_wimboot_bit", "32", "n", "wim", "64", "x64", "vt_sel_wimboot"], "variables": ["vtoy_wimboot_prefix", "vtdebug_flag", "vtoy_wimboot_bit"], "commands": ["set", "fi", "if", "else", "vt_sel_wimboot"], "security_relevant": false}, "vtoy_winpe_wimboot": {"body": "unset vtoy_boot_sdi_legacy\n    unset vtoy_boot_sdi_efi\n    \n    set vtoy_wimboot_prefix=(loop)    \n    set vtoy_wim_path=\"$1\"\n    \n    if [ -n \"${vtdebug_flag", "calls": ["unset", "set", "n", "vtoy_boot_sdi_efi", "vtoy_boot_sdi_legacy"], "variables": ["1", "vtdebug_flag"], "commands": ["unset", "if", "set"], "security_relevant": false}, "vtoy_wimboot_func": {"body": "if [ \"$grub_platform\" = \"pc\" ]; then\n        set vt_wimkernel=wimboot.x86_64.xz        \n    else\n        if [ \"$grub_cpu\" = \"i386\" ]; then\n            set vt_wimkernel=wimboot.i386.efi.xz\n        else\n            set vt_wimkernel=wimboot.x86_64.xz\n        fi\n    fi\n\n    if vt_is_standard_winiso (loop); then\n        echo -e \"\\n==================== VENTOY WIMBOOT ==================\\n\"\n        vtoy_windows_wimboot\n    else\n        vt_sel_winpe_wim (loop)\n        if [ -n \"$vtoy_pe_wim_path\" ]; then            \n            echo -e \"\\n==================== VENTOY WIMBOOT ==================\\n\"\n            \n            vt_fs_ignore_case 1\n            vt_load_file_to_mem \"auto\" $vtoy_path/common_bcd.xz vtoy_pe_bcd_mem\n            \n            set vt_sdi_path=0\n            for vsdi in \"boot/boot.sdi\" \"2K10/FONTS/boot.sdi\" \"SSTR/boot.sdi\" \"ISPE/BOOT.SDI\" \\\n            \"boot/uqi.sdi\" \"ISYL/boot.sdi\" \"WEPE/WEPE.SDI\" ; do\n                if [ -F \"(loop)/$vsdi\" ]; then\n                    set vt_sdi_path=$vsdi\n                    break\n                fi\n            done\n            \n            if [ \"$grub_platform\" = \"pc\" ]; then\n                vt_load_file_to_mem \"auto\" $vtoy_path/common_bootmgr.xz vtoy_pe_bootmgr_mem\n                vtoy_winpe_wimboot \"$vtoy_pe_wim_path\" \"$vt_sdi_path\" 1\n            else\n                vtoy_winpe_wimboot \"$vtoy_pe_wim_path\" \"$vt_sdi_path\" 0\n            fi\n            \n            vt_fs_ignore_case 0\n        fi\n    fi", "calls": ["vtoy_winpe_wimboot", "vt_load_file_to_mem", "vt_sel_winpe_wim", "vt_is_standard_winiso", "1", "in", "n", "0", "e", "VENTOY", "WIMBOOT", "vtoy_pe_bootmgr_mem", "F", "vtoy_pe_bcd_mem", "break", "xz", "set", "echo", "vsdi", "vt_fs_ignore_case", "vtoy_windows_wimboot"], "variables": ["grub_platform", "vsdi", "vtoy_path", "grub_cpu", "vtoy_pe_wim_path", "vt_sdi_path"], "commands": ["vtoy_winpe_wimboot", "vt_load_file_to_mem", "done", "set", "echo", "vt_sel_winpe_wim", "fi", "if", "else", "vt_fs_ignore_case", "for", "\"boot/uqi.sdi\"", "vtoy_windows_wimboot", "break"], "security_relevant": false}, "legacy_windows_menu_func": {"body": "vt_windows_reset\n    \n    unset vt_cur_wimboot_mode\n    if vt_check_mode 4 \"$vt_chosen_name\"; then\n        set vt_cur_wimboot_mode=1\n    fi\n    \n    if [ \"$ventoy_compatible\" = \"NO\" -o \"$vt_cur_wimboot_mode\" = \"1\" ]; then\n        if [ \"$ventoy_fs_probe\" = \"iso9660\" ]; then\n            loopback -d loop\n            vt_iso9660_nojoliet 1\n            loopback loop \"$1$2\"\n        fi\n        \n        for file in \"boot/bcd\" \"/efi/microsoft/boot/bcd\" \"SSTR/BCD\" \"boot/bce\"; do\n            vt_windows_collect_wim_patch bcd (loop)/$file                \n        done\n        \n        distro_specify_wim_patch\n\n        vt_windows_count_wim_patch vt_wim_cnt\n        if [ $vt_wim_cnt -eq 0 ]; then\n            distro_specify_wim_patch_phase2\n        fi\n        \n        ventoy_debug_pause\n        if [ -z \"$vt_cur_wimboot_mode\" ]; then\n            locate_wim \"${chosen_path", "calls": ["unset", "loop", "distro_specify_wim_patch", "locate_wim", "d", "4", "file", "vt_wim_cnt", "ventoy_debug_pause", "vt_windows_count_wim_patch", "z", "loopback", "vt_check_mode", "1", "in", "eq", "0", "o", "vt_windows_collect_wim_patch", "distro_specify_wim_patch_phase2", "vt_cur_wimboot_mode", "set", "vt_windows_reset", "bcd", "vt_iso9660_nojoliet"], "variables": ["chosen_path", "vt_cur_wimboot_mode", "ventoy_fs_probe", "2", "1", "file", "vt_wim_cnt", "vt_chosen_name", "ventoy_compatible"], "commands": ["unset", "done", "distro_specify_wim_patch", "set", "locate_wim", "vt_windows_reset", "fi", "if", "for", "ventoy_debug_pause", "vt_windows_count_wim_patch", "vt_iso9660_nojoliet", "vt_windows_collect_wim_patch", "loopback", "distro_specify_wim_patch_phase2"], "security_relevant": true}, "legacy_linux_menu_func": {"body": "if [ \"$ventoy_compatible\" = \"NO\" ]; then\n\n        if [ \"$ventoy_fs_probe\" = \"udf\" ]; then\n            loopback -d loop\n            set ventoy_fs_probe=iso9660\n            loopback loop \"$1$2\"\n        fi\n\n        \n        if vt_syslinux_need_nojoliet \"$1$2\"; then\n            vt_iso9660_nojoliet 1\n            loopback -d loop\n            loopback loop \"$1$2\"\n        fi\n\n        vt_load_cpio  $vtoy_path  \"$2\" \"$1\" \"busybox=$ventoy_busybox_ver\"\n\n        vt_linux_clear_initrd\n        \n        if [ -d (loop)/pmagic ]; then\n            vt_linux_specify_initrd_file /pmagic/initrd.img\n        else\n            for dir in \"isolinux\" \"boot/isolinux\" \"boot/x86_64/loader\" \"syslinux\" \"boot/syslinux\"; do\n                if [ -d (loop)/$dir ]; then\n                    vt_linux_parse_initrd_isolinux   (loop)/$dir/\n                fi\n            done\n        fi\n        \n        # special process for special distros\n        #archlinux\n        if [ -d (loop)/arch/boot/syslinux ]; then\n            vt_linux_parse_initrd_isolinux   (loop)/arch/boot/syslinux/  /arch/\n            vt_linux_parse_initrd_isolinux   (loop)/arch/boot/syslinux/  /arch/boot/syslinux/\n        elif [ -d (loop)/anarchy/boot/syslinux ]; then\n            vt_linux_parse_initrd_isolinux   (loop)/anarchy/boot/syslinux/  /anarchy/\n            \n        #manjaro\n        elif [ -d (loop)/manjaro ]; then\n            if [ -e (loop)/boot/grub/kernels.cfg ]; then\n                vt_linux_parse_initrd_grub  file  (loop)/boot/grub/kernels.cfg\n            fi\n        elif [ -e (loop)/boot/grub/grub.cfg ]; then                \n            vt_linux_parse_initrd_grub  file  (loop)/boot/grub/grub.cfg\n        fi\n        \n        distro_specify_initrd_file\n        \n        vt_linux_initrd_count vtcount\n        if [ $vtcount -eq 0 ]; then\n            if [ -d (loop)/rancheros ]; then\n                vt_linux_parse_initrd_isolinux   (loop)/boot/  /boot/isolinux/\n            fi\n\n            distro_specify_initrd_file_phase2\n        fi\n        \n        locate_initrd\n    fi\n    \n    vt_linux_chain_data \"${1", "calls": ["loop", "img", "special", "distros", "d", "syslinux", "file", "vtoy_path", "vt_linux_chain_data", "loopback", "iso9660", "1", "distro_specify_initrd_file_phase2", "in", "eq", "pmagic", "cfg", "vtcount", "vt_linux_specify_initrd_file", "0", "rancheros", "e", "vt_linux_initrd_count", "vt_linux_parse_initrd_grub", "process", "vt_load_cpio", "vt_syslinux_need_nojoliet", "<PERSON><PERSON><PERSON>", "vt_linux_parse_initrd_isolinux", "dir", "set", "elif", "manjaro", "vt_linux_clear_initrd", "locate_initrd", "distro_specify_initrd_file", "vt_iso9660_nojoliet"], "variables": ["ventoy_fs_probe", "ventoy_busybox_ver", "2", "dir", "1", "vtoy_path", "vtcount", "ventoy_compatible"], "commands": ["if", "vt_linux_chain_data", "loopback", "distro_specify_initrd_file_phase2", "else", "vt_linux_specify_initrd_file", "vt_linux_initrd_count", "done", "vt_linux_parse_initrd_grub", "fi", "for", "vt_load_cpio", "vt_linux_parse_initrd_isolinux", "set", "elif", "vt_linux_clear_initrd", "locate_initrd", "distro_specify_initrd_file", "vt_iso9660_nojoliet"], "security_relevant": false}, "legacy_unix_menu_func": {"body": "ventoy_unix_comm_proc $1 \"${chosen_path", "calls": ["1", "ventoy_unix_comm_proc"], "variables": ["chosen_path", "1"], "commands": ["ventoy_unix_comm_proc"], "security_relevant": false}, "legacy_iso_menu_func": {"body": "set chosen_path=\"$2\"\n    \n    vt_select_auto_install \"${chosen_path", "calls": ["set", "vt_select_auto_install"], "variables": ["chosen_path", "2"], "commands": ["set", "vt_select_auto_install"], "security_relevant": false}, "legacy_iso_memdisk": {"body": "linux16   $vtoy_path/memdisk iso raw    \n    echo \"Loading ISO file to memory ...\"\n    initrd16  \"${1", "calls": ["ISO", "echo", "memdisk", "file", "iso", "to", "linux16", "Loading", "memory", "raw", "initrd16"], "variables": ["vtoy_path", "1"], "commands": ["echo", "initrd16", "linux16"], "security_relevant": false}, "iso_endless_os_proc": {"body": "if [ -d (loop)/ ]; then\n        loopback -d loop\n    fi\n\n    loopback loop \"${1", "calls": ["loop", "d", "loopback"], "variables": ["1"], "commands": ["if", "loopback", "fi"], "security_relevant": false}, "ventoy_iso_busybox_ver": {"body": "if [ \"$VTOY_EFI_ARCH\" = \"aa64\" ]; then\n        set ventoy_busybox_ver=a64\n    elif [ \"$VTOY_EFI_ARCH\" = \"mips\" ]; then\n        set ventoy_busybox_ver=m64\n    else\n        set ventoy_busybox_ver=32\n    \n        #special process for deepin-live iso\n        if [ \"$vt_chosen_size\" = \"403701760\" ]; then\n            if vt_str_str \"$vt_chosen_path\" \"/deepin-live\"; then\n                set ventoy_busybox_ver=64\n            fi\n        elif vt_str_begin \"$vt_volume_id\" \"PHOTON_\"; then\n            set ventoy_busybox_ver=64\n        elif vt_str_begin \"$vt_volume_id\" \"smgl-test-quinq-x86_64\"; then\n            set ventoy_busybox_ver=64\n        elif vt_str_begin \"$vt_volume_id\" \"LDiagBootable\"; then\n            set ventoy_busybox_ver=64\n        elif vt_str_begin \"$vt_volume_id\" \"KAOS_\"; then\n            set ventoy_busybox_ver=64\n\n        fi\n    fi", "calls": ["special", "vt_str_begin", "elif", "set", "32", "process", "m64", "iso", "64", "live", "vt_str_str", "a64"], "variables": ["VTOY_EFI_ARCH", "vt_chosen_path", "vt_volume_id", "vt_chosen_size"], "commands": ["set", "elif", "fi", "if", "else"], "security_relevant": false}, "iso_common_menuentry": {"body": "unset vt_system_id\n    unset vt_volume_id\n    \n    vt_chosen_img_path vt_chosen_path vt_chosen_size vt_chosen_name\n\n    vt_parse_iso_volume \"${vtoy_iso_part", "calls": ["unset", "vt_parse_iso_volume", "vt_chosen_path", "vt_volume_id", "vt_chosen_size", "vt_chosen_name", "vt_chosen_img_path", "vt_system_id"], "variables": ["vtoy_iso_part"], "commands": ["unset", "vt_parse_iso_volume", "vt_chosen_img_path"], "security_relevant": false}, "miso_common_menuentry": {"body": "vt_chosen_img_path vt_chosen_path vt_chosen_size vt_chosen_name\n\n    if vt_check_password \"${vt_chosen_path", "calls": ["vt_chosen_path", "vt_chosen_size", "vt_chosen_name", "vt_check_password", "vt_chosen_img_path"], "variables": ["vt_chosen_path"], "commands": ["if", "vt_chosen_img_path"], "security_relevant": true}, "common_unsupport_menuentry": {"body": "echo -e \"\\n The name of the iso file could NOT contain space or non-ascii characters. \\n\"\n    echo -e \" 文件名中不能有中文或空格 \\n\"    \n    echo -en \"\\n$VTLANG_ENTER_EXIT ...\"  \n    read vtInputKey", "calls": ["The", "could", "ascii", "of", "contain", "file", "文件名中不能有中文或空格", "n", "or", "e", "en", "VTLANG_ENTER_EXIT", "read", "space", "NOT", "name", "echo", "iso", "the"], "variables": ["VTLANG_ENTER_EXIT"], "commands": ["echo", "read"], "security_relevant": true}, "miso_unsupport_menuentry": {"body": "common_unsupport_menuentry", "calls": [], "variables": [], "commands": ["common_unsupport_menuentry"], "security_relevant": false}, "iso_unsupport_menuentry": {"body": "common_unsupport_menuentry", "calls": [], "variables": [], "commands": ["common_unsupport_menuentry"], "security_relevant": false}, "wim_common_menuentry": {"body": "vt_chosen_img_path vt_chosen_path vt_chosen_size vt_chosen_name\n    \n    if vt_check_password \"${vt_chosen_path", "calls": ["vt_chosen_path", "vt_chosen_size", "vt_chosen_name", "vt_check_password", "vt_chosen_img_path"], "variables": ["vt_chosen_path"], "commands": ["if", "vt_chosen_img_path"], "security_relevant": true}, "wim_unsupport_menuentry": {"body": "common_unsupport_menuentry", "calls": [], "variables": [], "commands": ["common_unsupport_menuentry"], "security_relevant": false}, "efi_common_menuentry": {"body": "vt_chosen_img_path vt_chosen_path vt_chosen_size vt_chosen_name\n    \n    if vt_check_password \"${vt_chosen_path", "calls": ["vt_chosen_path", "vt_chosen_size", "vt_chosen_name", "vt_check_password", "vt_chosen_img_path"], "variables": ["vt_chosen_path"], "commands": ["if", "vt_chosen_img_path"], "security_relevant": true}, "efi_unsupport_menuentry": {"body": "common_unsupport_menuentry", "calls": [], "variables": [], "commands": ["common_unsupport_menuentry"], "security_relevant": false}, "vhdboot_common_func": {"body": "vt_patch_vhdboot \"$1\"\n    \n    ventoy_debug_pause    \n    \n    if [ -n \"$vtoy_vhd_buf_addr\" ]; then\n        if [ \"$grub_platform\" = \"pc\" ]; then\n            ventoy_cli_console\n            linux16   $vtoy_path/memdisk iso raw    \n            initrd16  mem:${vtoy_vhd_buf_addr", "calls": ["vt_patch_vhdboot", "ventoy_cli_console", "memdisk", "n", "iso", "ventoy_debug_pause", "linux16", "raw", "initrd16"], "variables": ["vtoy_vhd_buf_addr", "1", "vtoy_path", "grub_platform"], "commands": ["vt_patch_vhdboot", "ventoy_cli_console", "if", "ventoy_debug_pause", "linux16", "initrd16"], "security_relevant": false}, "vhd_common_menuentry": {"body": "vt_chosen_img_path vt_chosen_path vt_chosen_size vt_chosen_name\n    \n    if vt_check_password \"${vt_chosen_path", "calls": ["vt_chosen_path", "vt_chosen_size", "vt_chosen_name", "vt_check_password", "vt_chosen_img_path"], "variables": ["vt_chosen_path"], "commands": ["if", "vt_chosen_img_path"], "security_relevant": true}, "vhd_unsupport_menuentry": {"body": "common_unsupport_menuentry", "calls": [], "variables": [], "commands": ["common_unsupport_menuentry"], "security_relevant": false}, "vtoyboot_common_func": {"body": "set AltBootPart=0\n    set vtoysupport=0\n    \n    vt_get_vtoy_type \"${1", "calls": ["vt_get_vtoy_type", "set", "0"], "variables": ["1"], "commands": ["vt_get_vtoy_type", "set"], "security_relevant": false}, "vtoy_common_menuentry": {"body": "vt_chosen_img_path vt_chosen_path vt_chosen_size vt_chosen_name\n    \n    if vt_check_password \"${vt_chosen_path", "calls": ["vt_chosen_path", "vt_chosen_size", "vt_chosen_name", "vt_check_password", "vt_chosen_img_path"], "variables": ["vt_chosen_path"], "commands": ["if", "vt_chosen_img_path"], "security_relevant": true}, "vtoy_unsupport_menuentry": {"body": "common_unsupport_menuentry", "calls": [], "variables": [], "commands": ["common_unsupport_menuentry"], "security_relevant": false}, "only_uefi_tip": {"body": "echo -e \"\\n This IMG file is only supported in UEFI mode. \\n\"\n    echo -en \"\\n$VTLANG_ENTER_EXIT ...\"\n    read vtInputKey", "calls": ["e", "en", "VTLANG_ENTER_EXIT", "UEFI", "echo", "IMG", "file", "n", "in", "This", "supported", "is", "only", "read"], "variables": ["VTLANG_ENTER_EXIT"], "commands": ["echo", "read"], "security_relevant": true}, "ventoy_img_easyos": {"body": "vt_load_cpio  $vtoy_path  \"${vt_chosen_path", "calls": ["vtoy_path", "vt_load_cpio"], "variables": ["vtoy_path", "vt_chosen_path"], "commands": ["vt_load_cpio"], "security_relevant": false}, "ventoy_img_easyos2": {"body": "vt_load_cpio  $vtoy_path  \"${vt_chosen_path", "calls": ["vtoy_path", "vt_load_cpio"], "variables": ["vtoy_path", "vt_chosen_path"], "commands": ["vt_load_cpio"], "security_relevant": false}, "ventoy_img_volumio": {"body": "vt_load_cpio  $vtoy_path  \"${vt_chosen_path", "calls": ["vtoy_path", "vt_load_cpio"], "variables": ["vtoy_path", "vt_chosen_path"], "commands": ["vt_load_cpio"], "security_relevant": false}, "ventoy_img_openelec": {"body": "elec_ver=$1\n    \n    vt_load_cpio  $vtoy_path  \"${vt_chosen_path", "calls": ["vtoy_path", "1", "vt_load_cpio"], "variables": ["vtoy_path", "1", "vt_chosen_path"], "commands": ["elec_ver=$1", "vt_load_cpio"], "security_relevant": false}, "ventoy_img_freedombox": {"body": "vt_load_cpio  $vtoy_path  \"${vt_chosen_path", "calls": ["vtoy_path", "vt_load_cpio"], "variables": ["vtoy_path", "vt_chosen_path"], "commands": ["vt_load_cpio"], "security_relevant": false}, "ventoy_img_paldo": {"body": "vt_load_cpio  $vtoy_path  \"${vt_chosen_path", "calls": ["vtoy_path", "vt_load_cpio"], "variables": ["vtoy_path", "vt_chosen_path"], "commands": ["vt_load_cpio"], "security_relevant": false}, "ventoy_img_ubos": {"body": "vt_load_cpio  $vtoy_path  \"${vt_chosen_path", "calls": ["vtoy_path", "vt_load_cpio"], "variables": ["vtoy_path", "vt_chosen_path"], "commands": ["vt_load_cpio"], "security_relevant": false}, "ventoy_img_recalbox": {"body": "if [ $vtoy_img_max_part_end -GT $vt_chosen_size ]; then\n        echo -e \"\\nPlease extend the img file size before boot it. \\n\"\n        ventoy_pause\n        return\n    fi\n\n    vt_load_cpio  $vtoy_path  \"${vt_chosen_path", "calls": ["e", "nPlease", "vtoy_img_max_part_end", "img", "boot", "echo", "file", "vt_chosen_size", "ventoy_pause", "size", "vtoy_path", "the", "before", "return", "vt_load_cpio", "extend", "GT"], "variables": ["vt_chosen_size", "vtoy_path", "vt_chosen_path", "vtoy_img_max_part_end"], "commands": ["echo", "fi", "if", "ventoy_pause", "return", "vt_load_cpio"], "security_relevant": false}, "ventoy_img_esysrescue": {"body": "vt_load_cpio  $vtoy_path  \"${vt_chosen_path", "calls": ["vtoy_path", "vt_load_cpio"], "variables": ["vtoy_path", "vt_chosen_path"], "commands": ["vt_load_cpio"], "security_relevant": false}, "ventoy_img_batocera": {"body": "vt_load_cpio  $vtoy_path  \"${vt_chosen_path", "calls": ["vtoy_path", "vt_load_cpio"], "variables": ["vtoy_path", "vt_chosen_path"], "commands": ["vt_load_cpio"], "security_relevant": false}, "ventoy_img_openwrt": {"body": "if [ -e (vtimghd,2)/lib64 ]; then\n        set ventoy_busybox_ver=64\n    fi\n\n    vt_fs_enum_1st_dir (vtimghd,2) /lib/modules/ vt_dir_name\n\n    if [ -f (vtimghd,2)/lib/modules/$vt_dir_name/dm-mod.ko ]; then\n        set openwrt_plugin_need=0\n        vt_img_extra_initrd_append  (vtimghd,2)/lib/modules/$vt_dir_name/dm-mod.ko\n        if [ -f (vtimghd,2)/lib/modules/$vt_dir_name/dax.ko ]; then\n            vt_img_extra_initrd_append  (vtimghd,2)/lib/modules/$vt_dir_name/dax.ko\n        fi\n    else\n        set openwrt_plugin_need=1\n        if [ ! -f ${vtoy_iso_part", "calls": ["lib64", "e", "vt_dir_name", "vt_fs_enum_1st_dir", "vt_img_extra_initrd_append", "f", "set", "1", "64", "ko", "0"], "variables": ["vtoy_iso_part", "vt_dir_name"], "commands": ["vt_fs_enum_1st_dir", "vt_img_extra_initrd_append", "set", "fi", "if", "else"], "security_relevant": false}, "ventoy_img_tails": {"body": "vt_load_cpio  $vtoy_path  \"${vt_chosen_path", "calls": ["vtoy_path", "vt_load_cpio"], "variables": ["vtoy_path", "vt_chosen_path"], "commands": ["vt_load_cpio"], "security_relevant": false}, "ventoy_img_fydeos": {"body": "if [ \"$grub_platform\" = \"pc\" ]; then\n        only_uefi_tip\n        return\n    fi\n\n    vt_load_cpio  $vtoy_path  \"${vt_chosen_path", "calls": ["only_uefi_tip", "vt_load_cpio", "vtoy_path", "return"], "variables": ["vtoy_path", "vt_chosen_path", "grub_platform"], "commands": ["fi", "if", "vt_load_cpio", "return", "only_uefi_tip"], "security_relevant": false}, "ventoy_img_cloudready": {"body": "if [ \"$grub_platform\" = \"pc\" ]; then\n        only_uefi_tip\n        return\n    fi\n\n    vt_load_cpio  $vtoy_path  \"${vt_chosen_path", "calls": ["only_uefi_tip", "vt_load_cpio", "vtoy_path", "return"], "variables": ["vtoy_path", "vt_chosen_path", "grub_platform"], "commands": ["fi", "if", "vt_load_cpio", "return", "only_uefi_tip"], "security_relevant": false}, "ventoy_img_fwts": {"body": "vt_load_cpio  $vtoy_path  \"${vt_chosen_path", "calls": ["vtoy_path", "vt_load_cpio"], "variables": ["vtoy_path", "vt_chosen_path"], "commands": ["vt_load_cpio"], "security_relevant": false}, "ventoy_img_memtest86": {"body": "chainloader (vtimghd,1)/efi/boot/BOOTX64.efi\n    boot", "calls": ["chainloader", "efi"], "variables": [], "commands": ["boot", "chainloader"], "security_relevant": false}, "img_unsupport_tip": {"body": "echo -e \"\\n This IMG file is NOT supported now. \\n\"\n    echo -e \" 当前不支持启动此 IMG 文件 \\n\"    \n    echo -en \"\\n$VTLANG_ENTER_EXIT ...\"\n    read vtInputKey", "calls": ["e", "en", "VTLANG_ENTER_EXIT", "NOT", "echo", "当前不支持启动此", "IMG", "file", "n", "文件", "This", "supported", "is", "read"], "variables": ["VTLANG_ENTER_EXIT"], "commands": ["echo", "read"], "security_relevant": true}, "legacy_img_memdisk": {"body": "linux16   $vtoy_path/memdisk\n    echo \"Loading img file to memory ...\"\n    initrd16  \"${1", "calls": ["img", "echo", "memdisk", "file", "to", "Loading", "linux16", "memory", "initrd16"], "variables": ["vtoy_path", "1"], "commands": ["echo", "initrd16", "linux16"], "security_relevant": false}, "img_common_menuentry": {"body": "set ventoy_compatible=YES\n    set ventoy_busybox_ver=32\n    unset LoadIsoEfiDriver\n\n    vt_chosen_img_path vt_chosen_path vt_chosen_size vt_chosen_name\n    \n    if vt_check_password \"${vt_chosen_path", "calls": ["unset", "vt_chosen_path", "YES", "set", "32", "vt_chosen_size", "LoadIsoEfiDriver", "vt_chosen_name", "vt_check_password", "vt_chosen_img_path"], "variables": ["vt_chosen_path"], "commands": ["unset", "if", "set", "vt_chosen_img_path"], "security_relevant": true}, "img_unsupport_menuentry": {"body": "common_unsupport_menuentry", "calls": [], "variables": [], "commands": ["common_unsupport_menuentry"], "security_relevant": false}, "mimg_common_menuentry": {"body": "vt_chosen_img_path vt_chosen_path vt_chosen_size vt_chosen_name\n\n    if vt_check_password \"${vt_chosen_path", "calls": ["vt_chosen_path", "vt_chosen_size", "vt_chosen_name", "vt_check_password", "vt_chosen_img_path"], "variables": ["vt_chosen_path"], "commands": ["if", "vt_chosen_img_path"], "security_relevant": true}}, "boot_logic": {"os_detection": ["Windows detection", "Linux detection", "Unix detection", "FreeBSD detection", "DragonFly detection"], "boot_methods": ["EFI chainloading", "Legacy Linux boot", "Legacy initrd loading", "Windows WIM boot", "Memory disk boot"], "compatibility_checks": [], "file_operations": []}, "security_features": [{"feature": "Compatibility verification", "occurrences": 8, "pattern": "vt_check_compatible"}, {"feature": "Ventoy compatibility check", "occurrences": 15, "pattern": "ventoy_compatible"}, {"feature": "Secure boot related", "occurrences": 1, "pattern": "secure.*boot"}, {"feature": "Integrity checking", "occurrences": 3, "pattern": "hash|checksum"}]}, "grub/ventoy_grub.cfg": {"functions": {}, "boot_logic": {"os_detection": [], "boot_methods": ["EFI chainloading"], "compatibility_checks": [], "file_operations": []}, "security_features": []}}, "timestamp": "1754133116.5628097", "analysis_summary": {"efi_files_analyzed": 4, "grub_configs_analyzed": 2}}