#!/usr/bin/env python3
"""
全面的EFI启动文件和GRUB配置分析工具
Comprehensive EFI Boot File and GRUB Configuration Analysis Tool
"""

import os
import sys
import struct
import re
import hashlib
import json
from pathlib import Path
from typing import Dict, List, Any, Optional, Tuple
import binascii

class EFIAnalyzer:
    """EFI文件分析器"""
    
    def __init__(self, file_path: str):
        self.file_path = file_path
        self.file_data = None
        self.pe_header = None
        self.sections = []
        self.imports = []
        self.exports = []
        
    def load_file(self) -> bool:
        """加载EFI文件"""
        try:
            with open(self.file_path, 'rb') as f:
                self.file_data = f.read()
            return True
        except Exception as e:
            print(f"Error loading file {self.file_path}: {e}")
            return False
    
    def analyze_pe_structure(self) -> Dict[str, Any]:
        """分析PE结构"""
        if not self.file_data:
            return {}
        
        analysis = {
            'file_size': len(self.file_data),
            'file_hash': hashlib.sha256(self.file_data).hexdigest(),
            'pe_signature': None,
            'machine_type': None,
            'sections': [],
            'entry_point': None,
            'subsystem': None
        }
        
        # 检查DOS头
        if len(self.file_data) < 64:
            return analysis
        
        dos_header = struct.unpack('<H', self.file_data[:2])[0]
        if dos_header != 0x5A4D:  # 'MZ'
            analysis['error'] = 'Invalid DOS signature'
            return analysis
        
        # 获取PE头偏移
        pe_offset = struct.unpack('<L', self.file_data[60:64])[0]
        
        if pe_offset + 4 > len(self.file_data):
            analysis['error'] = 'Invalid PE offset'
            return analysis
        
        # 检查PE签名
        pe_signature = struct.unpack('<L', self.file_data[pe_offset:pe_offset+4])[0]
        analysis['pe_signature'] = hex(pe_signature)
        
        if pe_signature != 0x00004550:  # 'PE\0\0'
            analysis['error'] = 'Invalid PE signature'
            return analysis
        
        # 分析COFF头
        coff_header_offset = pe_offset + 4
        if coff_header_offset + 20 > len(self.file_data):
            return analysis

        try:
            coff_header = struct.unpack('<HHLLHH',
                                       self.file_data[coff_header_offset:coff_header_offset+20])

            analysis['machine_type'] = hex(coff_header[0])
            analysis['number_of_sections'] = coff_header[1]
            analysis['timestamp'] = coff_header[2]
            analysis['optional_header_size'] = coff_header[5]
        except struct.error as e:
            analysis['error'] = f'COFF header parsing error: {e}'
            # 尝试获取基本信息
            if coff_header_offset + 6 <= len(self.file_data):
                machine_type, num_sections = struct.unpack('<HH',
                                                         self.file_data[coff_header_offset:coff_header_offset+4])
                analysis['machine_type'] = hex(machine_type)
                analysis['number_of_sections'] = num_sections
            return analysis
        
        # 分析可选头
        optional_header_offset = coff_header_offset + 20
        if optional_header_offset + 2 > len(self.file_data):
            return analysis

        try:
            magic = struct.unpack('<H', self.file_data[optional_header_offset:optional_header_offset+2])[0]
            analysis['pe_type'] = 'PE32+' if magic == 0x20b else 'PE32'

            if magic == 0x20b:  # PE32+
                if optional_header_offset + 24 > len(self.file_data):
                    return analysis
                entry_point = struct.unpack('<L', self.file_data[optional_header_offset+16:optional_header_offset+20])[0]
                analysis['entry_point'] = hex(entry_point)

                if optional_header_offset + 68 > len(self.file_data):
                    return analysis
                subsystem = struct.unpack('<H', self.file_data[optional_header_offset+68:optional_header_offset+70])[0]
                analysis['subsystem'] = subsystem
            elif magic == 0x10b:  # PE32
                if optional_header_offset + 28 > len(self.file_data):
                    return analysis
                entry_point = struct.unpack('<L', self.file_data[optional_header_offset+16:optional_header_offset+20])[0]
                analysis['entry_point'] = hex(entry_point)

                if optional_header_offset + 68 > len(self.file_data):
                    return analysis
                subsystem = struct.unpack('<H', self.file_data[optional_header_offset+68:optional_header_offset+70])[0]
                analysis['subsystem'] = subsystem
        except struct.error as e:
            analysis['optional_header_error'] = f'Optional header parsing error: {e}'
        
        # 分析节表
        if 'optional_header_size' in analysis and 'number_of_sections' in analysis:
            section_table_offset = optional_header_offset + analysis['optional_header_size']
            for i in range(min(analysis['number_of_sections'], 20)):  # 限制最多20个节
                section_offset = section_table_offset + i * 40
                if section_offset + 40 > len(self.file_data):
                    break

                try:
                    section_data = self.file_data[section_offset:section_offset+40]
                    name = section_data[:8].rstrip(b'\x00').decode('ascii', errors='ignore')
                    virtual_size, virtual_address, raw_size, raw_address = struct.unpack('<LLLL', section_data[8:24])
                    characteristics = struct.unpack('<L', section_data[36:40])[0]

                    section_info = {
                        'name': name,
                        'virtual_size': virtual_size,
                        'virtual_address': hex(virtual_address),
                        'raw_size': raw_size,
                        'raw_address': hex(raw_address),
                        'characteristics': hex(characteristics)
                    }
                    analysis['sections'].append(section_info)
                except (struct.error, UnicodeDecodeError) as e:
                    analysis['section_error'] = f'Section {i} parsing error: {e}'
                    break
        
        return analysis
    
    def extract_strings(self, min_length: int = 4) -> List[str]:
        """提取字符串"""
        if not self.file_data:
            return []
        
        strings = []
        current_string = ""
        
        for byte in self.file_data:
            if 32 <= byte <= 126:  # 可打印ASCII字符
                current_string += chr(byte)
            else:
                if len(current_string) >= min_length:
                    strings.append(current_string)
                current_string = ""
        
        if len(current_string) >= min_length:
            strings.append(current_string)
        
        return strings
    
    def analyze_imports(self) -> List[Dict[str, Any]]:
        """分析导入表"""
        # 这里需要更复杂的PE导入表解析逻辑
        # 简化版本，仅提取可能的函数名
        strings = self.extract_strings()
        potential_imports = []
        
        # 常见的EFI函数模式
        efi_patterns = [
            r'.*Protocol.*',
            r'.*Service.*',
            r'.*Boot.*',
            r'.*Runtime.*',
            r'.*System.*',
            r'.*Handle.*',
            r'.*Image.*',
            r'.*Device.*',
            r'.*File.*'
        ]
        
        for string in strings:
            for pattern in efi_patterns:
                if re.match(pattern, string, re.IGNORECASE):
                    potential_imports.append({
                        'name': string,
                        'type': 'potential_efi_function'
                    })
                    break
        
        return potential_imports

class GRUBConfigAnalyzer:
    """GRUB配置分析器"""
    
    def __init__(self, config_path: str):
        self.config_path = config_path
        self.config_content = ""
        self.functions = {}
        self.variables = {}
        self.menu_entries = []
        self.security_features = []
        
    def load_config(self) -> bool:
        """加载配置文件"""
        try:
            with open(self.config_path, 'r', encoding='utf-8') as f:
                self.config_content = f.read()
            return True
        except Exception as e:
            print(f"Error loading config {self.config_path}: {e}")
            return False
    
    def analyze_functions(self) -> Dict[str, Dict[str, Any]]:
        """分析函数定义"""
        function_pattern = r'function\s+(\w+)\s*\{([^}]*)\}'
        functions = {}
        
        for match in re.finditer(function_pattern, self.config_content, re.MULTILINE | re.DOTALL):
            func_name = match.group(1)
            func_body = match.group(2).strip()
            
            # 分析函数体
            functions[func_name] = {
                'body': func_body,
                'calls': self._extract_function_calls(func_body),
                'variables': self._extract_variables(func_body),
                'commands': self._extract_commands(func_body),
                'security_relevant': self._is_security_relevant(func_body)
            }
        
        return functions
    
    def _extract_function_calls(self, text: str) -> List[str]:
        """提取函数调用"""
        call_pattern = r'(\w+)\s*(?:\(|\s)'
        calls = []
        for match in re.finditer(call_pattern, text):
            call = match.group(1)
            if call not in ['if', 'then', 'else', 'fi', 'for', 'do', 'done', 'while']:
                calls.append(call)
        return list(set(calls))
    
    def _extract_variables(self, text: str) -> List[str]:
        """提取变量"""
        var_pattern = r'\$\{?(\w+)\}?'
        variables = []
        for match in re.finditer(var_pattern, text):
            variables.append(match.group(1))
        return list(set(variables))
    
    def _extract_commands(self, text: str) -> List[str]:
        """提取命令"""
        lines = text.split('\n')
        commands = []
        for line in lines:
            line = line.strip()
            if line and not line.startswith('#'):
                cmd = line.split()[0] if line.split() else ''
                if cmd:
                    commands.append(cmd)
        return list(set(commands))
    
    def _is_security_relevant(self, text: str) -> bool:
        """检查是否与安全相关"""
        security_keywords = [
            'password', 'auth', 'secure', 'verify', 'check', 'validate',
            'signature', 'hash', 'crypto', 'key', 'cert', 'trust'
        ]
        text_lower = text.lower()
        return any(keyword in text_lower for keyword in security_keywords)
    
    def analyze_boot_logic(self) -> Dict[str, Any]:
        """分析启动逻辑"""
        boot_logic = {
            'os_detection': [],
            'boot_methods': [],
            'compatibility_checks': [],
            'file_operations': []
        }
        
        # 检测操作系统识别逻辑
        os_patterns = [
            (r'vtoy_os=Windows', 'Windows detection'),
            (r'vtoy_os=Linux', 'Linux detection'),
            (r'vtoy_os=Unix', 'Unix detection'),
            (r'FreeBSD', 'FreeBSD detection'),
            (r'DragonFly', 'DragonFly detection')
        ]
        
        for pattern, description in os_patterns:
            if re.search(pattern, self.config_content):
                boot_logic['os_detection'].append(description)
        
        # 检测启动方法
        boot_methods = [
            (r'chainloader', 'EFI chainloading'),
            (r'linux16', 'Legacy Linux boot'),
            (r'initrd16', 'Legacy initrd loading'),
            (r'wimboot', 'Windows WIM boot'),
            (r'memdisk', 'Memory disk boot')
        ]
        
        for pattern, description in boot_methods:
            if re.search(pattern, self.config_content):
                boot_logic['boot_methods'].append(description)
        
        return boot_logic
    
    def analyze_security_features(self) -> List[Dict[str, Any]]:
        """分析安全特性"""
        security_features = []
        
        # 检查各种安全相关功能
        security_checks = [
            (r'vt_check_compatible', 'Compatibility verification'),
            (r'ventoy_compatible', 'Ventoy compatibility check'),
            (r'signature', 'Signature verification'),
            (r'secure.*boot', 'Secure boot related'),
            (r'hash|checksum', 'Integrity checking'),
            (r'verify|validation', 'Verification process')
        ]
        
        for pattern, description in security_checks:
            matches = re.findall(pattern, self.config_content, re.IGNORECASE)
            if matches:
                security_features.append({
                    'feature': description,
                    'occurrences': len(matches),
                    'pattern': pattern
                })
        
        return security_features

def main():
    """主函数"""
    print("=== 全面EFI启动文件和GRUB配置分析工具 ===")
    print("=== Comprehensive EFI Boot and GRUB Analysis Tool ===\n")
    
    # 分析EFI文件
    efi_files = [
        "EFI/BOOT/BOOTX64.EFI",
        "EFI/BOOT/grub.efi",
        "EFI/BOOT/BOOTAA64.EFI",
        "EFI/BOOT/BOOTIA32.EFI"
    ]
    
    efi_analysis = {}
    for efi_file in efi_files:
        if os.path.exists(efi_file):
            print(f"分析EFI文件: {efi_file}")
            analyzer = EFIAnalyzer(efi_file)
            if analyzer.load_file():
                efi_analysis[efi_file] = {
                    'pe_structure': analyzer.analyze_pe_structure(),
                    'strings': analyzer.extract_strings()[:50],  # 限制字符串数量
                    'imports': analyzer.analyze_imports()[:20]   # 限制导入数量
                }
    
    # 分析GRUB配置
    grub_configs = [
        "grub/grub.cfg",
        "grub/ventoy_grub.cfg"
    ]
    
    grub_analysis = {}
    for config_file in grub_configs:
        if os.path.exists(config_file):
            print(f"分析GRUB配置: {config_file}")
            analyzer = GRUBConfigAnalyzer(config_file)
            if analyzer.load_config():
                grub_analysis[config_file] = {
                    'functions': analyzer.analyze_functions(),
                    'boot_logic': analyzer.analyze_boot_logic(),
                    'security_features': analyzer.analyze_security_features()
                }
    
    # 保存分析结果
    results = {
        'efi_analysis': efi_analysis,
        'grub_analysis': grub_analysis,
        'timestamp': str(os.path.getctime('.')),
        'analysis_summary': {
            'efi_files_analyzed': len(efi_analysis),
            'grub_configs_analyzed': len(grub_analysis)
        }
    }
    
    with open('comprehensive_analysis_results.json', 'w', encoding='utf-8') as f:
        json.dump(results, f, indent=2, ensure_ascii=False)
    
    print(f"\n分析完成！结果已保存到 comprehensive_analysis_results.json")
    return results

if __name__ == "__main__":
    main()
