#!/usr/bin/env python3
import struct

def comprehensive_boot_efi_analysis(filename):
    """对BOOT.EFI进行全方位深度分析"""
    
    try:
        with open(filename, 'rb') as f:
            data = f.read()
            
            print("=== BOOT.EFI 全方位深度分析 ===")
            print(f"文件大小: {len(data)} 字节\n")
            
            # 1. PE文件头分析
            print("1. PE文件结构分析:")
            if data[:2] == b'MZ':
                pe_offset = struct.unpack('<I', data[0x3C:0x40])[0]
                print(f"  PE头偏移: 0x{pe_offset:04X}")
                
                if data[pe_offset:pe_offset+4] == b'PE\x00\x00':
                    machine = struct.unpack('<H', data[pe_offset+4:pe_offset+6])[0]
                    sections = struct.unpack('<H', data[pe_offset+6:pe_offset+8])[0]
                    print(f"  机器类型: 0x{machine:04X}")
                    print(f"  节数量: {sections}")
            
            # 2. 搜索所有可读字符串
            print(f"\n2. 提取所有可读字符串 (长度>=4):")
            strings_found = []
            current_string = ""
            
            for i, byte in enumerate(data):
                if 32 <= byte <= 126:  # 可打印ASCII
                    current_string += chr(byte)
                else:
                    if len(current_string) >= 4:
                        strings_found.append((i - len(current_string), current_string))
                    current_string = ""
            
            # 最后检查一次
            if len(current_string) >= 4:
                strings_found.append((len(data) - len(current_string), current_string))
            
            print(f"  找到 {len(strings_found)} 个字符串:")
            for offset, string in strings_found[:50]:  # 只显示前50个
                print(f"    0x{offset:04X}: {string}")
            
            if len(strings_found) > 50:
                print(f"    ... 还有 {len(strings_found) - 50} 个字符串")
            
            # 3. 搜索特定的验证相关字符串
            print(f"\n3. 验证相关字符串详细分析:")
            verification_keywords = [
                b"check", b"verify", b"valid", b"fail", b"error", 
                b"MBR", b"disk", b"device", b"USB", b"hardware",
                b"General_UDisk", b"Silicon-Power", b"VID", b"PID",
                b"VTOY", b"Ventoy", b"DmarInsert", b"DMAR", b"ACPI",
                b"12", b"(12)", b"WARNING", b"NOT", b"standard"
            ]
            
            for keyword in verification_keywords:
                positions = []
                start = 0
                while True:
                    pos = data.find(keyword, start)
                    if pos == -1:
                        break
                    positions.append(pos)
                    start = pos + 1
                
                if positions:
                    print(f"  '{keyword.decode('ascii', errors='ignore')}' 出现 {len(positions)} 次:")
                    for pos in positions[:5]:  # 最多显示5个位置
                        # 显示上下文
                        context_start = max(0, pos - 30)
                        context_end = min(len(data), pos + len(keyword) + 30)
                        context = data[context_start:context_end]
                        ascii_context = ''.join(chr(b) if 32 <= b <= 126 else '.' for b in context)
                        print(f"    0x{pos:04X}: {ascii_context}")
                    if len(positions) > 5:
                        print(f"    ... 还有 {len(positions) - 5} 个位置")
            
            # 4. 搜索函数调用和跳转
            print(f"\n4. 函数调用分析:")
            call_instructions = []
            
            for i in range(len(data) - 5):
                # call near (E8)
                if data[i] == 0xE8:
                    offset = struct.unpack('<i', data[i+1:i+5])[0]
                    target = (i + 5 + offset) & 0xFFFFFFFF
                    call_instructions.append(('call', i, target))
                
                # jmp near (E9)
                elif data[i] == 0xE9:
                    offset = struct.unpack('<i', data[i+1:i+5])[0]
                    target = (i + 5 + offset) & 0xFFFFFFFF
                    call_instructions.append(('jmp', i, target))
            
            print(f"  找到 {len(call_instructions)} 个call/jmp指令")
            print("  前20个:")
            for instr_type, addr, target in call_instructions[:20]:
                print(f"    0x{addr:04X}: {instr_type} -> 0x{target:04X}")
            
            # 5. 搜索立即数12 (错误代码)
            print(f"\n5. 错误代码12的使用分析:")
            error_12_patterns = [
                (b'\xB8\x0C\x00\x00\x00', 'mov eax, 12'),
                (b'\x48\xC7\xC0\x0C\x00\x00\x00', 'mov rax, 12'),
                (b'\x83\xF8\x0C', 'cmp eax, 12'),
                (b'\x48\x83\xF8\x0C', 'cmp rax, 12'),
                (b'\x6A\x0C', 'push 12'),
                (b'\xB9\x0C\x00\x00\x00', 'mov ecx, 12')
            ]
            
            for pattern, description in error_12_patterns:
                positions = []
                start = 0
                while True:
                    pos = data.find(pattern, start)
                    if pos == -1:
                        break
                    positions.append(pos)
                    start = pos + 1
                
                if positions:
                    print(f"  {description}: {len(positions)} 次")
                    for pos in positions:
                        context = data[max(0, pos-10):pos+len(pattern)+10]
                        print(f"    0x{pos:04X}: {context.hex().upper()}")
            
            # 6. 搜索可能的硬件ID比较
            print(f"\n6. 硬件ID比较分析:")
            
            # 搜索你提供的硬件ID
            hardware_ids = [
                b"4C35A0E2",  # MBR签名
                b"33125640-96BD-44FE-B53F-644C9F6D6924",  # GUID
                b"General_UDisk",
                b"Silicon-Power8G"
            ]
            
            for hw_id in hardware_ids:
                pos = data.find(hw_id)
                if pos != -1:
                    print(f"  硬件ID '{hw_id.decode('ascii', errors='ignore')}' @ 0x{pos:04X}")
                    context = data[max(0, pos-50):pos+len(hw_id)+50]
                    ascii_context = ''.join(chr(b) if 32 <= b <= 126 else '.' for b in context)
                    print(f"    上下文: {ascii_context}")
            
            # 7. UTF-16字符串搜索
            print(f"\n7. UTF-16字符串搜索:")
            utf16_keywords = [
                "check", "verify", "MBR", "device", "error", 
                "General_UDisk", "WARNING", "NOT"
            ]
            
            for keyword in utf16_keywords:
                utf16_pattern = keyword.encode('utf-16le')
                pos = data.find(utf16_pattern)
                if pos != -1:
                    print(f"  UTF-16 '{keyword}' @ 0x{pos:04X}")
                    context = data[max(0, pos-30):pos+len(utf16_pattern)+30]
                    print(f"    上下文: {context.hex().upper()}")
            
            # 8. 入口点分析
            print(f"\n8. 可能的入口点和函数开始:")
            function_starts = []
            
            for i in range(len(data) - 10):
                # 寻找函数序言模式
                if data[i:i+3] == b'\x55\x48\x89':  # push rbp; mov rbp, rsp
                    function_starts.append(i)
                elif data[i:i+4] == b'\x48\x83\xEC':  # sub rsp, imm
                    function_starts.append(i)
                elif data[i:i+2] == b'\x48\x83' and data[i+2] == 0xEC:  # sub rsp, imm8
                    function_starts.append(i)
            
            print(f"  找到 {len(function_starts)} 个可能的函数开始")
            print("  前15个:")
            for addr in function_starts[:15]:
                context = data[addr:addr+16]
                print(f"    0x{addr:04X}: {context.hex().upper()}")
            
            print(f"\n=== BOOT.EFI 全方位分析完成 ===")
            
    except FileNotFoundError:
        print(f"文件未找到: {filename}")
    except Exception as e:
        print(f"分析过程中出错: {e}")

if __name__ == "__main__":
    filename = r"D:\新建文件夹 (2)\新建文件夹\EFI\BOOT\BOOT.EFI"
    comprehensive_boot_efi_analysis(filename)