#!/usr/bin/env python3

def create_fake_dmar_table():
    """创建一个示例DMAR表用于比较"""
    # 标准DMAR表结构
    dmar_header = bytearray(48)
    
    # ACPI表头
    dmar_header[0:4] = b'DMAR'           # 签名
    dmar_header[4:8] = (48).to_bytes(4, 'little')  # 长度
    dmar_header[8] = 1                   # 修订版本
    dmar_header[9] = 0                   # 校验和（稍后计算）
    dmar_header[10:16] = b'INTEL '       # OEM ID
    dmar_header[16:24] = b'EDK2    '     # OEM Table ID
    dmar_header[24:28] = (0x20151124).to_bytes(4, 'little')  # OEM修订
    dmar_header[28:32] = b'INTL'         # 创建者ID
    dmar_header[32:36] = (0x20120913).to_bytes(4, 'little')  # 创建者修订
    
    # DMAR特定字段
    dmar_header[36] = 39                 # Host Address Width (39位)
    dmar_header[37] = 0x01               # Flags (INTR_REMAP)
    dmar_header[38:48] = b'\x00' * 10    # 保留字段
    
    return bytes(dmar_header)

def search_embedded_tables(data):
    """搜索可能嵌入的ACPI表或DMAR结构"""
    
    print("=== Deep Binary Analysis ===")
    
    # 搜索可能的ACPI表结构模式
    patterns_to_search = [
        # 标准ACPI签名后跟合理的长度值
        (b'DMAR', "DMAR table signature"),
        (b'ACPI', "ACPI string"),
        (b'INTEL', "Intel OEM ID"),
        (b'EDK2', "EDK2 identifier"),
        (b'\x01\x00\x00\x00', "Revision 1"),
        # VT-d相关字符串
        (b'VTD', "VT-d reference"),
        (b'IOMMU', "IOMMU reference"),
        (b'DMA', "DMA reference"),
    ]
    
    for pattern, description in patterns_to_search:
        pos = 0
        count = 0
        print(f"\nSearching for {description} ({pattern}):")
        while True:
            pos = data.find(pattern, pos)
            if pos == -1:
                break
            print(f"  Found at offset 0x{pos:x}")
            
            # 显示周围32字节的上下文
            start = max(0, pos - 16)
            end = min(len(data), pos + 32)
            context = data[start:end]
            hex_repr = ' '.join(f'{b:02x}' for b in context)
            ascii_repr = ''.join(chr(b) if 32 <= b <= 126 else '.' for b in context)
            print(f"    {start:08x}: {hex_repr}")
            print(f"    ASCII: {ascii_repr}")
            
            pos += 1
            count += 1
            if count > 5:  # 限制每种模式的搜索结果
                print("    ... (more results truncated)")
                break
    
    # 检查是否有完整的表结构
    print(f"\n=== Checking for Complete Table Structures ===")
    
    # 创建示例DMAR表进行比较
    fake_dmar = create_fake_dmar_table()
    print(f"Example DMAR table structure:")
    for i in range(0, len(fake_dmar), 16):
        hex_bytes = ' '.join(f'{b:02x}' for b in fake_dmar[i:i+16])
        ascii_repr = ''.join(chr(b) if 32 <= b <= 126 else '.' for b in fake_dmar[i:i+16])
        print(f'  {i:08x}: {hex_bytes:<48} {ascii_repr}')
    
    # 搜索可能的表结构（基于4字节对齐和常见模式）
    print(f"\n=== Scanning for Table-like Structures ===")
    
    table_candidates = []
    for i in range(0, len(data) - 48, 4):  # 4字节对齐
        chunk = data[i:i+8]
        
        # 检查是否看起来像表头
        # 长度字段应该在合理范围内
        if len(chunk) >= 8:
            potential_length = int.from_bytes(chunk[4:8], byteorder='little')
            if 36 <= potential_length <= 1024:  # ACPI表的合理长度范围
                # 检查是否有足够的数据
                if i + potential_length <= len(data):
                    table_candidates.append((i, potential_length))
    
    print(f"Found {len(table_candidates)} potential table structures:")
    for offset, length in table_candidates[:10]:  # 只显示前10个
        print(f"  Offset 0x{offset:x}, Length: {length}")
        
        # 显示表头
        header = data[offset:offset+min(48, length)]
        signature = header[0:4] if len(header) >= 4 else b''
        try:
            sig_str = signature.decode('ascii', errors='ignore')
        except:
            sig_str = "???"
        
        print(f"    Signature: {signature} ({sig_str})")
        
        # 显示前48字节
        for j in range(0, min(48, len(header)), 16):
            hex_bytes = ' '.join(f'{b:02x}' for b in header[j:j+16])
            ascii_repr = ''.join(chr(b) if 32 <= b <= 126 else '.' for b in header[j:j+16])
            print(f'    {offset+j:08x}: {hex_bytes:<48} {ascii_repr}')
        print()

def check_manual_construction(data):
    """检查是否有手工构造的DMAR数据"""
    print("=== Checking for Manual DMAR Construction ===")
    
    # 查找典型的DMAR表构造代码模式
    construction_patterns = [
        b'DmarInsert',      # 我们在之前找到的字符串
        b'DMAR\x00\x00\x00',  # DMAR + 空字节
        b'\x44\x4d\x41\x52',  # DMAR的十六进制
    ]
    
    for pattern in construction_patterns:
        pos = data.find(pattern)
        if pos != -1:
            print(f"Found construction pattern '{pattern}' at offset 0x{pos:x}")
            
            # 显示周围更大的上下文
            start = max(0, pos - 64)
            end = min(len(data), pos + 128)
            context = data[start:end]
            
            print("Extended context:")
            for i in range(0, len(context), 16):
                hex_bytes = ' '.join(f'{b:02x}' for b in context[i:i+16])
                ascii_repr = ''.join(chr(b) if 32 <= b <= 126 else '.' for b in context[i:i+16])
                print(f'  {start+i:08x}: {hex_bytes:<48} {ascii_repr}')
            print()

# 主分析
with open('EFI/BOOT/BOOT.EFI', 'rb') as f:
    data = f.read()

print(f"Deep analysis of EFI/BOOT/BOOT.EFI ({len(data)} bytes)")
print("="*70)

search_embedded_tables(data)
check_manual_construction(data)

print("\nDeep analysis complete.")