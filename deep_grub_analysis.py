#!/usr/bin/env python3
"""
深度GRUB配置分析工具
Deep GRUB Configuration Analysis Tool
"""

import os
import sys
import re
import json
from pathlib import Path
from typing import Dict, List, Any, Optional, Tuple, Set

class DeepGRUBAnalyzer:
    """深度GRUB配置分析器"""
    
    def __init__(self, config_path: str):
        self.config_path = config_path
        self.config_content = ""
        self.lines = []
        self.functions = {}
        self.variables = {}
        self.conditionals = []
        self.security_analysis = {}
        
    def load_config(self) -> bool:
        """加载配置文件"""
        try:
            with open(self.config_path, 'r', encoding='utf-8') as f:
                self.config_content = f.read()
            self.lines = self.config_content.split('\n')
            return True
        except Exception as e:
            print(f"Error loading config {self.config_path}: {e}")
            return False
    
    def analyze_function_definitions(self) -> Dict[str, Dict[str, Any]]:
        """分析函数定义"""
        functions = {}
        
        # 匹配函数定义
        function_pattern = r'function\s+(\w+)\s*\{([^}]*(?:\{[^}]*\}[^}]*)*)\}'
        
        for match in re.finditer(function_pattern, self.config_content, re.MULTILINE | re.DOTALL):
            func_name = match.group(1)
            func_body = match.group(2).strip()
            
            # 分析函数体
            func_analysis = {
                'name': func_name,
                'body': func_body,
                'line_count': len(func_body.split('\n')),
                'complexity': self._calculate_complexity(func_body),
                'calls_functions': self._extract_function_calls(func_body),
                'uses_variables': self._extract_variables(func_body),
                'has_conditionals': self._has_conditionals(func_body),
                'has_loops': self._has_loops(func_body),
                'security_relevant': self._analyze_security_relevance(func_body),
                'boot_related': self._analyze_boot_relevance(func_body),
                'file_operations': self._extract_file_operations(func_body),
                'system_calls': self._extract_system_calls(func_body)
            }
            
            functions[func_name] = func_analysis
        
        return functions
    
    def _calculate_complexity(self, code: str) -> int:
        """计算代码复杂度（简化版）"""
        complexity = 1  # 基础复杂度
        
        # 条件语句增加复杂度
        complexity += len(re.findall(r'\bif\b', code))
        complexity += len(re.findall(r'\belif\b', code))
        complexity += len(re.findall(r'\bwhile\b', code))
        complexity += len(re.findall(r'\bfor\b', code))
        complexity += len(re.findall(r'\bcase\b', code))
        
        return complexity
    
    def _extract_function_calls(self, code: str) -> List[str]:
        """提取函数调用"""
        # 匹配函数调用模式
        call_pattern = r'(\w+)\s*(?:\(|\s+[^=\n])'
        calls = []
        
        for match in re.finditer(call_pattern, code):
            call = match.group(1)
            # 过滤掉关键字和常见命令
            if call not in ['if', 'then', 'else', 'fi', 'for', 'do', 'done', 'while', 'case', 'esac', 'set', 'unset']:
                calls.append(call)
        
        return list(set(calls))
    
    def _extract_variables(self, code: str) -> List[str]:
        """提取变量使用"""
        # 匹配变量引用
        var_pattern = r'\$\{?(\w+)\}?'
        variables = []
        
        for match in re.finditer(var_pattern, code):
            var_name = match.group(1)
            if var_name not in ['1', '2', '3', '4', '5', '6', '7', '8', '9', '0']:  # 过滤位置参数
                variables.append(var_name)
        
        return list(set(variables))
    
    def _has_conditionals(self, code: str) -> bool:
        """检查是否包含条件语句"""
        return bool(re.search(r'\b(if|case)\b', code))
    
    def _has_loops(self, code: str) -> bool:
        """检查是否包含循环"""
        return bool(re.search(r'\b(for|while)\b', code))
    
    def _analyze_security_relevance(self, code: str) -> Dict[str, Any]:
        """分析安全相关性"""
        security_keywords = {
            'authentication': ['auth', 'password', 'login', 'credential'],
            'verification': ['verify', 'check', 'validate', 'signature', 'hash'],
            'encryption': ['encrypt', 'decrypt', 'crypto', 'key', 'cert'],
            'access_control': ['permission', 'access', 'allow', 'deny', 'restrict'],
            'secure_boot': ['secure', 'boot', 'trust', 'signed']
        }
        
        code_lower = code.lower()
        security_features = {}
        
        for category, keywords in security_keywords.items():
            matches = []
            for keyword in keywords:
                if keyword in code_lower:
                    matches.append(keyword)
            if matches:
                security_features[category] = matches
        
        return security_features
    
    def _analyze_boot_relevance(self, code: str) -> Dict[str, Any]:
        """分析启动相关性"""
        boot_keywords = {
            'os_detection': ['windows', 'linux', 'unix', 'freebsd', 'dragonfly'],
            'boot_methods': ['chainloader', 'linux16', 'initrd', 'wimboot', 'memdisk'],
            'file_systems': ['iso9660', 'udf', 'fat', 'ntfs', 'ext'],
            'hardware': ['efi', 'bios', 'uefi', 'legacy', 'x86', 'x64', 'arm']
        }
        
        code_lower = code.lower()
        boot_features = {}
        
        for category, keywords in boot_keywords.items():
            matches = []
            for keyword in keywords:
                if keyword in code_lower:
                    matches.append(keyword)
            if matches:
                boot_features[category] = matches
        
        return boot_features
    
    def _extract_file_operations(self, code: str) -> List[str]:
        """提取文件操作"""
        file_ops = []
        
        # 文件操作模式
        file_patterns = [
            r'loopback\s+\w+\s+"([^"]+)"',
            r'configfile\s+"?([^"\s]+)"?',
            r'cat\s+"?([^"\s]+)"?',
            r'\[\s*-[ef]\s+"?([^"\s]+)"?\s*\]',
            r'vt_load_file_to_mem\s+[^"]*"([^"]+)"'
        ]
        
        for pattern in file_patterns:
            matches = re.findall(pattern, code)
            file_ops.extend(matches)
        
        return list(set(file_ops))
    
    def _extract_system_calls(self, code: str) -> List[str]:
        """提取系统调用"""
        system_calls = []
        
        # 系统调用模式
        system_patterns = [
            r'(chainloader)\s+',
            r'(linux16)\s+',
            r'(initrd16)\s+',
            r'(boot)\s*$',
            r'(terminal_output)\s+',
            r'(set\s+gfxmode)',
            r'(insmod)\s+\w+'
        ]
        
        for pattern in system_patterns:
            matches = re.findall(pattern, code, re.MULTILINE)
            system_calls.extend(matches)
        
        return list(set(system_calls))
    
    def analyze_variable_usage(self) -> Dict[str, Dict[str, Any]]:
        """分析变量使用情况"""
        variables = {}
        
        # 查找变量设置
        set_pattern = r'set\s+(\w+)=([^\n]*)'
        for match in re.finditer(set_pattern, self.config_content):
            var_name = match.group(1)
            var_value = match.group(2).strip()
            
            if var_name not in variables:
                variables[var_name] = {
                    'assignments': [],
                    'references': [],
                    'type': 'unknown'
                }
            
            variables[var_name]['assignments'].append(var_value)
        
        # 查找变量引用
        ref_pattern = r'\$\{?(\w+)\}?'
        for match in re.finditer(ref_pattern, self.config_content):
            var_name = match.group(1)
            
            if var_name not in variables:
                variables[var_name] = {
                    'assignments': [],
                    'references': [],
                    'type': 'external'
                }
            
            variables[var_name]['references'].append(match.start())
        
        # 分析变量类型
        for var_name, var_info in variables.items():
            var_info['reference_count'] = len(var_info['references'])
            var_info['assignment_count'] = len(var_info['assignments'])
            
            # 推断变量类型
            if any('path' in str(val).lower() for val in var_info['assignments']):
                var_info['type'] = 'path'
            elif any(val in ['YES', 'NO', '1', '0'] for val in var_info['assignments']):
                var_info['type'] = 'boolean'
            elif var_name.startswith('vt'):
                var_info['type'] = 'ventoy_internal'
            elif var_name.startswith('grub_'):
                var_info['type'] = 'grub_builtin'
        
        return variables
    
    def analyze_conditional_logic(self) -> List[Dict[str, Any]]:
        """分析条件逻辑"""
        conditionals = []
        
        # 匹配if语句
        if_pattern = r'if\s+(.+?);\s*then'
        for match in re.finditer(if_pattern, self.config_content):
            condition = match.group(1).strip()
            
            conditional_info = {
                'type': 'if',
                'condition': condition,
                'complexity': self._analyze_condition_complexity(condition),
                'variables_used': self._extract_variables(condition),
                'operators': self._extract_operators(condition)
            }
            
            conditionals.append(conditional_info)
        
        return conditionals
    
    def _analyze_condition_complexity(self, condition: str) -> Dict[str, Any]:
        """分析条件复杂度"""
        return {
            'has_and': '&&' in condition or '-a' in condition,
            'has_or': '||' in condition or '-o' in condition,
            'has_negation': '!' in condition or 'not' in condition.lower(),
            'has_file_test': bool(re.search(r'-[efdrwx]', condition)),
            'has_string_test': bool(re.search(r'=|!=', condition)),
            'has_numeric_test': bool(re.search(r'-[eqneltgt]', condition))
        }
    
    def _extract_operators(self, condition: str) -> List[str]:
        """提取操作符"""
        operators = []
        
        operator_patterns = [
            r'(-[efdrwxzn])',  # 文件测试操作符
            r'(-[eqneltgt])',  # 数值比较操作符
            r'(==|!=|=)',      # 字符串比较操作符
            r'(&&|\|\|)',      # 逻辑操作符
            r'(!)'             # 否定操作符
        ]
        
        for pattern in operator_patterns:
            matches = re.findall(pattern, condition)
            operators.extend(matches)
        
        return operators
    
    def analyze_security_mechanisms(self) -> Dict[str, Any]:
        """分析安全机制"""
        security_analysis = {
            'compatibility_checks': [],
            'file_verification': [],
            'signature_validation': [],
            'secure_boot_features': [],
            'access_controls': []
        }
        
        # 兼容性检查
        compat_patterns = [
            r'vt_check_compatible',
            r'ventoy_compatible',
            r'compatibility.*check'
        ]
        
        for pattern in compat_patterns:
            matches = re.findall(pattern, self.config_content, re.IGNORECASE)
            security_analysis['compatibility_checks'].extend(matches)
        
        # 文件验证
        verify_patterns = [
            r'vt_.*verify',
            r'check.*file',
            r'validate.*'
        ]
        
        for pattern in verify_patterns:
            matches = re.findall(pattern, self.config_content, re.IGNORECASE)
            security_analysis['file_verification'].extend(matches)
        
        # 签名验证
        sig_patterns = [
            r'signature',
            r'signed',
            r'cert.*verify',
            r'hash.*check'
        ]
        
        for pattern in sig_patterns:
            matches = re.findall(pattern, self.config_content, re.IGNORECASE)
            security_analysis['signature_validation'].extend(matches)
        
        # 安全启动特性
        secboot_patterns = [
            r'secure.*boot',
            r'trusted.*boot',
            r'verified.*boot'
        ]
        
        for pattern in secboot_patterns:
            matches = re.findall(pattern, self.config_content, re.IGNORECASE)
            security_analysis['secure_boot_features'].extend(matches)
        
        return security_analysis
    
    def analyze_all(self) -> Dict[str, Any]:
        """执行完整分析"""
        if not self.load_config():
            return {"error": "Failed to load config"}
        
        results = {
            "config_path": self.config_path,
            "basic_stats": {
                "total_lines": len(self.lines),
                "non_empty_lines": len([line for line in self.lines if line.strip()]),
                "comment_lines": len([line for line in self.lines if line.strip().startswith('#')]),
                "function_count": len(re.findall(r'function\s+\w+', self.config_content))
            },
            "functions": self.analyze_function_definitions(),
            "variables": self.analyze_variable_usage(),
            "conditionals": self.analyze_conditional_logic(),
            "security_mechanisms": self.analyze_security_mechanisms()
        }
        
        return results

def main():
    """主函数"""
    print("=== 深度GRUB配置分析工具 ===")
    print("=== Deep GRUB Configuration Analysis Tool ===\n")
    
    grub_configs = [
        "grub/grub.cfg",
        "grub/ventoy_grub.cfg"
    ]
    
    all_results = {}
    
    for config_file in grub_configs:
        if os.path.exists(config_file):
            print(f"深度分析GRUB配置: {config_file}")
            analyzer = DeepGRUBAnalyzer(config_file)
            all_results[config_file] = analyzer.analyze_all()
    
    # 保存结果
    with open('deep_grub_analysis_results.json', 'w', encoding='utf-8') as f:
        json.dump(all_results, f, indent=2, ensure_ascii=False)
    
    print(f"\n深度分析完成！结果已保存到 deep_grub_analysis_results.json")
    return all_results

if __name__ == "__main__":
    main()
