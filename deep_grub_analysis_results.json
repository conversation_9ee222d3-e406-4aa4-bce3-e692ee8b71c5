{"grub/grub.cfg": {"config_path": "grub/grub.cfg", "basic_stats": {"total_lines": 2740, "non_empty_lines": 2274, "comment_lines": 119, "function_count": 85}, "functions": {"ventoy_pause": {"name": "ventoy_pause", "body": "echo \"press Enter to continue ......\"\n    read vtTmpPause", "line_count": 2, "complexity": 1, "calls_functions": ["nter", "echo", "press", "ontinue", "o", "read"], "uses_variables": [], "has_conditionals": false, "has_loops": false, "security_relevant": {}, "boot_related": {}, "file_operations": [], "system_calls": []}, "ventoy_debug_pause": {"name": "ventoy_debug_pause", "body": "if [ -n \"${vtdebug_flag", "line_count": 1, "complexity": 2, "calls_functions": ["n"], "uses_variables": ["vtdebug_flag"], "has_conditionals": true, "has_loops": false, "security_relevant": {}, "boot_related": {}, "file_operations": [], "system_calls": []}, "ventoy_max_resolution": {"name": "ventoy_max_resolution", "body": "vt_enum_video_mode\n    vt_get_video_mode 0 vtCurMode\n    terminal_output console\n    set gfxmode=$vtCurMode\n    terminal_output gfxterm", "line_count": 5, "complexity": 1, "calls_functions": ["onsole", "vtCurMode", "erminal_output", "t_get_video_mode", "vt_enum_video_mode", "et"], "uses_variables": ["vtCurMode"], "has_conditionals": false, "has_loops": false, "security_relevant": {}, "boot_related": {}, "file_operations": [], "system_calls": ["terminal_output", "set gfxmode"]}, "ventoy_cli_console": {"name": "ventoy_cli_console", "body": "if [ -z \"$vtoy_display_mode\" ]; then\n        terminal_output  console\n    elif [ \"$vtoy_display_mode\" = \"GUI\" ]; then\n        terminal_output  console\n    fi", "line_count": 5, "complexity": 3, "calls_functions": ["erminal_output", "lif", "onsole", "z"], "uses_variables": ["vtoy_display_mode"], "has_conditionals": true, "has_loops": false, "security_relevant": {}, "boot_related": {}, "file_operations": [], "system_calls": ["terminal_output"]}, "ventoy_gui_console": {"name": "ventoy_gui_console", "body": "if [ -z \"$vtoy_display_mode\" ]; then\n        terminal_output  gfxterm\n    elif [ \"$vtoy_display_mode\" = \"GUI\" ]; then\n        terminal_output  gfxterm\n    fi", "line_count": 5, "complexity": 3, "calls_functions": ["fxterm", "erminal_output", "lif", "z"], "uses_variables": ["vtoy_display_mode"], "has_conditionals": true, "has_loops": false, "security_relevant": {}, "boot_related": {}, "file_operations": [], "system_calls": ["terminal_output"]}, "ventoy_acpi_param": {"name": "ventoy_acpi_param", "body": "if [ \"$VTOY_PARAM_NO_ACPI\" != \"1\" ]; then\n        vt_acpi_param \"$1\" \"$2\"\n    fi", "line_count": 3, "complexity": 2, "calls_functions": ["t_acpi_param"], "uses_variables": ["VTOY_PARAM_NO_ACPI"], "has_conditionals": true, "has_loops": false, "security_relevant": {}, "boot_related": {}, "file_operations": [], "system_calls": []}, "ventoy_vcfg_proc": {"name": "ventoy_vcfg_proc", "body": "if vt_check_custom_boot \"${1", "line_count": 1, "complexity": 2, "calls_functions": ["t_check_custom_boot"], "uses_variables": [], "has_conditionals": true, "has_loops": false, "security_relevant": {"verification": ["check"], "secure_boot": ["boot"]}, "boot_related": {}, "file_operations": [], "system_calls": []}, "ventoy_language": {"name": "ventoy_language", "body": "configfile $prefix/menulang.cfg", "line_count": 1, "complexity": 1, "calls_functions": ["configfile"], "uses_variables": ["prefix"], "has_conditionals": false, "has_loops": false, "security_relevant": {}, "boot_related": {"hardware": ["efi"]}, "file_operations": ["$prefix/menulang.cfg"], "system_calls": []}, "ventoy_diagnosis": {"name": "ventoy_diagnosis", "body": "vt_enum_video_mode    \n    configfile $prefix/debug.cfg", "line_count": 2, "complexity": 1, "calls_functions": ["onfigfile", "vt_enum_video_mode"], "uses_variables": ["prefix"], "has_conditionals": false, "has_loops": false, "security_relevant": {}, "boot_related": {"hardware": ["efi"]}, "file_operations": ["$prefix/debug.cfg"], "system_calls": []}, "ventoy_localboot": {"name": "ventoy_localboot", "body": "configfile $prefix/localboot.cfg", "line_count": 1, "complexity": 1, "calls_functions": ["configfile"], "uses_variables": ["prefix"], "has_conditionals": false, "has_loops": false, "security_relevant": {"secure_boot": ["boot"]}, "boot_related": {"hardware": ["efi"]}, "file_operations": ["$prefix/localboot.cfg"], "system_calls": []}, "ventoy_ext_menu": {"name": "ventoy_ext_menu", "body": "# 检查Ventoy的grub配置文件是否存在\n    if [ -e $prefix/ventoy_grub.cfg ]; then\n        # 设置Ventoy新上下文标记\n        set ventoy_new_context=1\n        # 加载Ventoy的grub配置文件\n        configfile $prefix/ventoy_grub.cfg\n        # 取消Ventoy新上下文标记\n        unset ventoy_new_context\n    else\n        # 若配置文件不存在，输出提示信息\n        echo \"ventoy_grub.cfg 不存在。\"\n        # 提示用户按回车键退出\n        echo -en \"\\n$VTLANG_ENTER_EXIT ...\"\n        # 等待用户输入（按回车继续）\n        read vtInputKey\n    fi", "line_count": 16, "complexity": 2, "calls_functions": ["cfg", "en", "entoy_new_context", "onfigfile", "cho", "1", "lse", "提示用户按回车键退出", "read", "tInputKey", "et", "检查Ventoy的grub配置文件是否存在", "输出提示信息", "f", "e", "取消Ventoy新上下文标记", "nset", "VTLANG_ENTER_EXIT", "加载Ventoy的grub配置文件", "设置Ventoy新上下文标记"], "uses_variables": ["VTLANG_ENTER_EXIT", "prefix"], "has_conditionals": true, "has_loops": false, "security_relevant": {"encryption": ["key"]}, "boot_related": {"file_systems": ["ext"], "hardware": ["efi"]}, "file_operations": ["$prefix/ventoy_grub.cfg"], "system_calls": []}, "ventoy_checksum": {"name": "ventoy_checksum", "body": "if [ -f \"${vtoy_iso_part", "line_count": 1, "complexity": 2, "calls_functions": ["f"], "uses_variables": ["vtoy_iso_part"], "has_conditionals": true, "has_loops": false, "security_relevant": {}, "boot_related": {}, "file_operations": [], "system_calls": []}, "ventoy_show_help": {"name": "ventoy_show_help", "body": "if [ -f $prefix/help.tar.gz ]; then\n        if [ -z \"$vtoy_help_txt_mem_addr\" ]; then\n            vt_load_file_to_mem \"auto\" $prefix/help.tar.gz vtoy_help_txt_mem\n        fi\n\n        loopback vt_help_tarfs mem:${vtoy_help_txt_mem_addr", "line_count": 6, "complexity": 3, "calls_functions": ["t_load_file_to_mem", "f", "gz", "z", "oopback", "i", "toy_help_txt_mem", "t_help_tarfs"], "uses_variables": ["vtoy_help_txt_mem_addr", "prefix"], "has_conditionals": true, "has_loops": false, "security_relevant": {}, "boot_related": {"hardware": ["efi"]}, "file_operations": ["$prefix/help.tar.gz", "auto"], "system_calls": []}, "ventoy_load_menu_lang_file": {"name": "ventoy_load_menu_lang_file", "body": "vt_load_file_to_mem \"auto\" $prefix/menu.tar.gz vtoy_menu_lang_mem\n    loopback vt_menu_tarfs mem:${vtoy_menu_lang_mem_addr", "line_count": 2, "complexity": 1, "calls_functions": ["gz", "oopback", "vt_load_file_to_mem", "t_menu_tarfs", "toy_menu_lang_mem"], "uses_variables": ["vtoy_menu_lang_mem_addr", "prefix"], "has_conditionals": false, "has_loops": false, "security_relevant": {}, "boot_related": {"hardware": ["efi"]}, "file_operations": ["auto"], "system_calls": []}, "get_os_type": {"name": "get_os_type", "body": "set vtoy_os=Linux\n    export vtoy_os\n\n    if vt_str_begin \"$vt_volume_id\" \"DLC Boot\"; then\n        if [ -f (loop)/DLCBoot.exe ]; then\n            set vtoy_os=Windows\n        fi\n    else\n        for file in \"efi/microsoft/boot/bcd\" \"sources/boot.wim\" \"boot/bcd\" \"bootmgr.efi\" \"boot/etfsboot.com\" ; do        \n            if vt_file_exist_nocase (loop)/$file; then        \n                set vtoy_os=Windows            \n                break\n            fi\n        done\n    fi\n\n    if [ \"$vtoy_os\" = \"Linux\" ]; then\n        if vt_strstr \"$vt_system_id\" \"FreeBSD\"; then\n            set vtoy_os=Unix\n            set vt_unix_type=FreeBSD\n        elif [ -e (loop)/bin/freebsd-version ]; then\n            set vtoy_os=Unix\n            set vt_unix_type=FreeBSD\n        elif [ -e (loop)/boot/kernel/geom_ventoy.ko ]; then\n            set vtoy_os=Unix\n            set vt_unix_type=FreeBSD\n        elif vt_str_begin \"$vt_system_id\" \"DragonFly\"; then\n            set vtoy_os=Unix\n            set vt_unix_type=DragonFly\n            \n            \n        elif [ -e (loop)/boot/kernel/kernel ]; then            \n            if file --is-x86-kfreebsd (loop)/boot/kernel/kernel; then\n                set vtoy_os=Unix\n                set vt_unix_type=FreeBSD\n            elif file --is-x86-knetbsd (loop)/boot/kernel/kernel; then\n                set vtoy_os=Unix\n                set vt_unix_type=NetBSD\n            fi\n        fi\n    fi\n\n    if [ -n \"${vtdebug_flag", "line_count": 43, "complexity": 14, "calls_functions": ["ile", "ko", "t_strstr", "DragonFly", "t_str_begin", "knetbsd", "or", "i", "exe", "one", "lse", "kfreebsd", "Unix", "reak", "toy_os", "et", "xport", "lif", "Linux", "FreeBSD", "kernel", "f", "e", "version", "n", "t_file_exist_nocase", "NetBSD", "DLC", "Windows"], "uses_variables": ["vtdebug_flag", "vtoy_os", "vt_volume_id", "file", "vt_system_id"], "has_conditionals": true, "has_loops": true, "security_relevant": {"secure_boot": ["boot"]}, "boot_related": {"os_detection": ["windows", "linux", "unix", "freebsd", "dragonfly"], "hardware": ["efi", "x86"]}, "file_operations": ["(loop)/bin/freebsd-version", "(loop)/boot/kernel/kernel", "(loop)/DLCBoot.exe", "(loop)/boot/kernel/geom_ventoy.ko"], "system_calls": []}, "vt_check_compatible_pe": {"name": "vt_check_compatible_pe", "body": "#Check for PE without external tools\n    #set compatible if ISO file is less than 80MB\n    if [ $vt_chosen_size -GT 33554432 -a $vt_chosen_size -LE 83886080 ]; then\n        set ventoy_compatible=YES    \n    fi\n\n    return", "line_count": 7, "complexity": 4, "calls_functions": ["vt_chosen_size", "3886080", "ile", "ess", "GT", "LE", "or", "YES", "ools", "a", "ithout", "i", "SO", "xternal", "s", "E", "0MB", "Check", "f", "3554432", "han", "ompatible", "et"], "uses_variables": ["vt_chosen_size"], "has_conditionals": true, "has_loops": true, "security_relevant": {"verification": ["check"]}, "boot_related": {"file_systems": ["ext"]}, "file_operations": [], "system_calls": []}, "vt_check_compatible_linux": {"name": "vt_check_compatible_linux", "body": "if vt_str_begin \"$vt_volume_id\" \"embootkit\"; then\n        set ventoy_compatible=YES\n    elif [ -e \"$1/casper/tinycore.gz\" ]; then\n        set ventoy_compatible=YES\n    fi\n\n    return", "line_count": 7, "complexity": 3, "calls_functions": ["et", "YES", "e", "t_str_begin", "i", "lif"], "uses_variables": ["vt_volume_id"], "has_conditionals": true, "has_loops": false, "security_relevant": {"secure_boot": ["boot"]}, "boot_related": {}, "file_operations": ["$1/casper/tinycore.gz"], "system_calls": []}, "locate_initrd": {"name": "locate_initrd", "body": "vt_linux_locate_initrd \n\n    if [ -n \"${vtdebug_flag", "line_count": 3, "complexity": 2, "calls_functions": ["f", "n", "vt_linux_locate_initrd"], "uses_variables": ["vtdebug_flag"], "has_conditionals": true, "has_loops": false, "security_relevant": {}, "boot_related": {"os_detection": ["linux"], "boot_methods": ["initrd"]}, "file_operations": [], "system_calls": []}, "locate_wim": {"name": "locate_wim", "body": "vt_windows_locate_wim_patch (loop) \"$1\"\n    \n    if [ -n \"${vtdebug_flag", "line_count": 3, "complexity": 2, "calls_functions": ["n", "vt_windows_locate_wim_patch"], "uses_variables": ["vtdebug_flag"], "has_conditionals": true, "has_loops": false, "security_relevant": {}, "boot_related": {"os_detection": ["windows"]}, "file_operations": [], "system_calls": []}, "distro_specify_wim_patch": {"name": "distro_specify_wim_patch", "body": "if [ -d (loop)/h3pe ]; then\n        vt_windows_collect_wim_patch wim /BOOT/H3_10PE.WIM\n        vt_windows_collect_wim_patch wim /BOOT/H3_7PE.WIM\n        vt_windows_collect_wim_patch wim /BOOT/H3_8PE.WIM\n        vt_windows_collect_wim_patch wim /BOOT/H3_81PE.WIM\n    elif [ -d (loop)/2k10/winpe ]; then\n        vt_windows_collect_wim_patch wim /2k10/winpe/w1086pe.wim\n        vt_windows_collect_wim_patch wim /2k10/winpe/w8x86pe.wim\n        vt_windows_collect_wim_patch wim /2k10/winpe/w7x86pe.wim\n    fi", "line_count": 10, "complexity": 3, "calls_functions": ["t_windows_collect_wim_patch", "h3pe", "WIM", "lif", "d", "winpe", "wim", "im"], "uses_variables": [], "has_conditionals": true, "has_loops": false, "security_relevant": {"secure_boot": ["boot"]}, "boot_related": {"os_detection": ["windows"], "hardware": ["x86"]}, "file_operations": [], "system_calls": []}, "distro_specify_wim_patch_phase2": {"name": "distro_specify_wim_patch_phase2", "body": "if [ -f (loop)/boot/boot.wim ]; then\n        vt_windows_collect_wim_patch wim /boot/boot.wim\n    elif [ -f (loop)/sources/boot.wim ]; then\n        vt_windows_collect_wim_patch wim /sources/boot.wim\n    fi\n\n    if vt_str_begin \"$vt_volume_id\" \"DLC Boot\"; then\n        for vwfile in \"/DLC1/WinPE/W11x64.wim\" \"/DLC1/WinPE/W10x64.wim\" \"/DLC1/WinPE/W10x86.wim\"; do\n            if [ -f (loop)/$vwfile ]; then\n                vt_windows_collect_wim_patch wim $vwfile\n            fi\n        done\n    fi", "line_count": 13, "complexity": 6, "calls_functions": ["one", "DLC", "or", "t_windows_collect_wim_patch", "f", "vwfile", "lif", "wfile", "n", "wim", "t_str_begin", "i", "im"], "uses_variables": ["vt_volume_id", "vwfile"], "has_conditionals": true, "has_loops": true, "security_relevant": {"secure_boot": ["boot"]}, "boot_related": {"os_detection": ["windows"], "hardware": ["x86", "x64"]}, "file_operations": ["(loop)/boot/boot.wim", "(loop)/sources/boot.wim", "(loop)/$vwfile"], "system_calls": []}, "distro_specify_initrd_file": {"name": "distro_specify_initrd_file", "body": "if [ -e (loop)/boot/all.rdz ]; then\n        vt_linux_specify_initrd_file /boot/all.rdz\n    elif [ -e (loop)/boot/xen.gz ]; then \n        if [ -e (loop)/install.img ]; then\n            vt_linux_specify_initrd_file /install.img\n        fi\n    elif [ -d (loop)/casper ]; then \n        if [ -e (loop)/casper/initrd ]; then\n            vt_linux_specify_initrd_file /casper/initrd\n        fi\n        if [ -e (loop)/casper/initrd.gz ]; then\n            vt_linux_specify_initrd_file /casper/initrd.gz\n        fi\n        if [ -e (loop)/casper/initrd-oem ]; then\n            vt_linux_specify_initrd_file /casper/initrd-oem\n        fi\n    elif [ -e (loop)/boot/grub/initrd.xz ]; then\n        vt_linux_specify_initrd_file /boot/grub/initrd.xz\n    elif [ -e (loop)/initrd.gz ]; then\n        vt_linux_specify_initrd_file /initrd.gz\n    elif [ -e (loop)/slax/boot/initrfs.img ]; then\n        vt_linux_specify_initrd_file /slax/boot/initrfs.img\n    elif [ -e (loop)/minios/boot/initrfs.img ]; then\n        vt_linux_specify_initrd_file /minios/boot/initrfs.img\n    elif [ -e (loop)/pmagic/initrd.img ]; then\n        vt_linux_specify_initrd_file /pmagic/initrd.img\n    elif [ -e (loop)/boot/initrd.xz ]; then\n        vt_linux_specify_initrd_file /boot/initrd.xz\n    elif [ -e (loop)/boot/initrd.gz ]; then\n        vt_linux_specify_initrd_file /boot/initrd.gz\n    elif [ -f (loop)/boot/initrd ]; then\n        vt_linux_specify_initrd_file /boot/initrd\n    elif [ -f (loop)/boot/x86_64/loader/initrd ]; then\n        vt_linux_specify_initrd_file /boot/x86_64/loader/initrd\n    elif [ -f (loop)/boot/initramfs-x86_64.img ]; then\n        vt_linux_specify_initrd_file /boot/initramfs-x86_64.img\n    elif [ -f (loop)/boot/isolinux/initramfs_data64.cpio.gz ]; then \n        vt_linux_specify_initrd_file /boot/isolinux/initramfs_data64.cpio.gz\n    elif [ -f (loop)/boot/initrd.img ]; then \n        vt_linux_specify_initrd_file /boot/initrd.img\n        \n    fi\n    \n    if [ -f (loop)/isolinux/initrd.gz ]; then \n        vt_linux_specify_initrd_file /isolinux/initrd.gz\n    fi\n    \n    if vt_str_begin \"$vt_volume_id\" \"QUBES\"; then \n        vt_linux_specify_initrd_file /images/pxeboot/initrd.img\n    fi\n    \n    if [ \"$vt_chosen_size\" = \"1133375488\" ]; then\n        if [ -d (loop)/boot/grub/x86_64-efi ]; then\n            vt_cpio_busybox64 \"64h\"\n        fi\n    fi", "line_count": 56, "complexity": 24, "calls_functions": ["img", "t_linux_specify_initrd_file", "casper", "e", "gz", "f", "i", "d", "oem", "initrd", "xz", "efi", "t_str_begin", "rdz", "lif", "t_cpio_busybox64"], "uses_variables": ["vt_volume_id", "vt_chosen_size"], "has_conditionals": true, "has_loops": false, "security_relevant": {"secure_boot": ["boot"]}, "boot_related": {"os_detection": ["linux"], "boot_methods": ["initrd"], "hardware": ["efi", "x86", "x64"]}, "file_operations": ["(loop)/boot/initramfs-x86_64.img", "(loop)/boot/all.rdz", "(loop)/pmagic/initrd.img", "(loop)/boot/grub/initrd.xz", "(loop)/casper/initrd.gz", "(loop)/boot/x86_64/loader/initrd", "(loop)/boot/xen.gz", "(loop)/boot/initrd.gz", "(loop)/initrd.gz", "(loop)/install.img", "(loop)/boot/initrd.xz", "(loop)/casper/initrd", "(loop)/slax/boot/initrfs.img", "(loop)/casper/initrd-oem", "(loop)/isolinux/initrd.gz", "(loop)/boot/initrd", "(loop)/boot/initrd.img", "(loop)/boot/isolinux/initramfs_data64.cpio.gz", "(loop)/minios/boot/initrfs.img"], "system_calls": []}, "distro_specify_initrd_file_phase2": {"name": "distro_specify_initrd_file_phase2", "body": "if [ -f (loop)/boot/initrd.img ]; then\n        vt_linux_specify_initrd_file /boot/initrd.img\n    elif [ -f (loop)/Setup/initrd.gz ]; then\n        vt_linux_specify_initrd_file /Setup/initrd.gz\n    elif [ -f (loop)/isolinux/initramfs ]; then\n        vt_linux_specify_initrd_file /isolinux/initramfs\n    elif [ -f (loop)/boot/iniramfs.igz ]; then\n        vt_linux_specify_initrd_file /boot/iniramfs.igz\n    elif [ -f (loop)/initrd-x86_64 ]; then\n        vt_linux_specify_initrd_file /initrd-x86_64\n    elif [ -f (loop)/live/initrd.img ]; then \n        vt_linux_specify_initrd_file /live/initrd.img\n    elif [ -f (loop)/initrd.img ]; then \n        vt_linux_specify_initrd_file /initrd.img\n    elif [ -f (loop)/sysresccd/boot/x86_64/sysresccd.img ]; then \n        vt_linux_specify_initrd_file /sysresccd/boot/x86_64/sysresccd.img\n    elif [ -f (loop)/CDlinux/initrd ]; then \n        vt_linux_specify_initrd_file /CDlinux/initrd\n    elif [ -f (loop)/parabola/boot/x86_64/parabolaiso.img ]; then \n        vt_linux_specify_initrd_file /parabola/boot/x86_64/parabolaiso.img\n        if [ -f (loop)/parabola/boot/i686/parabolaiso.img ]; then \n            vt_linux_specify_initrd_file /parabola/boot/i686/parabolaiso.img\n        fi\n    elif [ -f (loop)/parabola/boot/x86_64/initramfs-linux-libre.img ]; then\n        vt_linux_specify_initrd_file /parabola/boot/x86_64/initramfs-linux-libre.img\n        if [ -f (loop)/parabola/boot/i686/initramfs-linux-libre.img ]; then\n            vt_linux_specify_initrd_file /parabola/boot/i686/initramfs-linux-libre.img\n        fi\n    elif [ -f (loop)/hyperbola/boot/x86_64/hyperiso.img ]; then \n        vt_linux_specify_initrd_file /hyperbola/boot/x86_64/hyperiso.img\n        if [ -f (loop)/hyperbola/boot/i686/hyperiso.img ]; then \n            vt_linux_specify_initrd_file /hyperbola/boot/i686/hyperiso.img\n        fi\n    elif [ -f (loop)/EFI/BOOT/initrd.img ]; then \n        #Qubes\n        vt_linux_specify_initrd_file /EFI/BOOT/initrd.img\n        if [ \"$grub_platform\" != \"pc\" ]; then\n            vt_add_replace_file 0 \"initrd.img\"    \n        fi\n    elif [ -f (loop)/initrd ]; then \n        vt_linux_specify_initrd_file /initrd\n    elif [ -f (loop)/live/initrd1 ]; then \n        vt_linux_specify_initrd_file /live/initrd1\n    elif [ -f (loop)/isolinux/initrd.img ]; then \n        vt_linux_specify_initrd_file /isolinux/initrd.img\n    elif [ -f (loop)/isolinux/initrd.gz ]; then \n        vt_linux_specify_initrd_file /isolinux/initrd.gz\n    elif [ -f (loop)/syslinux/kernel/initramfs.gz ]; then \n        vt_linux_specify_initrd_file /syslinux/kernel/initramfs.gz    \n    elif vt_strstr \"$vt_volume_id\" \"Daphile\"; then\n        vt_linux_parse_initrd_isolinux   (loop)/isolinux/\n    elif [ -f (loop)/boot/rootfs.xz ]; then \n        vt_linux_specify_initrd_file /boot/rootfs.xz\n        if [ \"$grub_platform\" != \"pc\" ]; then\n            vt_add_replace_file 0 \"minimal\\\\x86_64\\\\rootfs.xz\"\n        fi\n    elif [ -f (loop)/arch/boot/x86_64/archiso.img ]; then \n        vt_linux_specify_initrd_file /arch/boot/x86_64/archiso.img\n        if [ \"$grub_platform\" != \"pc\" ]; then\n            vt_add_replace_file 0 \"EFI\\\\archiso\\\\archiso.img\"\n        fi\n    elif [ -f (loop)/blackarch/boot/x86_64/archiso.img ]; then \n        vt_linux_specify_initrd_file /blackarch/boot/x86_64/archiso.img\n    elif [ -f (loop)/blackarch/boot/x86_64/initramfs-linux.img ]; then \n        vt_linux_specify_initrd_file /blackarch/boot/x86_64/initramfs-linux.img\n        \n    elif [ -f (loop)/install.amd/initrd.gz ]; then\n        vt_linux_specify_initrd_file /live/initrd2.img\n        vt_linux_specify_initrd_file /install.amd/initrd.gz\n        vt_linux_specify_initrd_file /install.amd/gtk/initrd.gz\n    elif [ -f (loop)/boot/grub/kernels.cfg ]; then\n        vt_linux_parse_initrd_grub  file  (loop)/boot/grub/kernels.cfg\n    elif [ -f (loop)/austrumi/initrd.gz ]; then\n        vt_linux_specify_initrd_file /austrumi/initrd.gz\n        if [ -f (loop)/EFI/BOOT/bootx64.efi ]; then\n            vt_cpio_busybox64 \"64h\"\n        fi\n    elif [ -f (loop)/boot/initfs.x86_64-efi ]; then\n        vt_linux_specify_initrd_file /boot/initfs.x86_64-efi\n        if [ -f (loop)/boot/initfs.i386-pc ]; then\n            vt_linux_specify_initrd_file /boot/initfs.i386-pc\n        fi\n    elif [ -f (loop)/antiX/initrd.gz ]; then\n        vt_linux_specify_initrd_file /antiX/initrd.gz\n    elif [ -f (loop)/360Disk/initrd.gz ]; then\n        vt_linux_specify_initrd_file /360Disk/initrd.gz\n    elif [ -f (loop)/porteus/initrd.xz ]; then\n        vt_linux_specify_initrd_file /porteus/initrd.xz\n    elif [ -f (loop)/pyabr/boot/initrfs.img ]; then\n        vt_linux_specify_initrd_file /pyabr/boot/initrfs.img\n    elif [ -f (loop)/initrd0.img ]; then\n        vt_linux_specify_initrd_file /initrd0.img\n    elif [ -f (loop)/sysresccd/boot/i686/sysresccd.img ]; then\n        vt_linux_specify_initrd_file /sysresccd/boot/i686/sysresccd.img\n    elif [ -f (loop)/boot/full.cz ]; then\n        vt_linux_specify_initrd_file /boot/full.cz\n    elif [ -f (loop)/images/pxeboot/initrd.img ]; then\n        vt_linux_specify_initrd_file /images/pxeboot/initrd.img\n    elif [ -f (loop)/live/initrd ]; then\n        vt_linux_specify_initrd_file /live/initrd\n    elif [ -f (loop)/initramfs-linux.img ]; then\n        vt_linux_specify_initrd_file /initramfs-linux.img\n    elif [ -f (loop)/boot/isolinux/initrd.gz ]; then\n        vt_linux_specify_initrd_file /boot/isolinux/initrd.gz        \n    fi", "line_count": 105, "complexity": 47, "calls_functions": ["gz", "x86_64", "ile", "cfg", "t_strstr", "t_add_replace_file", "Qubes", "efi", "t_linux_parse_initrd_isolinux", "initrd", "t_linux_parse_initrd_grub", "i", "img", "t_cpio_busybox64", "elif", "pc", "lif", "igz", "cz", "t_linux_specify_initrd_file", "f", "initrd1", "initramfs", "xz"], "uses_variables": ["grub_platform", "vt_volume_id"], "has_conditionals": true, "has_loops": false, "security_relevant": {"secure_boot": ["boot"]}, "boot_related": {"os_detection": ["linux"], "boot_methods": ["initrd"], "hardware": ["efi", "x86", "x64"]}, "file_operations": ["(loop)/install.amd/initrd.gz", "(loop)/parabola/boot/i686/parabolaiso.img", "(loop)/EFI/BOOT/initrd.img", "(loop)/blackarch/boot/x86_64/initramfs-linux.img", "(loop)/parabola/boot/x86_64/parabolaiso.img", "(loop)/austrumi/initrd.gz", "(loop)/boot/iniramfs.igz", "(loop)/boot/rootfs.xz", "(loop)/isolinux/initramfs", "(loop)/boot/full.cz", "(loop)/live/initrd1", "(loop)/porteus/initrd.xz", "(loop)/arch/boot/x86_64/archiso.img", "(loop)/antiX/initrd.gz", "(loop)/syslinux/kernel/initramfs.gz", "(loop)/boot/isolinux/initrd.gz", "(loop)/Setup/initrd.gz", "(loop)/initrd", "(loop)/initrd-x86_64", "(loop)/isolinux/initrd.img", "(loop)/blackarch/boot/x86_64/archiso.img", "(loop)/sysresccd/boot/x86_64/sysresccd.img", "(loop)/boot/grub/kernels.cfg", "(loop)/boot/initfs.x86_64-efi", "(loop)/boot/initfs.i386-pc", "(loop)/parabola/boot/i686/initramfs-linux-libre.img", "(loop)/360Disk/initrd.gz", "(loop)/live/initrd.img", "(loop)/initrd.img", "(loop)/hyperbola/boot/x86_64/hyperiso.img", "(loop)/pyabr/boot/initrfs.img", "(loop)/CDlinux/initrd", "(loop)/images/pxeboot/initrd.img", "(loop)/initramfs-linux.img", "(loop)/isolinux/initrd.gz", "(loop)/EFI/BOOT/bootx64.efi", "(loop)/sysresccd/boot/i686/sysresccd.img", "(loop)/boot/initrd.img", "(loop)/live/initrd", "(loop)/initrd0.img", "(loop)/parabola/boot/x86_64/initramfs-linux-libre.img", "(loop)/hyperbola/boot/i686/hyperiso.img"], "system_calls": []}, "ventoy_get_ghostbsd_ver": {"name": "ventoy_get_ghostbsd_ver", "body": "# fallback to parse version from elf /boot/kernel/kernel\n    set vt_freebsd_ver=xx", "line_count": 2, "complexity": 1, "calls_functions": ["kernel", "rom", "lf", "o", "fallback", "et", "ersion", "arse"], "uses_variables": [], "has_conditionals": false, "has_loops": false, "security_relevant": {"secure_boot": ["boot"]}, "boot_related": {"os_detection": ["freebsd"]}, "file_operations": [], "system_calls": []}, "ventoy_get_furybsd_ver": {"name": "ventoy_get_furybsd_ver", "body": "set vt_freebsd_ver=12.x\n    if regexp --set 1:vtFuryVer \"(14|13)\\.[0-9]\" \"$2\"; then\n        set vt_freebsd_ver=${vtFuryVer", "line_count": 3, "complexity": 2, "calls_functions": ["vtFuryVer", "f", "x", "et", "egexp"], "uses_variables": ["vtFuryVer"], "has_conditionals": true, "has_loops": false, "security_relevant": {}, "boot_related": {"os_detection": ["freebsd"]}, "file_operations": [], "system_calls": []}, "ventoy_get_freenas_ver": {"name": "ventoy_get_freenas_ver", "body": "set vt_freebsd_ver=11.x\n\n    if [ -e (loop)/FreeNAS-MANIFEST ]; then\n        vt_parse_freenas_ver (loop)/FreeNAS-MANIFEST vt_freenas_ver\n        if regexp --set 1:vtNasVer \"^(14|13|12|11)\\.[0-9]\" \"$vt_freenas_ver\"; then\n            set vt_freebsd_ver=${vtNasVer", "line_count": 6, "complexity": 3, "calls_functions": ["f", "MANIFEST", "e", "vtNasVer", "t_freenas_ver", "t_parse_freenas_ver", "x", "et", "egexp"], "uses_variables": ["vtNasVer", "vt_freenas_ver"], "has_conditionals": true, "has_loops": false, "security_relevant": {}, "boot_related": {"os_detection": ["freebsd"]}, "file_operations": ["(loop)/FreeNAS-MANIFEST"], "system_calls": []}, "ventoy_get_truenas_ver": {"name": "ventoy_get_truenas_ver", "body": "set vt_freebsd_ver=12.x\n\n    if [ -e (loop)/TrueNAS-MANIFEST ]; then\n        vt_parse_freenas_ver (loop)/TrueNAS-MANIFEST vt_truenas_ver\n        if regexp --set 1:vtTNasVer \"^(14|13|12|11)\\.[0-9]\" \"$vt_truenas_ver\"; then\n            set vt_freebsd_ver=${vtTNasVer", "line_count": 6, "complexity": 3, "calls_functions": ["t_truenas_ver", "f", "MANIFEST", "e", "vtTNasVer", "t_parse_freenas_ver", "x", "et", "egexp"], "uses_variables": ["vt_truenas_ver", "vtTNasVer"], "has_conditionals": true, "has_loops": false, "security_relevant": {}, "boot_related": {"os_detection": ["freebsd"]}, "file_operations": ["(loop)/TrueNAS-MANIFEST"], "system_calls": []}, "ventoy_get_midnightbsd_ver": {"name": "ventoy_get_midnightbsd_ver", "body": "if vt_str_begin \"$vt_volume_id\" \"1_\"; then\n        set vt_freebsd_ver=11.x\n    elif vt_str_begin \"$vt_volume_id\" \"2_\"; then\n        set vt_freebsd_ver=2.x\n    elif vt_str_begin \"$vt_volume_id\" \"3_\"; then\n        set vt_freebsd_ver=3.x\n    fi", "line_count": 7, "complexity": 4, "calls_functions": ["t_str_begin", "et", "lif", "x"], "uses_variables": ["vt_volume_id"], "has_conditionals": true, "has_loops": false, "security_relevant": {}, "boot_related": {"os_detection": ["freebsd"]}, "file_operations": [], "system_calls": []}, "ventoy_freebsd_proc": {"name": "ventoy_freebsd_proc", "body": "set vtFreeBsdDistro=FreeBSD\n    set vt_freebsd_ver=xx\n\n    if [ -e (loop)/boot/kernel/geom_ventoy.ko ]; then\n        vt_unix_ko_fillmap /boot/kernel/geom_ventoy.ko\n        return\n    fi\n\n    if vt_strstr \"$vt_volume_id\" \"GHOSTBSD\"; then\n        ventoy_get_ghostbsd_ver \"$1\" \"${chosen_path", "line_count": 10, "complexity": 3, "calls_functions": ["FreeBSD", "entoy_get_ghostbsd_ver", "f", "e", "ko", "t_strstr", "eturn", "t_unix_ko_fillmap", "et", "i", "xx"], "uses_variables": ["vt_volume_id", "chosen_path"], "has_conditionals": true, "has_loops": false, "security_relevant": {"secure_boot": ["boot"]}, "boot_related": {"os_detection": ["unix", "freebsd"]}, "file_operations": ["(loop)/boot/kernel/geom_ventoy.ko"], "system_calls": []}, "ventoy_dragonfly_proc": {"name": "ventoy_dragonfly_proc", "body": "unset vt_unix_mod_path\n    for file in \"/boot/kernel/initrd.img.gz\"; do\n        if [ -e (loop)${file", "line_count": 3, "complexity": 3, "calls_functions": ["or", "ile", "f", "t_unix_mod_path", "e", "n"], "uses_variables": ["file"], "has_conditionals": true, "has_loops": true, "security_relevant": {"secure_boot": ["boot"]}, "boot_related": {"os_detection": ["unix"], "boot_methods": ["initrd"]}, "file_operations": [], "system_calls": []}, "ventoy_unix_comm_proc": {"name": "ventoy_unix_comm_proc", "body": "vt_unix_reset\n    \n    vt_unix_check_vlnk \"${1", "line_count": 3, "complexity": 1, "calls_functions": ["t_unix_check_vlnk", "vt_unix_reset"], "uses_variables": [], "has_conditionals": false, "has_loops": false, "security_relevant": {"verification": ["check"]}, "boot_related": {"os_detection": ["unix"]}, "file_operations": [], "system_calls": []}, "uefi_windows_menu_func": {"name": "uefi_windows_menu_func", "body": "vt_windows_reset\n\n    unset vt_cur_wimboot_mode\n    if vt_check_mode 4 \"$vt_chosen_name\"; then\n        set vt_cur_wimboot_mode=1\n    fi\n\n    if [ \"$ventoy_compatible\" = \"NO\" -o \"$vt_cur_wimboot_mode\" = \"1\" ]; then\n        if [ \"$ventoy_fs_probe\" = \"iso9660\" ]; then\n            loopback -d loop\n            vt_iso9660_nojoliet 1            \n            loopback loop \"$1$2\"\n        fi\n        \n        for file in \"efi/microsoft/boot/bcd\"; do\n            vt_windows_collect_wim_patch bcd (loop)/$file                \n        done\n\n        vt_windows_count_wim_patch vt_wim_cnt\n        if [ $vt_wim_cnt -eq 0 ]; then\n            distro_specify_wim_patch_phase2\n        fi\n        \n        ventoy_debug_pause        \n        locate_wim \"${chosen_path", "line_count": 25, "complexity": 6, "calls_functions": ["t_check_mode", "ile", "vt_wim_cnt", "loopback", "t_wim_cnt", "d", "t_iso9660_nojoliet", "file", "t_windows_count_wim_patch", "or", "istro_specify_wim_patch_phase2", "o", "oop", "i", "1", "cd", "one", "ocate_wim", "t_windows_collect_wim_patch", "eq", "entoy_debug_pause", "t_cur_wimboot_mode", "f", "oopback", "n", "nset", "et", "vt_windows_reset"], "uses_variables": ["vt_wim_cnt", "chosen_path", "ventoy_fs_probe", "ventoy_compatible", "vt_chosen_name", "vt_cur_wimboot_mode", "file"], "has_conditionals": true, "has_loops": true, "security_relevant": {"verification": ["check"], "secure_boot": ["boot"]}, "boot_related": {"os_detection": ["windows"], "boot_methods": ["wimboot"], "file_systems": ["iso9660"], "hardware": ["efi"]}, "file_operations": ["$1$2"], "system_calls": []}, "uefi_find_replace_initrd": {"name": "uefi_find_replace_initrd", "body": "if vt_get_efi_vdisk_offset \"${1", "line_count": 1, "complexity": 2, "calls_functions": ["t_get_efi_vdisk_offset"], "uses_variables": [], "has_conditionals": true, "has_loops": false, "security_relevant": {}, "boot_related": {"hardware": ["efi"]}, "file_operations": [], "system_calls": []}, "uefi_linux_menu_func": {"name": "uefi_linux_menu_func", "body": "if [ \"$ventoy_compatible\" = \"NO\" ]; then    \n        \n        if [ \"$ventoy_fs_probe\" = \"udf\" ]; then\n            loopback -d loop            \n            set ventoy_fs_probe=iso9660\n            loopback loop \"$1$2\"\n        fi\n        \n        vt_load_cpio  $vtoy_path   \"$2\" \"$1\" \"busybox=$ventoy_busybox_ver\"\n        \n        vt_linux_clear_initrd\n        \n        if [ -d (loop)/pmagic ]; then\n            vt_linux_specify_initrd_file /pmagic/initrd.img\n        else\n            for file in \"boot/grub/grub.cfg\" \"EFI/BOOT/grub.cfg\" \"EFI/boot/grub.cfg\" \"efi/boot/grub.cfg\" \"EFI/BOOT/BOOTX64.conf\" \"/grub/grub.cfg\" \"EFI/BOOT/grub/grub.cfg\"; do\n                if [ -e (loop)/$file ]; then                    \n                    vt_linux_parse_initrd_grub  file  (loop)/$file\n                fi\n            done\n        fi\n\n        # special process for special distros\n        if [ -d (loop)/loader/entries ]; then\n            vt_linux_parse_initrd_grub  dir  (loop)/loader/entries/\n        elif [ -d (loop)/boot/grub ]; then\n            vt_linux_parse_initrd_grub  dir  (loop)/boot/grub/\n        fi\n        \n        distro_specify_initrd_file\n        \n        vt_linux_initrd_count vtcount\n        \n        if [ $vtcount -eq 0 ]; then\n            if [ -e (loop)/EFI/boot/livegrub.cfg ]; then\n                vt_linux_parse_initrd_grub  file  (loop)/EFI/boot/livegrub.cfg\n            fi\n            distro_specify_initrd_file_phase2\n            \n            if [ \"$vt_efi_dir\" = \"NO\" ]; then\n                if [ -f (loop)/efi.img ];  then\n                    vt_add_replace_file 0 \"initrd\"\n                fi\n            fi\n        fi\n        \n        locate_initrd\n        \n        if [ -d (loop)/loader/entries ]; then\n            vt_linux_get_main_initrd_index vtindex\n            \n            if [ -d (loop)/arch ]; then\n                if [ -f (loop)/arch/boot/x86_64/archiso.img ]; then\n                    vt_add_replace_file $vtindex \"EFI\\\\archiso\\\\archiso.img\"\n                elif [ -f (loop)/arch/boot/x86_64/initramfs-linux.img ]; then\n                    vt_add_replace_file $vtindex \"arch\\\\boot\\\\x86_64\\\\initramfs-linux.img\"\n                elif [ -f (loop)/boot/initramfs_x86_64.img ]; then\n                    vt_add_replace_file $vtindex \"boot\\\\initramfs_x86_64.img\"\n                fi\n            elif [ -d (loop)/blackarch ]; then\n                if [ -f (loop)/blackarch/boot/x86_64/archiso.img ]; then\n                    vt_add_replace_file $vtindex \"EFI\\\\archiso\\\\archiso.img\"\n                elif [ -f (loop)/blackarch/boot/x86_64/initramfs-linux.img ]; then\n                    vt_add_replace_file $vtindex \"blackarch\\\\boot\\\\x86_64\\\\initramfs-linux.img\"\n                fi\n            elif [ -d (loop)/anarchy ]; then\n                if [ -f (loop)/anarchy/boot/x86_64/initramfs-linux.img ]; then\n                    vt_add_replace_file $vtindex \"anarchy\\\\boot\\\\x86_64\\\\initramfs-linux.img\"\n                fi\n            elif [ -d (loop)/parabola ]; then\n                if [ -f (loop)/parabola/boot/x86_64/parabolaiso.img ]; then\n                    vt_add_replace_file $vtindex \"EFI\\\\parabolaiso\\\\parabolaiso.img\"\n                elif [ -f (loop)/parabola/boot/x86_64/initramfs-linux-libre.img ]; then\n                    vt_add_replace_file $vtindex \"parabola\\\\boot\\\\x86_64\\\\initramfs-linux-libre.img\"\n                fi\n            elif [ -f (loop)/EFI/BOOT/initrd.gz ]; then\n                vt_add_replace_file $vtindex \"EFI\\\\BOOT\\\\initrd.gz\"\n            elif [ -f (loop)/loader/entries/thinstation.conf ]; then\n                vt_add_replace_file $vtindex \"boot\\\\initrd\"\n            elif [ -f (loop)/loader/entries/pisi-efi-x86_64.conf ]; then\n                vt_add_replace_file $vtindex \"EFI\\\\pisi\\\\initrd.img\"\n            fi\n\n            vt_get_replace_file_cnt vt_replace_cnt\n            if [ $vt_replace_cnt -eq 0 ]; then\n                uefi_find_replace_initrd \"$1\" \"$2\" $vtindex\n            fi\n        elif [ -d (loop)/EFI/boot/entries ]; then\n            if [ -f (loop)/parabola/boot/x86_64/parabolaiso.img ]; then\n                vt_add_replace_file 0 \"EFI\\\\parabolaiso\\\\parabolaiso.img\"\n            elif [ -f (loop)/hyperbola/boot/x86_64/hyperiso.img ]; then\n                vt_add_replace_file 0 \"EFI\\\\hyperiso\\\\hyperiso.img\"\n            fi\n        elif [ -d (loop)/EFI/BOOT/entries ]; then\n            vt_linux_get_main_initrd_index vtindex\n\n            if [ -f (loop)/parabola/boot/x86_64/parabolaiso.img ]; then\n                vt_add_replace_file 0 \"EFI\\\\parabolaiso\\\\parabolaiso.img\"\n            elif [ -f (loop)/parabola/boot/x86_64/initramfs-linux-libre.img ]; then\n                vt_add_replace_file $vtindex \"parabola\\\\boot\\\\x86_64\\\\initramfs-linux-libre.img\"\n            fi\n        elif [ -e (loop)/syslinux/alt0/full.cz ]; then\n            vt_add_replace_file 0 \"EFI\\\\BOOT\\\\full.cz\"            \n            set FirstTryBootFile='@EFI@<EMAIL>'\n            \n        elif vt_str_begin \"$vt_volume_id\" \"SolusLive\"; then\n            vt_add_replace_file 0 \"initrd\"\n\n        fi\n        \n    fi\n    \n    vt_linux_chain_data \"${1", "line_count": 113, "complexity": 38, "calls_functions": ["blackarch", "entries", "ile", "gz", "grub", "cfg", "t_add_replace_file", "d", "file", "t_str_begin", "ir", "vtindex", "rocess", "or", "t_linux_get_main_initrd_index", "t_get_replace_file_cnt", "vt_linux_clear_initrd", "pmagic", "oop", "vt_replace_cnt", "t_load_cpio", "ocate_initrd", "t_linux_parse_initrd_grub", "i", "img", "parabola", "one", "lse", "special", "istro_specify_initrd_file", "elif", "eq", "t_linux_chain_data", "conf", "et", "vtcount", "lif", "tindex", "t_replace_cnt", "efi_find_replace_initrd", "cz", "vtoy_path", "t_linux_specify_initrd_file", "pecial", "f", "e", "arch", "tcount", "istros", "oopback", "n", "iso9660", "anarchy", "istro_specify_initrd_file_phase2", "t_linux_initrd_count"], "uses_variables": ["vtoy_path", "ventoy_fs_probe", "ventoy_compatible", "vt_volume_id", "vt_replace_cnt", "ventoy_busybox_ver", "file", "vtcount", "vt_efi_dir", "vtindex"], "has_conditionals": true, "has_loops": true, "security_relevant": {"secure_boot": ["boot"]}, "boot_related": {"os_detection": ["linux"], "boot_methods": ["initrd"], "file_systems": ["iso9660", "udf"], "hardware": ["efi", "uefi", "x86", "x64"]}, "file_operations": ["(loop)/EFI/boot/livegrub.cfg", "(loop)/blackarch/boot/x86_64/initramfs-linux.img", "(loop)/loader/entries/thinstation.conf", "(loop)/syslinux/alt0/full.cz", "(loop)/$file", "(loop)/arch/boot/x86_64/archiso.img", "(loop)/parabola/boot/x86_64/parabolaiso.img", "(loop)/EFI/BOOT/initrd.gz", "(loop)/hyperbola/boot/x86_64/hyperiso.img", "(loop)/efi.img", "(loop)/parabola/boot/x86_64/initramfs-linux-libre.img", "(loop)/boot/initramfs_x86_64.img", "(loop)/anarchy/boot/x86_64/initramfs-linux.img", "(loop)/loader/entries/pisi-efi-x86_64.conf", "(loop)/arch/boot/x86_64/initramfs-linux.img", "$1$2", "(loop)/blackarch/boot/x86_64/archiso.img"], "system_calls": []}, "uefi_unix_menu_func": {"name": "uefi_unix_menu_func", "body": "ventoy_unix_comm_proc $1 \"${chosen_path", "line_count": 1, "complexity": 1, "calls_functions": ["ventoy_unix_comm_proc", "1"], "uses_variables": ["chosen_path"], "has_conditionals": false, "has_loops": false, "security_relevant": {}, "boot_related": {"os_detection": ["unix"]}, "file_operations": [], "system_calls": []}, "ventoy_reset_nojoliet": {"name": "ventoy_reset_nojoliet", "body": "if vt_str_begin \"$vt_volume_id\" \"ARCARESCUE\"; then\n        vt_iso9660_nojoliet 1\n    else\n        vt_iso9660_nojoliet 0\n    fi\n    \n    vt_append_extra_sector 0", "line_count": 7, "complexity": 2, "calls_functions": ["t_str_begin", "t_append_extra_sector", "t_iso9660_nojoliet"], "uses_variables": ["vt_volume_id"], "has_conditionals": true, "has_loops": false, "security_relevant": {}, "boot_related": {"file_systems": ["iso9660", "ext"]}, "file_operations": [], "system_calls": []}, "uefi_iso_menu_func": {"name": "uefi_iso_menu_func", "body": "if [ -n \"$vtisouefi\" ]; then\n        set LoadIsoEfiDriver=on\n        unset vtisouefi\n    elif vt_check_mode 2 \"$vt_chosen_name\"; then\n        set LoadIsoEfiDriver=on\n    else\n        unset LoadIsoEfiDriver\n    fi\n\n    set chosen_path=\"$2\"\n    vt_select_auto_install \"${chosen_path", "line_count": 11, "complexity": 3, "calls_functions": ["t_check_mode", "lse", "tis<PERSON><PERSON>i", "n", "nset", "vt_select_auto_install", "et", "i", "lif", "on", "oadIsoEfiDriver"], "uses_variables": ["vtisouefi", "vt_chosen_name", "chosen_path"], "has_conditionals": true, "has_loops": false, "security_relevant": {"verification": ["check"]}, "boot_related": {"hardware": ["efi", "uefi"]}, "file_operations": [], "system_calls": []}, "uefi_iso_memdisk": {"name": "uefi_iso_memdisk", "body": "echo 'Loading ISO file to memory ...'\n    vt_load_img_memdisk \"${1", "line_count": 2, "complexity": 1, "calls_functions": ["Loading", "ile", "echo", "o", "SO", "emory", "vt_load_img_memdisk"], "uses_variables": [], "has_conditionals": false, "has_loops": false, "security_relevant": {}, "boot_related": {"boot_methods": ["memdisk"]}, "file_operations": [], "system_calls": []}, "vtoy_windows_wimboot": {"name": "vtoy_windows_wimboot", "body": "if [ -f (loop)/x86/sources/boot.wim -a -f (loop)/x64/sources/boot.wim ]; then\n        vt_sel_wimboot vtoy_wimboot_bit\n        if [ \"$vtoy_wimboot_bit\" = \"32\" ]; then\n            set vtoy_wimboot_prefix=(loop)/x86\n        else\n            set vtoy_wimboot_prefix=(loop)/x64\n        fi\n    else\n        set vtoy_wimboot_prefix=(loop)\n        if vt_is_pe64 $vtoy_wimboot_prefix/setup.exe; then\n            set vtoy_wimboot_bit=64\n        else\n            set vtoy_wimboot_bit=32\n        fi\n    fi\n\n    if [ -n \"${vtdebug_flag", "line_count": 17, "complexity": 5, "calls_functions": ["t_sel_wimboot", "64", "lse", "f", "x64", "a", "x86", "t_is_pe64", "toy_wimboot_bit", "n", "wim", "et", "i", "32"], "uses_variables": ["vtdebug_flag", "vtoy_wimboot_prefix", "vtoy_wimboot_bit"], "has_conditionals": true, "has_loops": false, "security_relevant": {"secure_boot": ["boot"]}, "boot_related": {"boot_methods": ["wimboot"], "hardware": ["efi", "x86", "x64"]}, "file_operations": [], "system_calls": []}, "vtoy_winpe_wimboot": {"name": "vtoy_winpe_wimboot", "body": "unset vtoy_boot_sdi_legacy\n    unset vtoy_boot_sdi_efi\n    \n    set vtoy_wimboot_prefix=(loop)    \n    set vtoy_wim_path=\"$1\"\n    \n    if [ -n \"${vtdebug_flag", "line_count": 7, "complexity": 2, "calls_functions": ["toy_boot_sdi_legacy", "n", "nset", "toy_boot_sdi_efi", "et"], "uses_variables": ["vtdebug_flag"], "has_conditionals": true, "has_loops": false, "security_relevant": {"secure_boot": ["boot"]}, "boot_related": {"boot_methods": ["wimboot"], "hardware": ["efi", "legacy"]}, "file_operations": [], "system_calls": []}, "vtoy_wimboot_func": {"name": "vtoy_wimboot_func", "body": "if [ \"$grub_platform\" = \"pc\" ]; then\n        set vt_wimkernel=wimboot.x86_64.xz        \n    else\n        if [ \"$grub_cpu\" = \"i386\" ]; then\n            set vt_wimkernel=wimboot.i386.efi.xz\n        else\n            set vt_wimkernel=wimboot.x86_64.xz\n        fi\n    fi\n\n    if vt_is_standard_winiso (loop); then\n        echo -e \"\\n==================== VENTOY WIMBOOT ==================\\n\"\n        vtoy_windows_wimboot\n    else\n        vt_sel_winpe_wim (loop)\n        if [ -n \"$vtoy_pe_wim_path\" ]; then            \n            echo -e \"\\n==================== VENTOY WIMBOOT ==================\\n\"\n            \n            vt_fs_ignore_case 1\n            vt_load_file_to_mem \"auto\" $vtoy_path/common_bcd.xz vtoy_pe_bcd_mem\n            \n            set vt_sdi_path=0\n            for vsdi in \"boot/boot.sdi\" \"2K10/FONTS/boot.sdi\" \"SSTR/boot.sdi\" \"ISPE/BOOT.SDI\" \\\n            \"boot/uqi.sdi\" \"ISYL/boot.sdi\" \"WEPE/WEPE.SDI\" ; do\n                if [ -F \"(loop)/$vsdi\" ]; then\n                    set vt_sdi_path=$vsdi\n                    break\n                fi\n            done\n            \n            if [ \"$grub_platform\" = \"pc\" ]; then\n                vt_load_file_to_mem \"auto\" $vtoy_path/common_bootmgr.xz vtoy_pe_bootmgr_mem\n                vtoy_winpe_wimboot \"$vtoy_pe_wim_path\" \"$vt_sdi_path\" 1\n            else\n                vtoy_winpe_wimboot \"$vtoy_pe_wim_path\" \"$vt_sdi_path\" 0\n            fi\n            \n            vt_fs_ignore_case 0\n        fi\n    fi", "line_count": 40, "complexity": 8, "calls_functions": ["sdi", "or", "t_load_file_to_mem", "toy_pe_bcd_mem", "t_sel_winpe_wim", "0", "cho", "VENTOY", "i", "1", "one", "lse", "t_is_standard_winiso", "reak", "vtoy_windows_wimboot", "toy_pe_bootmgr_mem", "f", "e", "F", "xz", "n", "vsdi", "vt_load_file_to_mem", "t_fs_ignore_case", "et", "vt_fs_ignore_case", "toy_winpe_wimboot"], "uses_variables": ["vtoy_path", "vtoy_pe_wim_path", "grub_platform", "vt_sdi_path", "vsdi", "grub_cpu"], "has_conditionals": true, "has_loops": true, "security_relevant": {"secure_boot": ["boot"]}, "boot_related": {"os_detection": ["windows"], "boot_methods": ["wimboot"], "hardware": ["efi", "x86"]}, "file_operations": ["auto"], "system_calls": ["boot"]}, "legacy_windows_menu_func": {"name": "legacy_windows_menu_func", "body": "vt_windows_reset\n    \n    unset vt_cur_wimboot_mode\n    if vt_check_mode 4 \"$vt_chosen_name\"; then\n        set vt_cur_wimboot_mode=1\n    fi\n    \n    if [ \"$ventoy_compatible\" = \"NO\" -o \"$vt_cur_wimboot_mode\" = \"1\" ]; then\n        if [ \"$ventoy_fs_probe\" = \"iso9660\" ]; then\n            loopback -d loop\n            vt_iso9660_nojoliet 1\n            loopback loop \"$1$2\"\n        fi\n        \n        for file in \"boot/bcd\" \"/efi/microsoft/boot/bcd\" \"SSTR/BCD\" \"boot/bce\"; do\n            vt_windows_collect_wim_patch bcd (loop)/$file                \n        done\n        \n        distro_specify_wim_patch\n\n        vt_windows_count_wim_patch vt_wim_cnt\n        if [ $vt_wim_cnt -eq 0 ]; then\n            distro_specify_wim_patch_phase2\n        fi\n        \n        ventoy_debug_pause\n        if [ -z \"$vt_cur_wimboot_mode\" ]; then\n            locate_wim \"${chosen_path", "line_count": 28, "complexity": 7, "calls_functions": ["t_check_mode", "ile", "vt_wim_cnt", "loopback", "t_wim_cnt", "d", "t_iso9660_nojoliet", "file", "t_windows_count_wim_patch", "or", "istro_specify_wim_patch_phase2", "o", "oop", "i", "1", "cd", "one", "ocate_wim", "t_windows_collect_wim_patch", "eq", "z", "entoy_debug_pause", "t_cur_wimboot_mode", "f", "oopback", "n", "nset", "et", "istro_specify_wim_patch", "vt_windows_reset"], "uses_variables": ["vt_wim_cnt", "chosen_path", "ventoy_fs_probe", "ventoy_compatible", "vt_chosen_name", "vt_cur_wimboot_mode", "file"], "has_conditionals": true, "has_loops": true, "security_relevant": {"verification": ["check"], "secure_boot": ["boot"]}, "boot_related": {"os_detection": ["windows"], "boot_methods": ["wimboot"], "file_systems": ["iso9660"], "hardware": ["efi"]}, "file_operations": ["$1$2"], "system_calls": []}, "legacy_linux_menu_func": {"name": "legacy_linux_menu_func", "body": "if [ \"$ventoy_compatible\" = \"NO\" ]; then\n\n        if [ \"$ventoy_fs_probe\" = \"udf\" ]; then\n            loopback -d loop\n            set ventoy_fs_probe=iso9660\n            loopback loop \"$1$2\"\n        fi\n\n        \n        if vt_syslinux_need_nojoliet \"$1$2\"; then\n            vt_iso9660_nojoliet 1\n            loopback -d loop\n            loopback loop \"$1$2\"\n        fi\n\n        vt_load_cpio  $vtoy_path  \"$2\" \"$1\" \"busybox=$ventoy_busybox_ver\"\n\n        vt_linux_clear_initrd\n        \n        if [ -d (loop)/pmagic ]; then\n            vt_linux_specify_initrd_file /pmagic/initrd.img\n        else\n            for dir in \"isolinux\" \"boot/isolinux\" \"boot/x86_64/loader\" \"syslinux\" \"boot/syslinux\"; do\n                if [ -d (loop)/$dir ]; then\n                    vt_linux_parse_initrd_isolinux   (loop)/$dir/\n                fi\n            done\n        fi\n        \n        # special process for special distros\n        #archlinux\n        if [ -d (loop)/arch/boot/syslinux ]; then\n            vt_linux_parse_initrd_isolinux   (loop)/arch/boot/syslinux/  /arch/\n            vt_linux_parse_initrd_isolinux   (loop)/arch/boot/syslinux/  /arch/boot/syslinux/\n        elif [ -d (loop)/anarchy/boot/syslinux ]; then\n            vt_linux_parse_initrd_isolinux   (loop)/anarchy/boot/syslinux/  /anarchy/\n            \n        #manjaro\n        elif [ -d (loop)/manjaro ]; then\n            if [ -e (loop)/boot/grub/kernels.cfg ]; then\n                vt_linux_parse_initrd_grub  file  (loop)/boot/grub/kernels.cfg\n            fi\n        elif [ -e (loop)/boot/grub/grub.cfg ]; then                \n            vt_linux_parse_initrd_grub  file  (loop)/boot/grub/grub.cfg\n        fi\n        \n        distro_specify_initrd_file\n        \n        vt_linux_initrd_count vtcount\n        if [ $vtcount -eq 0 ]; then\n            if [ -d (loop)/rancheros ]; then\n                vt_linux_parse_initrd_isolinux   (loop)/boot/  /boot/isolinux/\n            fi\n\n            distro_specify_initrd_file_phase2\n        fi\n        \n        locate_initrd\n    fi\n    \n    vt_linux_chain_data \"${1", "line_count": 61, "complexity": 15, "calls_functions": ["ile", "cfg", "loopback", "d", "t_iso9660_nojoliet", "ir", "<PERSON><PERSON><PERSON>", "rocess", "or", "dir", "vt_linux_clear_initrd", "t_linux_parse_initrd_isolinux", "pmagic", "oop", "t_load_cpio", "ocate_initrd", "t_linux_parse_initrd_grub", "i", "img", "one", "manjaro", "lse", "t_syslinux_need_nojoliet", "special", "istro_specify_initrd_file", "elif", "eq", "vt_linux_parse_initrd_isolinux", "rancheros", "t_linux_chain_data", "et", "syslinux", "lif", "vtcount", "vtoy_path", "t_linux_specify_initrd_file", "pecial", "f", "e", "tcount", "istros", "oopback", "n", "iso9660", "istro_specify_initrd_file_phase2", "t_linux_initrd_count"], "uses_variables": ["dir", "vtoy_path", "ventoy_fs_probe", "ventoy_compatible", "ventoy_busybox_ver", "vtcount"], "has_conditionals": true, "has_loops": true, "security_relevant": {"secure_boot": ["boot"]}, "boot_related": {"os_detection": ["linux"], "boot_methods": ["initrd"], "file_systems": ["iso9660", "udf"], "hardware": ["x86"]}, "file_operations": ["(loop)/boot/grub/kernels.cfg", "(loop)/boot/grub/grub.cfg", "$1$2"], "system_calls": []}, "legacy_unix_menu_func": {"name": "legacy_unix_menu_func", "body": "ventoy_unix_comm_proc $1 \"${chosen_path", "line_count": 1, "complexity": 1, "calls_functions": ["ventoy_unix_comm_proc", "1"], "uses_variables": ["chosen_path"], "has_conditionals": false, "has_loops": false, "security_relevant": {}, "boot_related": {"os_detection": ["unix"]}, "file_operations": [], "system_calls": []}, "legacy_iso_menu_func": {"name": "legacy_iso_menu_func", "body": "set chosen_path=\"$2\"\n    \n    vt_select_auto_install \"${chosen_path", "line_count": 3, "complexity": 1, "calls_functions": ["vt_select_auto_install"], "uses_variables": ["chosen_path"], "has_conditionals": false, "has_loops": false, "security_relevant": {}, "boot_related": {}, "file_operations": [], "system_calls": []}, "legacy_iso_memdisk": {"name": "legacy_iso_memdisk", "body": "linux16   $vtoy_path/memdisk iso raw    \n    echo \"Loading ISO file to memory ...\"\n    initrd16  \"${1", "line_count": 3, "complexity": 1, "calls_functions": ["aw", "linux16", "memdisk", "Loading", "ile", "so", "o", "SO", "cho", "emory", "initrd16"], "uses_variables": ["vtoy_path"], "has_conditionals": false, "has_loops": false, "security_relevant": {}, "boot_related": {"os_detection": ["linux"], "boot_methods": ["linux16", "initrd", "memdisk"]}, "file_operations": [], "system_calls": ["initrd16", "linux16"]}, "iso_endless_os_proc": {"name": "iso_endless_os_proc", "body": "if [ -d (loop)/ ]; then\n        loopback -d loop\n    fi\n\n    loopback loop \"${1", "line_count": 5, "complexity": 2, "calls_functions": ["oop", "d", "i", "oopback"], "uses_variables": [], "has_conditionals": true, "has_loops": false, "security_relevant": {}, "boot_related": {}, "file_operations": [], "system_calls": []}, "ventoy_iso_busybox_ver": {"name": "ventoy_iso_busybox_ver", "body": "if [ \"$VTOY_EFI_ARCH\" = \"aa64\" ]; then\n        set ventoy_busybox_ver=a64\n    elif [ \"$VTOY_EFI_ARCH\" = \"mips\" ]; then\n        set ventoy_busybox_ver=m64\n    else\n        set ventoy_busybox_ver=32\n    \n        #special process for deepin-live iso\n        if [ \"$vt_chosen_size\" = \"403701760\" ]; then\n            if vt_str_str \"$vt_chosen_path\" \"/deepin-live\"; then\n                set ventoy_busybox_ver=64\n            fi\n        elif vt_str_begin \"$vt_volume_id\" \"PHOTON_\"; then\n            set ventoy_busybox_ver=64\n        elif vt_str_begin \"$vt_volume_id\" \"smgl-test-quinq-x86_64\"; then\n            set ventoy_busybox_ver=64\n        elif vt_str_begin \"$vt_volume_id\" \"LDiagBootable\"; then\n            set ventoy_busybox_ver=64\n        elif vt_str_begin \"$vt_volume_id\" \"KAOS_\"; then\n            set ventoy_busybox_ver=64\n\n        fi\n    fi", "line_count": 23, "complexity": 10, "calls_functions": ["or", "64", "lse", "m64", "t_str_str", "f", "special", "live", "t_str_begin", "so", "a64", "et", "i", "lif", "32", "rocess"], "uses_variables": ["vt_volume_id", "vt_chosen_size", "VTOY_EFI_ARCH", "vt_chosen_path"], "has_conditionals": true, "has_loops": true, "security_relevant": {"secure_boot": ["boot"]}, "boot_related": {"hardware": ["efi", "x86"]}, "file_operations": [], "system_calls": []}, "iso_common_menuentry": {"name": "iso_common_menuentry", "body": "unset vt_system_id\n    unset vt_volume_id\n    \n    vt_chosen_img_path vt_chosen_path vt_chosen_size vt_chosen_name\n\n    vt_parse_iso_volume \"${vtoy_iso_part", "line_count": 6, "complexity": 1, "calls_functions": ["t_chosen_name", "t_chosen_path", "t_chosen_img_path", "t_volume_id", "t_chosen_size", "t_parse_iso_volume", "nset", "t_system_id"], "uses_variables": ["vtoy_iso_part"], "has_conditionals": false, "has_loops": false, "security_relevant": {}, "boot_related": {}, "file_operations": [], "system_calls": []}, "miso_common_menuentry": {"name": "miso_common_menuentry", "body": "vt_chosen_img_path vt_chosen_path vt_chosen_size vt_chosen_name\n\n    if vt_check_password \"${vt_chosen_path", "line_count": 3, "complexity": 2, "calls_functions": ["t_check_password", "t_chosen_name", "t_chosen_path", "f", "t_chosen_size", "vt_chosen_img_path"], "uses_variables": ["vt_chosen_path"], "has_conditionals": true, "has_loops": false, "security_relevant": {"authentication": ["password"], "verification": ["check"]}, "boot_related": {}, "file_operations": [], "system_calls": []}, "common_unsupport_menuentry": {"name": "common_unsupport_menuentry", "body": "echo -e \"\\n The name of the iso file could NOT contain space or non-ascii characters. \\n\"\n    echo -e \" 文件名中不能有中文或空格 \\n\"    \n    echo -en \"\\n$VTLANG_ENTER_EXIT ...\"  \n    read vtInputKey", "line_count": 4, "complexity": 1, "calls_functions": ["ontain", "e", "f", "ile", "echo", "pace", "en", "so", "ould", "ame", "he", "OT", "r", "read", "n", "VTLANG_ENTER_EXIT", "ascii", "文件名中不能有中文或空格"], "uses_variables": ["VTLANG_ENTER_EXIT"], "has_conditionals": false, "has_loops": false, "security_relevant": {"encryption": ["key"]}, "boot_related": {}, "file_operations": [], "system_calls": []}, "miso_unsupport_menuentry": {"name": "miso_unsupport_menuentry", "body": "common_unsupport_menuentry", "line_count": 1, "complexity": 1, "calls_functions": [], "uses_variables": [], "has_conditionals": false, "has_loops": false, "security_relevant": {}, "boot_related": {}, "file_operations": [], "system_calls": []}, "iso_unsupport_menuentry": {"name": "iso_unsupport_menuentry", "body": "common_unsupport_menuentry", "line_count": 1, "complexity": 1, "calls_functions": [], "uses_variables": [], "has_conditionals": false, "has_loops": false, "security_relevant": {}, "boot_related": {}, "file_operations": [], "system_calls": []}, "wim_common_menuentry": {"name": "wim_common_menuentry", "body": "vt_chosen_img_path vt_chosen_path vt_chosen_size vt_chosen_name\n    \n    if vt_check_password \"${vt_chosen_path", "line_count": 3, "complexity": 2, "calls_functions": ["t_check_password", "t_chosen_name", "t_chosen_path", "f", "t_chosen_size", "vt_chosen_img_path"], "uses_variables": ["vt_chosen_path"], "has_conditionals": true, "has_loops": false, "security_relevant": {"authentication": ["password"], "verification": ["check"]}, "boot_related": {}, "file_operations": [], "system_calls": []}, "wim_unsupport_menuentry": {"name": "wim_unsupport_menuentry", "body": "common_unsupport_menuentry", "line_count": 1, "complexity": 1, "calls_functions": [], "uses_variables": [], "has_conditionals": false, "has_loops": false, "security_relevant": {}, "boot_related": {}, "file_operations": [], "system_calls": []}, "efi_common_menuentry": {"name": "efi_common_menuentry", "body": "vt_chosen_img_path vt_chosen_path vt_chosen_size vt_chosen_name\n    \n    if vt_check_password \"${vt_chosen_path", "line_count": 3, "complexity": 2, "calls_functions": ["t_check_password", "t_chosen_name", "t_chosen_path", "f", "t_chosen_size", "vt_chosen_img_path"], "uses_variables": ["vt_chosen_path"], "has_conditionals": true, "has_loops": false, "security_relevant": {"authentication": ["password"], "verification": ["check"]}, "boot_related": {}, "file_operations": [], "system_calls": []}, "efi_unsupport_menuentry": {"name": "efi_unsupport_menuentry", "body": "common_unsupport_menuentry", "line_count": 1, "complexity": 1, "calls_functions": [], "uses_variables": [], "has_conditionals": false, "has_loops": false, "security_relevant": {}, "boot_related": {}, "file_operations": [], "system_calls": []}, "vhdboot_common_func": {"name": "vhdboot_common_func", "body": "vt_patch_vhdboot \"$1\"\n    \n    ventoy_debug_pause    \n    \n    if [ -n \"$vtoy_vhd_buf_addr\" ]; then\n        if [ \"$grub_platform\" = \"pc\" ]; then\n            ventoy_cli_console\n            linux16   $vtoy_path/memdisk iso raw    \n            initrd16  mem:${vtoy_vhd_buf_addr", "line_count": 9, "complexity": 3, "calls_functions": ["nitrd16", "aw", "vt_patch_vhdboot", "f", "memdisk", "ventoy_debug_pause", "entoy_cli_console", "so", "n", "inux16"], "uses_variables": ["grub_platform", "vtoy_path", "vtoy_vhd_buf_addr"], "has_conditionals": true, "has_loops": false, "security_relevant": {"secure_boot": ["boot"]}, "boot_related": {"os_detection": ["linux"], "boot_methods": ["linux16", "initrd", "memdisk"]}, "file_operations": [], "system_calls": ["initrd16", "linux16"]}, "vhd_common_menuentry": {"name": "vhd_common_menuentry", "body": "vt_chosen_img_path vt_chosen_path vt_chosen_size vt_chosen_name\n    \n    if vt_check_password \"${vt_chosen_path", "line_count": 3, "complexity": 2, "calls_functions": ["t_check_password", "t_chosen_name", "t_chosen_path", "f", "t_chosen_size", "vt_chosen_img_path"], "uses_variables": ["vt_chosen_path"], "has_conditionals": true, "has_loops": false, "security_relevant": {"authentication": ["password"], "verification": ["check"]}, "boot_related": {}, "file_operations": [], "system_calls": []}, "vhd_unsupport_menuentry": {"name": "vhd_unsupport_menuentry", "body": "common_unsupport_menuentry", "line_count": 1, "complexity": 1, "calls_functions": [], "uses_variables": [], "has_conditionals": false, "has_loops": false, "security_relevant": {}, "boot_related": {}, "file_operations": [], "system_calls": []}, "vtoyboot_common_func": {"name": "vtoyboot_common_func", "body": "set AltBootPart=0\n    set vtoysupport=0\n    \n    vt_get_vtoy_type \"${1", "line_count": 4, "complexity": 1, "calls_functions": ["et", "0", "t_get_vtoy_type"], "uses_variables": [], "has_conditionals": false, "has_loops": false, "security_relevant": {"secure_boot": ["boot"]}, "boot_related": {}, "file_operations": [], "system_calls": []}, "vtoy_common_menuentry": {"name": "vtoy_common_menuentry", "body": "vt_chosen_img_path vt_chosen_path vt_chosen_size vt_chosen_name\n    \n    if vt_check_password \"${vt_chosen_path", "line_count": 3, "complexity": 2, "calls_functions": ["t_check_password", "t_chosen_name", "t_chosen_path", "f", "t_chosen_size", "vt_chosen_img_path"], "uses_variables": ["vt_chosen_path"], "has_conditionals": true, "has_loops": false, "security_relevant": {"authentication": ["password"], "verification": ["check"]}, "boot_related": {}, "file_operations": [], "system_calls": []}, "vtoy_unsupport_menuentry": {"name": "vtoy_unsupport_menuentry", "body": "common_unsupport_menuentry", "line_count": 1, "complexity": 1, "calls_functions": [], "uses_variables": [], "has_conditionals": false, "has_loops": false, "security_relevant": {}, "boot_related": {}, "file_operations": [], "system_calls": []}, "only_uefi_tip": {"name": "only_uefi_tip", "body": "echo -e \"\\n This IMG file is only supported in UEFI mode. \\n\"\n    echo -en \"\\n$VTLANG_ENTER_EXIT ...\"\n    read vtInputKey", "line_count": 3, "complexity": 1, "calls_functions": ["MG", "his", "nly", "e", "ile", "echo", "en", "EFI", "upported", "read", "n", "VTLANG_ENTER_EXIT", "s"], "uses_variables": ["VTLANG_ENTER_EXIT"], "has_conditionals": false, "has_loops": false, "security_relevant": {"encryption": ["key"]}, "boot_related": {"hardware": ["efi", "uefi"]}, "file_operations": [], "system_calls": []}, "ventoy_img_easyos": {"name": "ventoy_img_easyos", "body": "vt_load_cpio  $vtoy_path  \"${vt_chosen_path", "line_count": 1, "complexity": 1, "calls_functions": ["vtoy_path", "vt_load_cpio"], "uses_variables": ["vtoy_path", "vt_chosen_path"], "has_conditionals": false, "has_loops": false, "security_relevant": {}, "boot_related": {}, "file_operations": [], "system_calls": []}, "ventoy_img_easyos2": {"name": "ventoy_img_easyos2", "body": "vt_load_cpio  $vtoy_path  \"${vt_chosen_path", "line_count": 1, "complexity": 1, "calls_functions": ["vtoy_path", "vt_load_cpio"], "uses_variables": ["vtoy_path", "vt_chosen_path"], "has_conditionals": false, "has_loops": false, "security_relevant": {}, "boot_related": {}, "file_operations": [], "system_calls": []}, "ventoy_img_volumio": {"name": "ventoy_img_volumio", "body": "vt_load_cpio  $vtoy_path  \"${vt_chosen_path", "line_count": 1, "complexity": 1, "calls_functions": ["vtoy_path", "vt_load_cpio"], "uses_variables": ["vtoy_path", "vt_chosen_path"], "has_conditionals": false, "has_loops": false, "security_relevant": {}, "boot_related": {}, "file_operations": [], "system_calls": []}, "ventoy_img_openelec": {"name": "ventoy_img_openelec", "body": "elec_ver=$1\n    \n    vt_load_cpio  $vtoy_path  \"${vt_chosen_path", "line_count": 3, "complexity": 1, "calls_functions": ["vtoy_path", "1", "t_load_cpio"], "uses_variables": ["vtoy_path", "vt_chosen_path"], "has_conditionals": false, "has_loops": false, "security_relevant": {}, "boot_related": {}, "file_operations": [], "system_calls": []}, "ventoy_img_freedombox": {"name": "ventoy_img_freedombox", "body": "vt_load_cpio  $vtoy_path  \"${vt_chosen_path", "line_count": 1, "complexity": 1, "calls_functions": ["vtoy_path", "vt_load_cpio"], "uses_variables": ["vtoy_path", "vt_chosen_path"], "has_conditionals": false, "has_loops": false, "security_relevant": {}, "boot_related": {}, "file_operations": [], "system_calls": []}, "ventoy_img_paldo": {"name": "ventoy_img_paldo", "body": "vt_load_cpio  $vtoy_path  \"${vt_chosen_path", "line_count": 1, "complexity": 1, "calls_functions": ["vtoy_path", "vt_load_cpio"], "uses_variables": ["vtoy_path", "vt_chosen_path"], "has_conditionals": false, "has_loops": false, "security_relevant": {}, "boot_related": {}, "file_operations": [], "system_calls": []}, "ventoy_img_ubos": {"name": "ventoy_img_ubos", "body": "vt_load_cpio  $vtoy_path  \"${vt_chosen_path", "line_count": 1, "complexity": 1, "calls_functions": ["vtoy_path", "vt_load_cpio"], "uses_variables": ["vtoy_path", "vt_chosen_path"], "has_conditionals": false, "has_loops": false, "security_relevant": {}, "boot_related": {}, "file_operations": [], "system_calls": []}, "ventoy_img_recalbox": {"name": "ventoy_img_recalbox", "body": "if [ $vtoy_img_max_part_end -GT $vt_chosen_size ]; then\n        echo -e \"\\nPlease extend the img file size before boot it. \\n\"\n        ventoy_pause\n        return\n    fi\n\n    vt_load_cpio  $vtoy_path  \"${vt_chosen_path", "line_count": 7, "complexity": 2, "calls_functions": ["oot", "vtoy_path", "vt_chosen_size", "nPlease", "vtoy_img_max_part_end", "e", "ile", "ize", "xtend", "he", "ventoy_pause", "mg", "eturn", "efore", "cho", "t_load_cpio", "GT", "i"], "uses_variables": ["vtoy_path", "vt_chosen_size", "vt_chosen_path", "vtoy_img_max_part_end"], "has_conditionals": true, "has_loops": false, "security_relevant": {"secure_boot": ["boot"]}, "boot_related": {"file_systems": ["ext"]}, "file_operations": [], "system_calls": []}, "ventoy_img_esysrescue": {"name": "ventoy_img_esysrescue", "body": "vt_load_cpio  $vtoy_path  \"${vt_chosen_path", "line_count": 1, "complexity": 1, "calls_functions": ["vtoy_path", "vt_load_cpio"], "uses_variables": ["vtoy_path", "vt_chosen_path"], "has_conditionals": false, "has_loops": false, "security_relevant": {}, "boot_related": {}, "file_operations": [], "system_calls": []}, "ventoy_img_batocera": {"name": "ventoy_img_batocera", "body": "vt_load_cpio  $vtoy_path  \"${vt_chosen_path", "line_count": 1, "complexity": 1, "calls_functions": ["vtoy_path", "vt_load_cpio"], "uses_variables": ["vtoy_path", "vt_chosen_path"], "has_conditionals": false, "has_loops": false, "security_relevant": {}, "boot_related": {}, "file_operations": [], "system_calls": []}, "ventoy_img_openwrt": {"name": "ventoy_img_openwrt", "body": "if [ -e (vtimghd,2)/lib64 ]; then\n        set ventoy_busybox_ver=64\n    fi\n\n    vt_fs_enum_1st_dir (vtimghd,2) /lib/modules/ vt_dir_name\n\n    if [ -f (vtimghd,2)/lib/modules/$vt_dir_name/dm-mod.ko ]; then\n        set openwrt_plugin_need=0\n        vt_img_extra_initrd_append  (vtimghd,2)/lib/modules/$vt_dir_name/dm-mod.ko\n        if [ -f (vtimghd,2)/lib/modules/$vt_dir_name/dax.ko ]; then\n            vt_img_extra_initrd_append  (vtimghd,2)/lib/modules/$vt_dir_name/dax.ko\n        fi\n    else\n        set openwrt_plugin_need=1\n        if [ ! -f ${vtoy_iso_part", "line_count": 15, "complexity": 5, "calls_functions": ["64", "lse", "e", "f", "ko", "t_img_extra_initrd_append", "lib64", "t_fs_enum_1st_dir", "0", "vt_dir_name", "et", "i", "1"], "uses_variables": ["vt_dir_name", "vtoy_iso_part"], "has_conditionals": true, "has_loops": false, "security_relevant": {}, "boot_related": {"boot_methods": ["initrd"], "file_systems": ["ext"]}, "file_operations": ["(vtimghd,2)/lib64", "(vtimghd,2)/lib/modules/$vt_dir_name/dax.ko", "(vtimghd,2)/lib/modules/$vt_dir_name/dm-mod.ko"], "system_calls": []}, "ventoy_img_tails": {"name": "ventoy_img_tails", "body": "vt_load_cpio  $vtoy_path  \"${vt_chosen_path", "line_count": 1, "complexity": 1, "calls_functions": ["vtoy_path", "vt_load_cpio"], "uses_variables": ["vtoy_path", "vt_chosen_path"], "has_conditionals": false, "has_loops": false, "security_relevant": {}, "boot_related": {}, "file_operations": [], "system_calls": []}, "ventoy_img_fydeos": {"name": "ventoy_img_fydeos", "body": "if [ \"$grub_platform\" = \"pc\" ]; then\n        only_uefi_tip\n        return\n    fi\n\n    vt_load_cpio  $vtoy_path  \"${vt_chosen_path", "line_count": 6, "complexity": 2, "calls_functions": ["vtoy_path", "eturn", "t_load_cpio", "i", "nly_uefi_tip"], "uses_variables": ["grub_platform", "vtoy_path", "vt_chosen_path"], "has_conditionals": true, "has_loops": false, "security_relevant": {}, "boot_related": {"hardware": ["efi", "uefi"]}, "file_operations": [], "system_calls": []}, "ventoy_img_cloudready": {"name": "ventoy_img_cloudready", "body": "if [ \"$grub_platform\" = \"pc\" ]; then\n        only_uefi_tip\n        return\n    fi\n\n    vt_load_cpio  $vtoy_path  \"${vt_chosen_path", "line_count": 6, "complexity": 2, "calls_functions": ["vtoy_path", "eturn", "t_load_cpio", "i", "nly_uefi_tip"], "uses_variables": ["grub_platform", "vtoy_path", "vt_chosen_path"], "has_conditionals": true, "has_loops": false, "security_relevant": {}, "boot_related": {"hardware": ["efi", "uefi"]}, "file_operations": [], "system_calls": []}, "ventoy_img_fwts": {"name": "ventoy_img_fwts", "body": "vt_load_cpio  $vtoy_path  \"${vt_chosen_path", "line_count": 1, "complexity": 1, "calls_functions": ["vtoy_path", "vt_load_cpio"], "uses_variables": ["vtoy_path", "vt_chosen_path"], "has_conditionals": false, "has_loops": false, "security_relevant": {}, "boot_related": {}, "file_operations": [], "system_calls": []}, "ventoy_img_memtest86": {"name": "ventoy_img_memtest86", "body": "chainloader (vtimghd,1)/efi/boot/BOOTX64.efi\n    boot", "line_count": 2, "complexity": 1, "calls_functions": ["efi", "chainloader"], "uses_variables": [], "has_conditionals": false, "has_loops": false, "security_relevant": {"secure_boot": ["boot"]}, "boot_related": {"boot_methods": ["chainloader"], "hardware": ["efi", "x64"]}, "file_operations": [], "system_calls": ["boot", "chainloader"]}, "img_unsupport_tip": {"name": "img_unsupport_tip", "body": "echo -e \"\\n This IMG file is NOT supported now. \\n\"\n    echo -e \" 当前不支持启动此 IMG 文件 \\n\"    \n    echo -en \"\\n$VTLANG_ENTER_EXIT ...\"\n    read vtInputKey", "line_count": 4, "complexity": 1, "calls_functions": ["MG", "his", "e", "ile", "echo", "en", "OT", "upported", "件", "read", "n", "当前不支持启动此", "VTLANG_ENTER_EXIT", "s"], "uses_variables": ["VTLANG_ENTER_EXIT"], "has_conditionals": false, "has_loops": false, "security_relevant": {"encryption": ["key"]}, "boot_related": {}, "file_operations": [], "system_calls": []}, "legacy_img_memdisk": {"name": "legacy_img_memdisk", "body": "linux16   $vtoy_path/memdisk\n    echo \"Loading img file to memory ...\"\n    initrd16  \"${1", "line_count": 3, "complexity": 1, "calls_functions": ["linux16", "memdisk", "Loading", "ile", "o", "mg", "cho", "emory", "initrd16"], "uses_variables": ["vtoy_path"], "has_conditionals": false, "has_loops": false, "security_relevant": {}, "boot_related": {"os_detection": ["linux"], "boot_methods": ["linux16", "initrd", "memdisk"]}, "file_operations": [], "system_calls": ["initrd16", "linux16"]}, "img_common_menuentry": {"name": "img_common_menuentry", "body": "set ventoy_compatible=YES\n    set ventoy_busybox_ver=32\n    unset LoadIsoEfiDriver\n\n    vt_chosen_img_path vt_chosen_path vt_chosen_size vt_chosen_name\n    \n    if vt_check_password \"${vt_chosen_path", "line_count": 7, "complexity": 2, "calls_functions": ["t_check_password", "t_chosen_name", "YES", "t_chosen_path", "f", "t_chosen_img_path", "t_chosen_size", "nset", "et", "oadIsoEfiDriver", "32"], "uses_variables": ["vt_chosen_path"], "has_conditionals": true, "has_loops": false, "security_relevant": {"authentication": ["password"], "verification": ["check"]}, "boot_related": {"hardware": ["efi"]}, "file_operations": [], "system_calls": []}, "img_unsupport_menuentry": {"name": "img_unsupport_menuentry", "body": "common_unsupport_menuentry", "line_count": 1, "complexity": 1, "calls_functions": [], "uses_variables": [], "has_conditionals": false, "has_loops": false, "security_relevant": {}, "boot_related": {}, "file_operations": [], "system_calls": []}, "mimg_common_menuentry": {"name": "mimg_common_menuentry", "body": "vt_chosen_img_path vt_chosen_path vt_chosen_size vt_chosen_name\n\n    if vt_check_password \"${vt_chosen_path", "line_count": 3, "complexity": 2, "calls_functions": ["t_check_password", "t_chosen_name", "t_chosen_path", "f", "t_chosen_size", "vt_chosen_img_path"], "uses_variables": ["vt_chosen_path"], "has_conditionals": true, "has_loops": false, "security_relevant": {"authentication": ["password"], "verification": ["check"]}, "boot_related": {}, "file_operations": [], "system_calls": []}}, "variables": {"gfxmode": {"assignments": ["$vtCurMode", "$vtoy_gfxmode", "1920x1080  # 默认分辨率", "1920x1080", "$vtCurMode"], "references": [], "type": "unknown", "reference_count": 0, "assignment_count": 5}, "vtoy_chosen_path": {"assignments": ["\"${1}\""], "references": [2010], "type": "ventoy_internal", "reference_count": 1, "assignment_count": 1}, "ventoy_new_context": {"assignments": ["1"], "references": [], "type": "boolean", "reference_count": 0, "assignment_count": 1}, "vtoy_os": {"assignments": ["Linux", "Windows", "Windows", "Unix", "Unix", "Unix", "Unix", "Unix", "Unix"], "references": [4406, 5397, 33579, 33721, 48078, 48222, 51546], "type": "ventoy_internal", "reference_count": 7, "assignment_count": 9}, "vt_unix_type": {"assignments": ["FreeBSD", "FreeBSD", "FreeBSD", "DragonFly", "FreeBSD", "NetBSD"], "references": [20941, 21046, 21163], "type": "ventoy_internal", "reference_count": 3, "assignment_count": 6}, "ventoy_compatible": {"assignments": ["YES", "YES", "YES", "YES", "YES", "YES", "YES", "YES", "NO"], "references": [20820, 21657, 23331, 40933, 41933, 42354], "type": "boolean", "reference_count": 6, "assignment_count": 9}, "vt_freebsd_ver": {"assignments": ["xx", "12.x", "${vtFuryVer}.x", "11.x", "${vtNasVer}.x", "12.x", "${vtTNasVer}.x", "11.x", "2.x", "3.x", "xx", "${vtBsdVerNum}.x", "${vtBsdVerNum}.x", "${vtBsdVerNum}.x", "9.x", "14.x"], "references": [18584, 18956, 19053, 19282, 19739, 20003], "type": "ventoy_internal", "reference_count": 6, "assignment_count": 16}, "vtFreeBsdDistro": {"assignments": ["FreeBSD", "MidnightBSD", "ClonOS", "pfSense"], "references": [19265, 19707, 19971], "type": "ventoy_internal", "reference_count": 3, "assignment_count": 4}, "vt_freebsd_bit": {"assignments": ["64", "32"], "references": [18734, 18891, 19298, 19755, 20019], "type": "ventoy_internal", "reference_count": 5, "assignment_count": 2}, "vt_unix_mod_path": {"assignments": ["${file}", "${file}"], "references": [19614, 19668, 20546], "type": "ventoy_internal", "reference_count": 3, "assignment_count": 2}, "vt_cur_wimboot_mode": {"assignments": ["1", "1"], "references": [21688, 22354, 40964, 41574, 41752], "type": "boolean", "reference_count": 5, "assignment_count": 2}, "ventoy_fs_probe": {"assignments": ["iso9660", "iso9660", "udf", "iso9660"], "references": [21738, 22667, 23393, 33464, 41014, 42404, 47967], "type": "unknown", "reference_count": 7, "assignment_count": 4}, "FirstTryBootFile": {"assignments": ["'@EFI@<EMAIL>'"], "references": [29451, 31971, 78098], "type": "unknown", "reference_count": 3, "assignment_count": 1}, "vtGrub2Mode": {"assignments": ["1", "1", "1", "1", "1"], "references": [29196, 44801], "type": "boolean", "reference_count": 2, "assignment_count": 5}, "vtback_root": {"assignments": ["$root", "$root", "$root"], "references": [31464, 46733, 78263], "type": "ventoy_internal", "reference_count": 3, "assignment_count": 3}, "vtback_theme": {"assignments": ["$theme", "$theme", "$theme"], "references": [31503, 46776, 78312], "type": "ventoy_internal", "reference_count": 3, "assignment_count": 3}, "root": {"assignments": ["(loop)", "(loop,2)", "$vtback_root", "(loop)", "(loop,2)", "$vtback_root", "$vtoy_iso_part", "$vtOldRoot", "(vtimghd,1)", "(vtimghd,1)", "(vtimghd,1)", "(vtimghd,1)", "(vtimghd,1)", "(vtimghd,1)", "(vtimghd,12)", "(vtimghd,12)", "$vtback_root"], "references": [29633, 44964, 56030, 74995, 81128, 81224, 82122], "type": "unknown", "reference_count": 7, "assignment_count": 17}, "vtback_cfg_find": {"assignments": ["0", "1", "1", "1", "1", "0", "1", "1", "1", "1"], "references": [30238, 30783, 31195, 45633, 46252], "type": "boolean", "reference_count": 5, "assignment_count": 10}, "theme": {"assignments": ["$vtback_theme", "$vtback_theme", "$vtback_theme", "$prefix/themes/ventoy/theme.txt  # 默认主题"], "references": [29699, 45038, 75068], "type": "unknown", "reference_count": 3, "assignment_count": 4}, "LoadIsoEfiDriver": {"assignments": ["on", "on"], "references": [22643, 28444, 29422, 31942, 34183, 54973, 57036, 78069, 79020], "type": "unknown", "reference_count": 9, "assignment_count": 2}, "chosen_path": {"assignments": ["\"$2\"", "\"$2\""], "references": [16918, 17022, 17126, 17230, 17473, 18107, 19831, 20232, 20686, 20789, 21014, 21123, 21429, 22245, 22304, 28414, 31705, 32651, 32694, 32735, 33433, 33541, 33693, 33782, 33884, 41628, 41698, 44411, 47218, 47625, 47668, 47940, 48044, 48194, 48285, 48389], "type": "unknown", "reference_count": 36, "assignment_count": 2}, "vt_efi_dir": {"assignments": ["YES", "YES", "NO"], "references": [24791], "type": "boolean", "reference_count": 1, "assignment_count": 3}, "vtoy_wimboot_prefix": {"assignments": ["(loop)/x86", "(loop)/x64", "(loop)", "(loop)"], "references": [34700, 34912, 35032, 35405, 35459, 35518, 35582, 35773, 36032, 36284, 36337, 36395, 36945, 37013, 37074, 37723, 38156, 38413], "type": "ventoy_internal", "reference_count": 18, "assignment_count": 4}, "vtoy_wimboot_bit": {"assignments": ["64", "32"], "references": [34478, 34950], "type": "ventoy_internal", "reference_count": 2, "assignment_count": 2}, "vtoy_wimfile_path": {"assignments": ["mem:${vtoy_wimfile_mem_addr}:size:${vtoy_wimfile_mem_size}", "$vtoy_wimboot_prefix/sources/boot.wim", "mem:${vtoy_wimfile_mem_addr}:size:${vtoy_wimfile_mem_size}", "$vtoy_wimboot_prefix/$vtoy_wim_path"], "references": [36458, 39080], "type": "path", "reference_count": 2, "assignment_count": 4}, "vtoy_wim_path": {"assignments": ["\"$1\""], "references": [37095, 37744, 38177, 38434], "type": "ventoy_internal", "reference_count": 4, "assignment_count": 1}, "vtoy_boot_sdi_legacy": {"assignments": ["\"newc:boot.sdi:$vtoy_wimboot_prefix/$2\""], "references": [37672], "type": "ventoy_internal", "reference_count": 1, "assignment_count": 1}, "vtoy_boot_sdi_efi": {"assignments": ["\"vf=boot.sdi:$vtoy_wimboot_prefix/$2\""], "references": [38997], "type": "ventoy_internal", "reference_count": 1, "assignment_count": 1}, "vtoy_boot_efi_path": {"assignments": ["\"vf=bootx64.efi:(loop)/efi/boot/boot${VTOY_EFI_ARCH}.efi\""], "references": [39032], "type": "ventoy_internal", "reference_count": 1, "assignment_count": 1}, "vt_wimkernel": {"assignments": ["wimboot.x86_64.xz", "wimboot.i386.efi.xz", "wimboot.x86_64.xz"], "references": [34981, 35173, 36141, 37212, 37465, 38731, 38887], "type": "ventoy_internal", "reference_count": 7, "assignment_count": 3}, "vt_sdi_path": {"assignments": ["0", "$vsdi"], "references": [40552, 40641], "type": "boolean", "reference_count": 2, "assignment_count": 2}, "eosimage": {"assignments": ["loop"], "references": [], "type": "unknown", "reference_count": 0, "assignment_count": 1}, "ventoy_bls_bootdev": {"assignments": ["/boot"], "references": [], "type": "unknown", "reference_count": 0, "assignment_count": 1}, "ventoy_loading_tip": {"assignments": ["\"Loading files ......\""], "references": [], "type": "unknown", "reference_count": 0, "assignment_count": 1}, "ventoy_busybox_ver": {"assignments": ["a64", "m64", "32", "64", "64", "64", "64", "64", "64", "32"], "references": [23615, 42772, 48802, 61809, 62669, 63877, 64345, 65635, 66275, 66929, 67828, 68611, 69049, 70949, 71572, 73536], "type": "unknown", "reference_count": 16, "assignment_count": 10}, "vtcompat": {"assignments": ["1"], "references": [33187, 47702], "type": "boolean", "reference_count": 2, "assignment_count": 1}, "vtMemDiskBoot": {"assignments": ["1", "1", "1", "1"], "references": [52830, 53034], "type": "boolean", "reference_count": 2, "assignment_count": 4}, "vtOldRoot": {"assignments": ["$root"], "references": [56144], "type": "ventoy_internal", "reference_count": 1, "assignment_count": 1}, "AltBootPart": {"assignments": ["0"], "references": [58983, 59283], "type": "boolean", "reference_count": 2, "assignment_count": 1}, "vtoysupport": {"assignments": ["0", "1", "1", "1"], "references": [58867], "type": "boolean", "reference_count": 1, "assignment_count": 4}, "openwrt_plugin_need": {"assignments": ["0", "1"], "references": [71044], "type": "boolean", "reference_count": 1, "assignment_count": 2}, "grubdisk": {"assignments": ["vtimghd", "vtimghd"], "references": [], "type": "unknown", "reference_count": 0, "assignment_count": 2}, "grubpartA": {"assignments": ["(vtimghd,3)", "(vtimghd,3)"], "references": [], "type": "unknown", "reference_count": 0, "assignment_count": 2}, "grubpartB": {"assignments": ["(vtimghd,5)", "(vtimghd,5)"], "references": [], "type": "unknown", "reference_count": 0, "assignment_count": 2}, "linuxpartA": {"assignments": ["(sda,3)", "(sda,3)"], "references": [], "type": "unknown", "reference_count": 0, "assignment_count": 2}, "linuxpartB": {"assignments": ["(sda,5)", "(sda,5)"], "references": [], "type": "unknown", "reference_count": 0, "assignment_count": 2}, "VENTOY_VERSION": {"assignments": ["\"2.0.01\""], "references": [80536, 80697, 80829, 80964, 81063], "type": "unknown", "reference_count": 5, "assignment_count": 1}, "VTOY_PARAM_NO_ACPI": {"assignments": ["1"], "references": [1793], "type": "boolean", "reference_count": 1, "assignment_count": 1}, "VTOY_DEFAULT_MENU_MODE": {"assignments": ["0"], "references": [83556, 86297], "type": "boolean", "reference_count": 2, "assignment_count": 1}, "VTOY_MEM_DISK_STR": {"assignments": ["\"[内存磁盘]\""], "references": [], "type": "unknown", "reference_count": 0, "assignment_count": 1}, "VTOY_ISO_RAW_STR": {"assignments": ["\"兼容模式\""], "references": [], "type": "unknown", "reference_count": 0, "assignment_count": 1}, "VTOY_GRUB2_MODE_STR": {"assignments": ["\"GRUB2模式\""], "references": [], "type": "unknown", "reference_count": 0, "assignment_count": 1}, "VTOY_WIMBOOT_MODE_STR": {"assignments": ["\"WIMBOOT模式\""], "references": [], "type": "unknown", "reference_count": 0, "assignment_count": 1}, "VTOY_ISO_UEFI_DRV_STR": {"assignments": ["\"UEFI文件系统\""], "references": [], "type": "unknown", "reference_count": 0, "assignment_count": 1}, "VTOY_F2_CMD": {"assignments": ["\"vt_browser_disk\"       # F2键：浏览磁盘"], "references": [], "type": "unknown", "reference_count": 0, "assignment_count": 1}, "VTOY_F4_CMD": {"assignments": ["\"ventoy_localboot\"      # F4键：本地启动"], "references": [], "type": "unknown", "reference_count": 0, "assignment_count": 1}, "VTOY_F5_CMD": {"assignments": ["\"ventoy_diagnosis\"      # F5键：Ventoy诊断"], "references": [], "type": "unknown", "reference_count": 0, "assignment_count": 1}, "VTOY_F6_CMD": {"assignments": ["\"ventoy_ext_menu\"       # F6键：扩展菜单"], "references": [], "type": "unknown", "reference_count": 0, "assignment_count": 1}, "VTOY_HELP_CMD": {"assignments": ["\"ventoy_show_help\"    # 帮助命令"], "references": [], "type": "unknown", "reference_count": 0, "assignment_count": 1}, "VTOY_CHKSUM_CMD": {"assignments": ["\"ventoy_checksum\"   # 校验和命令"], "references": [], "type": "unknown", "reference_count": 0, "assignment_count": 1}, "VTOY_HELP_TXT_LANGUAGE": {"assignments": ["\"en_US\"      # 帮助文本语言"], "references": [], "type": "unknown", "reference_count": 0, "assignment_count": 1}, "VTOY_CHKSUM_FILE_PATH": {"assignments": ["\"X\"           # 校验和文件路径"], "references": [3024], "type": "unknown", "reference_count": 1, "assignment_count": 1}, "VTOY_LANG_CMD": {"assignments": ["\"ventoy_language\"     # 语言设置命令"], "references": [], "type": "unknown", "reference_count": 0, "assignment_count": 1}, "VTOY_TEXT_MENU_VER": {"assignments": ["\"版本：$VENTOY_VERSION 平台：BIOS\"", "\"版本：$VENTOY_VERSION 平台：IA32\"", "\"版本：$VENTOY_VERSION 平台：AA64\"", "\"版本：$VENTOY_VERSION 平台：MIPS\"", "\"版本：$VENTOY_VERSION 平台：UEFI\""], "references": [], "type": "unknown", "reference_count": 0, "assignment_count": 5}, "VTOY_EFI_ARCH": {"assignments": ["ia32", "aa64", "mips", "x64"], "references": [22591, 29280, 29362, 31346, 31890, 34124, 37836, 38538, 38638, 49280, 49360, 54921, 56247, 56977, 60183, 78008, 78961], "type": "unknown", "reference_count": 17, "assignment_count": 4}, "vtoy_path": {"assignments": ["($root)", "(ventoydisk)/ventoy", "($root)/ventoy"], "references": [20564, 22571, 23583, 29342, 31870, 34104, 35162, 36130, 37201, 37454, 38720, 38876, 39947, 40447, 41990, 42116, 42741, 46944, 47355, 48490, 48770, 54729, 54901, 56227, 56725, 56957, 59820, 60163, 61751, 62611, 63819, 64287, 65577, 66217, 66871, 67770, 68553, 68991, 70891, 71514, 72057, 72809, 73478, 74163, 77816, 77988, 78941], "type": "ventoy_internal", "reference_count": 47, "assignment_count": 3}, "vtoy_iso_part": {"assignments": ["(hd$vtid,1)   # ISO文件所在分区", "($vtoy_dev,2)   # ISO文件所在分区"], "references": [2168, 3008, 29760, 32914, 45111, 50299, 51106, 51345, 51730, 51928, 52126, 52885, 52964, 53087, 53165, 53580, 53647, 54367, 54435, 55626, 55883, 56049, 57667, 60936, 61193, 61783, 61850, 62643, 62710, 63851, 63918, 64319, 64386, 65609, 65676, 66249, 66316, 66903, 66970, 67802, 67869, 68585, 68652, 69023, 69090, 69979, 70442, 70923, 70990, 71095, 71185, 71546, 71613, 72089, 72139, 72841, 72891, 73510, 73577, 74760, 74853, 74907, 77662, 78779, 78850, 86239], "type": "ventoy_internal", "reference_count": 66, "assignment_count": 2}, "vtoy_efi_part": {"assignments": ["(hd$vtid,2)   # EFI系统分区", "($vtoy_dev,2)   # EFI系统分区"], "references": [20879, 64453, 81586, 81645, 81800, 81869, 82452, 82564, 82635, 82713, 83023, 83092, 83143, 83212, 83292, 83361, 83412, 83481], "type": "ventoy_internal", "reference_count": 18, "assignment_count": 2}, "vtoydev": {"assignments": ["hd$vtid", "$vtoy_dev"], "references": [82498], "type": "ventoy_internal", "reference_count": 1, "assignment_count": 2}, "vt_plugin_path": {"assignments": ["$vtoy_efi_part", "$prefix", "$vtoy_efi_part"], "references": [81951], "type": "ventoy_internal", "reference_count": 1, "assignment_count": 3}, "timeout": {"assignments": ["$VTOY_MENU_TIMEOUT", "0", "$VTOY_MENU_TIMEOUT"], "references": [], "type": "boolean", "reference_count": 0, "assignment_count": 3}, "VTOY_F3_CMD": {"assignments": ["\"vt_dynamic_menu 1 1\"  # 当前是列表模式，F3切换到树状", "\"vt_dynamic_menu 1 0\"  # 当前是树状模式，F3切换到列表"], "references": [], "type": "unknown", "reference_count": 0, "assignment_count": 2}, "gfxpayload": {"assignments": ["keep  # 保持图形模式", "keep"], "references": [], "type": "unknown", "reference_count": 0, "assignment_count": 2}, "mouse_delta": {"assignments": ["4000  # 鼠标灵敏度"], "references": [], "type": "unknown", "reference_count": 0, "assignment_count": 1}, "ventoy_img_count": {"assignments": ["0"], "references": [], "type": "boolean", "reference_count": 0, "assignment_count": 1}, "default": {"assignments": ["\"$vtDefault\"  # 设置默认启动项"], "references": [], "type": "unknown", "reference_count": 0, "assignment_count": 1}, "grub_platform": {"assignments": [], "references": [871, 11883, 12718, 12964, 20067, 35113, 37152, 39276, 40373, 52287, 52782, 53522, 54676, 56641, 58912, 59708, 71963, 72715, 74647, 77518, 77764, 78721, 80461, 85051], "type": "grub_builtin", "reference_count": 24, "assignment_count": 0}, "vtdebug_flag": {"assignments": [], "references": [1074, 5351, 5974, 6157, 19218, 21297, 22686, 29471, 31991, 34203, 34854, 36793, 42010, 42136, 46964, 47375, 54749, 54993, 56316, 57056, 59840, 60244, 77836, 78118, 79040], "type": "ventoy_internal", "reference_count": 25, "assignment_count": 0}, "vtCurMode": {"assignments": [], "references": [1314, 84729], "type": "ventoy_internal", "reference_count": 2, "assignment_count": 0}, "vtoy_display_mode": {"assignments": [], "references": [1399, 1472, 1596, 1669, 83960, 84045, 84241], "type": "ventoy_internal", "reference_count": 7, "assignment_count": 0}, "1": {"assignments": [], "references": [1852, 1928, 1978, 5820, 6135, 16913, 17017, 17121, 17225, 17468, 19827, 20228, 20682, 20785, 21009, 21118, 21425, 21878, 22300, 22912, 22999, 23535, 23602, 27229, 28410, 31701, 32731, 33429, 33534, 33688, 33777, 33879, 34037, 36771, 36846, 41142, 41694, 42534, 42599, 42701, 42759, 44407, 47214, 47936, 48037, 48189, 48280, 48384, 48578, 48712, 48741, 48788, 48844, 56545, 58605, 59565, 59598, 64261, 74239], "type": "external", "reference_count": 59, "assignment_count": 0}, "2": {"assignments": [], "references": [1857, 15584, 21880, 22916, 23003, 23537, 23597, 27234, 32619, 34041, 36849, 36878, 36966, 37034, 41144, 42536, 42601, 42703, 42754, 47588, 48582, 48716, 48745, 48783, 48849, 74243], "type": "external", "reference_count": 26, "assignment_count": 0}, "vt_vcfg": {"assignments": [], "references": [2184], "type": "ventoy_internal", "reference_count": 1, "assignment_count": 0}, "prefix": {"assignments": [], "references": [2284, 2378, 2442, 2555, 2692, 3077, 3148, 3265, 3756, 73760, 81920, 82009, 82322, 84951], "type": "external", "reference_count": 14, "assignment_count": 0}, "VTLANG_ENTER_EXIT": {"assignments": [], "references": [2886, 53881, 61656, 70294, 74071], "type": "external", "reference_count": 5, "assignment_count": 0}, "vtoy_help_txt_mem_addr": {"assignments": [], "references": [3193, 3350], "type": "ventoy_internal", "reference_count": 2, "assignment_count": 0}, "vtoy_help_txt_mem_size": {"assignments": [], "references": [3381], "type": "ventoy_internal", "reference_count": 1, "assignment_count": 0}, "vtCurLang": {"assignments": [], "references": [3480, 3544], "type": "ventoy_internal", "reference_count": 2, "assignment_count": 0}, "vtoy_menu_lang_mem_addr": {"assignments": [], "references": [3826], "type": "ventoy_internal", "reference_count": 1, "assignment_count": 0}, "vtoy_menu_lang_mem_size": {"assignments": [], "references": [3858], "type": "ventoy_internal", "reference_count": 1, "assignment_count": 0}, "vt_volume_id": {"assignments": [], "references": [3978, 5738, 7194, 9587, 12517, 16380, 16461, 16541, 16848, 16954, 17058, 17162, 17313, 28276, 28878, 29033, 30384, 32203, 32854, 44632, 45807, 49723, 49819, 49930, 50032, 51679, 51866, 51984], "type": "ventoy_internal", "reference_count": 28, "assignment_count": 0}, "file": {"assignments": [], "references": [4270, 18406, 18470, 19481, 19550, 20407, 20476, 22005, 24001, 24096, 41303], "type": "external", "reference_count": 11, "assignment_count": 0}, "vt_system_id": {"assignments": [], "references": [4456, 4825], "type": "ventoy_internal", "reference_count": 2, "assignment_count": 0}, "vt_chosen_size": {"assignments": [], "references": [5551, 5583, 9703, 28466, 49535, 50406, 50453, 50581, 50671, 50755, 51556, 52581, 67613], "type": "ventoy_internal", "reference_count": 13, "assignment_count": 0}, "vwfile": {"assignments": [], "references": [7354, 7419], "type": "external", "reference_count": 2, "assignment_count": 0}, "vtFuryVer": {"assignments": [], "references": [15621], "type": "ventoy_internal", "reference_count": 1, "assignment_count": 0}, "vt_freenas_ver": {"assignments": [], "references": [15884], "type": "ventoy_internal", "reference_count": 1, "assignment_count": 0}, "vtNasVer": {"assignments": [], "references": [15938], "type": "ventoy_internal", "reference_count": 1, "assignment_count": 0}, "vt_truenas_ver": {"assignments": [], "references": [16220], "type": "ventoy_internal", "reference_count": 1, "assignment_count": 0}, "vtTNasVer": {"assignments": [], "references": [16274], "type": "ventoy_internal", "reference_count": 1, "assignment_count": 0}, "vtBsdVerNum": {"assignments": [], "references": [17361, 17788, 18051], "type": "ventoy_internal", "reference_count": 3, "assignment_count": 0}, "vt_userland_ver": {"assignments": [], "references": [17733], "type": "ventoy_internal", "reference_count": 1, "assignment_count": 0}, "vt_freebsd_line1": {"assignments": [], "references": [17995], "type": "ventoy_internal", "reference_count": 1, "assignment_count": 0}, "vt_chosen_name": {"assignments": [], "references": [21582, 28798, 32495, 33289, 40854, 44552, 47804, 51473, 52213, 52647, 74706], "type": "ventoy_internal", "reference_count": 11, "assignment_count": 0}, "vt_wim_cnt": {"assignments": [], "references": [22100, 41440], "type": "ventoy_internal", "reference_count": 2, "assignment_count": 0}, "vtoy_chain_mem_addr": {"assignments": [], "references": [22437, 22493, 22706, 28627, 28683, 29491, 31739, 31795, 32011, 41835, 41891, 42030, 42161, 44468, 44853, 46894, 46985, 47252, 47309, 47396, 54632, 54770, 55013, 59658, 59769, 59879, 60054, 60264, 77723, 77868, 78138], "type": "ventoy_internal", "reference_count": 31, "assignment_count": 0}, "env_param": {"assignments": [], "references": [22623, 29402, 31922, 34163, 54953, 56286, 57016, 78049, 79000], "type": "external", "reference_count": 9, "assignment_count": 0}, "vtoy_chain_mem_size": {"assignments": [], "references": [22734, 29519, 32039, 42058, 42189, 47013, 47424, 54798, 55041, 59907, 60292, 77896, 78166], "type": "ventoy_internal", "reference_count": 13, "assignment_count": 0}, "vt_efivdisk_offset": {"assignments": [], "references": [22967], "type": "ventoy_internal", "reference_count": 1, "assignment_count": 0}, "vt_rp_initrd": {"assignments": [], "references": [23130, 23189], "type": "ventoy_internal", "reference_count": 2, "assignment_count": 0}, "3": {"assignments": [], "references": [23185, 36852], "type": "external", "reference_count": 2, "assignment_count": 0}, "vtcount": {"assignments": [], "references": [24540, 44119], "type": "ventoy_internal", "reference_count": 2, "assignment_count": 0}, "vtindex": {"assignments": [], "references": [25261, 25416, 25574, 25791, 25951, 26190, 26425, 26598, 26770, 26907, 27040, 27238, 27981], "type": "ventoy_internal", "reference_count": 13, "assignment_count": 0}, "vt_replace_cnt": {"assignments": [], "references": [27157], "type": "ventoy_internal", "reference_count": 1, "assignment_count": 0}, "vt_chosen_path": {"assignments": [], "references": [29777, 32930, 45128, 49601, 50315, 50920, 50993, 51122, 51361, 51528, 51746, 51944, 52142, 52901, 52980, 53104, 53182, 53379, 53459, 53596, 53664, 54200, 54277, 54383, 54451, 55393, 55470, 55576, 55642, 55899, 57434, 57511, 57617, 57683, 57924, 60699, 60780, 60886, 60952, 61209, 61764, 61868, 62624, 62728, 63832, 63936, 64300, 64404, 65590, 65694, 66230, 66334, 66884, 66988, 67783, 67887, 68566, 68670, 69004, 69108, 70458, 70904, 71008, 71527, 71631, 72070, 72157, 72822, 72909, 73491, 73595, 74508, 74581, 74776, 74869, 74923, 77678, 78578, 78658, 78795, 78864], "type": "ventoy_internal", "reference_count": 81, "assignment_count": 0}, "cfg": {"assignments": [], "references": [30089, 30169, 45460, 45548], "type": "external", "reference_count": 4, "assignment_count": 0}, "vt_sys_menu_mem_addr": {"assignments": [], "references": [30681, 46134, 63640], "type": "ventoy_internal", "reference_count": 3, "assignment_count": 0}, "vt_sys_menu_mem_size": {"assignments": [], "references": [30710, 46163, 63669], "type": "ventoy_internal", "reference_count": 3, "assignment_count": 0}, "vtisouefi": {"assignments": [], "references": [32393], "type": "ventoy_internal", "reference_count": 1, "assignment_count": 0}, "vtoy_iso_buf_addr": {"assignments": [], "references": [34223, 56336], "type": "ventoy_internal", "reference_count": 2, "assignment_count": 0}, "vtoy_iso_buf_size": {"assignments": [], "references": [34249, 56362], "type": "ventoy_internal", "reference_count": 2, "assignment_count": 0}, "vtoy_wimboot_mem_addr": {"assignments": [], "references": [35317, 36197, 37366, 38789], "type": "ventoy_internal", "reference_count": 4, "assignment_count": 0}, "vtoy_wimboot_mem_size": {"assignments": [], "references": [35347, 36227, 37396, 38819], "type": "ventoy_internal", "reference_count": 4, "assignment_count": 0}, "vtoy_wimfile_mem_addr": {"assignments": [], "references": [35930, 38311], "type": "ventoy_internal", "reference_count": 2, "assignment_count": 0}, "vtoy_wimfile_mem_size": {"assignments": [], "references": [35960, 38341], "type": "ventoy_internal", "reference_count": 2, "assignment_count": 0}, "vtoy_chain_file_size": {"assignments": [], "references": [36499, 39121], "type": "ventoy_internal", "reference_count": 2, "assignment_count": 0}, "vtoy_chain_file_read": {"assignments": [], "references": [36543, 39165], "type": "ventoy_internal", "reference_count": 2, "assignment_count": 0}, "vtoy_init_exe": {"assignments": [], "references": [37347, 38770], "type": "ventoy_internal", "reference_count": 2, "assignment_count": 0}, "vtoy_pe_bootmgr_mem_addr": {"assignments": [], "references": [37515], "type": "ventoy_internal", "reference_count": 1, "assignment_count": 0}, "vtoy_pe_bootmgr_mem_size": {"assignments": [], "references": [37548], "type": "ventoy_internal", "reference_count": 1, "assignment_count": 0}, "vtoy_pe_bcd_mem_addr": {"assignments": [], "references": [37604, 38928], "type": "ventoy_internal", "reference_count": 2, "assignment_count": 0}, "vtoy_pe_bcd_mem_size": {"assignments": [], "references": [37633, 38957], "type": "ventoy_internal", "reference_count": 2, "assignment_count": 0}, "vtoy_wim_bit": {"assignments": [], "references": [37864], "type": "ventoy_internal", "reference_count": 1, "assignment_count": 0}, "grub_cpu": {"assignments": [], "references": [39381, 80603, 80734, 80866], "type": "grub_builtin", "reference_count": 4, "assignment_count": 0}, "vtoy_pe_wim_path": {"assignments": [], "references": [39743, 40532, 40621], "type": "ventoy_internal", "reference_count": 3, "assignment_count": 0}, "vsdi": {"assignments": [], "references": [40223, 40274], "type": "external", "reference_count": 2, "assignment_count": 0}, "dir": {"assignments": [], "references": [43077, 43150], "type": "external", "reference_count": 2, "assignment_count": 0}, "vt_volume_space": {"assignments": [], "references": [50385, 50564], "type": "ventoy_internal", "reference_count": 2, "assignment_count": 0}, "vt_chosen_size_mod": {"assignments": [], "references": [50506, 50597], "type": "ventoy_internal", "reference_count": 2, "assignment_count": 0}, "VTLANG_ENTER_CONTINUE": {"assignments": [], "references": [50815, 58291], "type": "external", "reference_count": 2, "assignment_count": 0}, "VTOY_SECOND_EXIT": {"assignments": [], "references": [51586], "type": "external", "reference_count": 1, "assignment_count": 0}, "vt_vlnk_dst": {"assignments": [], "references": [55690, 56081, 56178, 57731, 58414, 61000, 61266], "type": "ventoy_internal", "reference_count": 7, "assignment_count": 0}, "vtoy_vhd_buf_addr": {"assignments": [], "references": [56599, 56782, 57076], "type": "ventoy_internal", "reference_count": 3, "assignment_count": 0}, "vtoy_vhd_buf_size": {"assignments": [], "references": [56808, 57102], "type": "ventoy_internal", "reference_count": 2, "assignment_count": 0}, "VTOY_VHD_NO_WARNING": {"assignments": [], "references": [57957], "type": "external", "reference_count": 1, "assignment_count": 0}, "vtoy_iso_fs": {"assignments": [], "references": [58012, 58127], "type": "ventoy_internal", "reference_count": 2, "assignment_count": 0}, "vtoytype": {"assignments": [], "references": [58666, 58728, 58793, 60521], "type": "ventoy_internal", "reference_count": 4, "assignment_count": 0}, "parttype": {"assignments": [], "references": [58961, 59261], "type": "external", "reference_count": 2, "assignment_count": 0}, "ventoy_env_param": {"assignments": [], "references": [60224], "type": "external", "reference_count": 1, "assignment_count": 0}, "vt_module_ver": {"assignments": [], "references": [62023, 62178, 62272, 63148, 63303, 63397, 64820, 64992, 65123, 65800, 65892, 67094, 67186, 68098, 68193], "type": "ventoy_internal", "reference_count": 15, "assignment_count": 0}, "mod": {"assignments": [], "references": [62193, 62287, 63318, 63412, 65007, 65138], "type": "external", "reference_count": 6, "assignment_count": 0}, "vt_dir_name": {"assignments": [], "references": [63032, 69607, 69732, 69795, 69887], "type": "ventoy_internal", "reference_count": 5, "assignment_count": 0}, "elec_ver": {"assignments": [], "references": [64536, 64573, 65313], "type": "external", "reference_count": 3, "assignment_count": 0}, "vt_paldo_entry_conf": {"assignments": [], "references": [66579], "type": "ventoy_internal", "reference_count": 1, "assignment_count": 0}, "vtPaldoVer": {"assignments": [], "references": [66669, 66750], "type": "ventoy_internal", "reference_count": 2, "assignment_count": 0}, "vtoy_img_max_part_end": {"assignments": [], "references": [67586], "type": "ventoy_internal", "reference_count": 1, "assignment_count": 0}, "vtImgHd1Label": {"assignments": [], "references": [75181, 75374, 76372, 76459, 76554, 76725, 76809, 76909, 76986], "type": "ventoy_internal", "reference_count": 9, "assignment_count": 0}, "vtImgHd3Label": {"assignments": [], "references": [75741, 76289], "type": "ventoy_internal", "reference_count": 2, "assignment_count": 0}, "vt_release_line1": {"assignments": [], "references": [75930], "type": "ventoy_internal", "reference_count": 1, "assignment_count": 0}, "vtImgHostname": {"assignments": [], "references": [76637], "type": "ventoy_internal", "reference_count": 1, "assignment_count": 0}, "vtImgHd2Label": {"assignments": [], "references": [76876], "type": "ventoy_internal", "reference_count": 1, "assignment_count": 0}, "vtoy_img_buf_addr": {"assignments": [], "references": [79060], "type": "ventoy_internal", "reference_count": 1, "assignment_count": 0}, "vtoy_img_buf_size": {"assignments": [], "references": [79086], "type": "ventoy_internal", "reference_count": 1, "assignment_count": 0}, "vtoy_dev": {"assignments": [], "references": [81177, 82160, 82193, 82243], "type": "ventoy_internal", "reference_count": 4, "assignment_count": 0}, "vtid": {"assignments": [], "references": [81300, 81369, 81425, 81472], "type": "ventoy_internal", "reference_count": 4, "assignment_count": 0}, "vtoy_font_mem_addr": {"assignments": [], "references": [81718, 82379], "type": "ventoy_internal", "reference_count": 2, "assignment_count": 0}, "vtoy_font_mem_size": {"assignments": [], "references": [81745, 82406], "type": "ventoy_internal", "reference_count": 2, "assignment_count": 0}, "VTOY_MENU_LANGUAGE": {"assignments": [], "references": [82751, 82803], "type": "external", "reference_count": 2, "assignment_count": 0}, "VTOY_MENU_TIMEOUT": {"assignments": [], "references": [82891, 82935, 86668, 86759], "type": "external", "reference_count": 4, "assignment_count": 0}, "vtoy_gfxmode": {"assignments": [], "references": [83785, 83824, 84487], "type": "ventoy_internal", "reference_count": 3, "assignment_count": 0}, "vtoy_serial_param": {"assignments": [], "references": [84108, 84151, 84316, 84359], "type": "ventoy_internal", "reference_count": 4, "assignment_count": 0}, "vtoy_res_fit": {"assignments": [], "references": [84784], "type": "ventoy_internal", "reference_count": 1, "assignment_count": 0}, "vtoy_theme": {"assignments": [], "references": [84882], "type": "ventoy_internal", "reference_count": 1, "assignment_count": 0}, "VTOY_DEFAULT_KBD_LAYOUT": {"assignments": [], "references": [85197, 85256], "type": "external", "reference_count": 2, "assignment_count": 0}, "VTOY_PLUGIN_PATH_CASE_MISMATCH": {"assignments": [], "references": [85332, 85393], "type": "external", "reference_count": 2, "assignment_count": 0}, "VTOY_DEFAULT_IMAGE": {"assignments": [], "references": [86434, 86558], "type": "external", "reference_count": 2, "assignment_count": 0}, "vtDefault": {"assignments": [], "references": [86611], "type": "ventoy_internal", "reference_count": 1, "assignment_count": 0}, "vtHotkey": {"assignments": [], "references": [86917, 87013, 87084, 87155], "type": "ventoy_internal", "reference_count": 4, "assignment_count": 0}}, "conditionals": [{"type": "if", "condition": "[ \"$grub_platform\" = \"pc\" ]", "complexity": {"has_and": false, "has_or": false, "has_negation": false, "has_file_test": false, "has_string_test": true, "has_numeric_test": false}, "variables_used": ["grub_platform"], "operators": ["="]}, {"type": "if", "condition": "[ -n \"${vtdebug_flag}\" ]", "complexity": {"has_and": false, "has_or": false, "has_negation": false, "has_file_test": false, "has_string_test": false, "has_numeric_test": true}, "variables_used": ["vtdebug_flag"], "operators": ["-n", "-n"]}, {"type": "if", "condition": "[ -z \"$vtoy_display_mode\" ]", "complexity": {"has_and": false, "has_or": false, "has_negation": false, "has_file_test": false, "has_string_test": false, "has_numeric_test": false}, "variables_used": ["vtoy_display_mode"], "operators": ["-z"]}, {"type": "if", "condition": "[ \"$vtoy_display_mode\" = \"GUI\" ]", "complexity": {"has_and": false, "has_or": false, "has_negation": false, "has_file_test": false, "has_string_test": true, "has_numeric_test": false}, "variables_used": ["vtoy_display_mode"], "operators": ["="]}, {"type": "if", "condition": "[ -z \"$vtoy_display_mode\" ]", "complexity": {"has_and": false, "has_or": false, "has_negation": false, "has_file_test": false, "has_string_test": false, "has_numeric_test": false}, "variables_used": ["vtoy_display_mode"], "operators": ["-z"]}, {"type": "if", "condition": "[ \"$vtoy_display_mode\" = \"GUI\" ]", "complexity": {"has_and": false, "has_or": false, "has_negation": false, "has_file_test": false, "has_string_test": true, "has_numeric_test": false}, "variables_used": ["vtoy_display_mode"], "operators": ["="]}, {"type": "if", "condition": "[ \"$VTOY_PARAM_NO_ACPI\" != \"1\" ]", "complexity": {"has_and": false, "has_or": false, "has_negation": true, "has_file_test": false, "has_string_test": true, "has_numeric_test": false}, "variables_used": ["VTOY_PARAM_NO_ACPI"], "operators": ["!=", "!"]}, {"type": "if", "condition": "vt_check_custom_boot \"${1}\" vt_vcfg", "complexity": {"has_and": false, "has_or": false, "has_negation": false, "has_file_test": false, "has_string_test": false, "has_numeric_test": false}, "variables_used": [], "operators": []}, {"type": "if", "condition": "[ -e $prefix/ventoy_grub.cfg ]", "complexity": {"has_and": false, "has_or": false, "has_negation": false, "has_file_test": true, "has_string_test": false, "has_numeric_test": true}, "variables_used": ["prefix"], "operators": ["-e", "-e"]}, {"type": "if", "condition": "[ -f \"${vtoy_iso_part}${VTOY_CHKSUM_FILE_PATH}\" ]", "complexity": {"has_and": false, "has_or": false, "has_negation": false, "has_file_test": true, "has_string_test": false, "has_numeric_test": false}, "variables_used": ["VTOY_CHKSUM_FILE_PATH", "vtoy_iso_part"], "operators": ["-f"]}, {"type": "if", "condition": "[ -f $prefix/help.tar.gz ]", "complexity": {"has_and": false, "has_or": false, "has_negation": false, "has_file_test": true, "has_string_test": false, "has_numeric_test": false}, "variables_used": ["prefix"], "operators": ["-f"]}, {"type": "if", "condition": "[ -z \"$vtoy_help_txt_mem_addr\" ]", "complexity": {"has_and": false, "has_or": false, "has_negation": false, "has_file_test": false, "has_string_test": false, "has_numeric_test": false}, "variables_used": ["vtoy_help_txt_mem_addr"], "operators": ["-z"]}, {"type": "if", "condition": "[ -f \"(vt_help_tarfs)/help/${vtCurLang}.txt\" ]", "complexity": {"has_and": false, "has_or": false, "has_negation": false, "has_file_test": true, "has_string_test": false, "has_numeric_test": false}, "variables_used": ["vtCurLang"], "operators": ["-f"]}, {"type": "if", "condition": "vt_str_begin \"$vt_volume_id\" \"DLC Boot\"", "complexity": {"has_and": false, "has_or": false, "has_negation": false, "has_file_test": false, "has_string_test": false, "has_numeric_test": false}, "variables_used": ["vt_volume_id"], "operators": []}, {"type": "if", "condition": "[ -f (loop)/DLCBoot.exe ]", "complexity": {"has_and": false, "has_or": false, "has_negation": false, "has_file_test": true, "has_string_test": false, "has_numeric_test": false}, "variables_used": [], "operators": ["-f"]}, {"type": "if", "condition": "vt_file_exist_nocase (loop)/$file", "complexity": {"has_and": false, "has_or": false, "has_negation": false, "has_file_test": false, "has_string_test": false, "has_numeric_test": false}, "variables_used": ["file"], "operators": []}, {"type": "if", "condition": "[ \"$vtoy_os\" = \"Linux\" ]", "complexity": {"has_and": false, "has_or": false, "has_negation": false, "has_file_test": false, "has_string_test": true, "has_numeric_test": false}, "variables_used": ["vtoy_os"], "operators": ["="]}, {"type": "if", "condition": "vt_strstr \"$vt_system_id\" \"FreeBSD\"", "complexity": {"has_and": false, "has_or": false, "has_negation": false, "has_file_test": false, "has_string_test": false, "has_numeric_test": false}, "variables_used": ["vt_system_id"], "operators": []}, {"type": "if", "condition": "[ -e (loop)/bin/freebsd-version ]", "complexity": {"has_and": false, "has_or": false, "has_negation": false, "has_file_test": true, "has_string_test": false, "has_numeric_test": true}, "variables_used": [], "operators": ["-e", "-e"]}, {"type": "if", "condition": "[ -e (loop)/boot/kernel/geom_ventoy.ko ]", "complexity": {"has_and": false, "has_or": false, "has_negation": false, "has_file_test": true, "has_string_test": false, "has_numeric_test": true}, "variables_used": [], "operators": ["-e", "-e"]}, {"type": "if", "condition": "vt_str_begin \"$vt_system_id\" \"DragonFly\"", "complexity": {"has_and": false, "has_or": false, "has_negation": false, "has_file_test": false, "has_string_test": false, "has_numeric_test": false}, "variables_used": ["vt_system_id"], "operators": []}, {"type": "if", "condition": "[ -e (loop)/boot/kernel/kernel ]", "complexity": {"has_and": false, "has_or": false, "has_negation": false, "has_file_test": true, "has_string_test": false, "has_numeric_test": true}, "variables_used": [], "operators": ["-e", "-e"]}, {"type": "if", "condition": "file --is-x86-kfreebsd (loop)/boot/kernel/kernel", "complexity": {"has_and": false, "has_or": false, "has_negation": false, "has_file_test": true, "has_string_test": false, "has_numeric_test": false}, "variables_used": [], "operators": ["-x"]}, {"type": "if", "condition": "file --is-x86-knetbsd (loop)/boot/kernel/kernel", "complexity": {"has_and": false, "has_or": false, "has_negation": false, "has_file_test": true, "has_string_test": false, "has_numeric_test": false}, "variables_used": [], "operators": ["-x"]}, {"type": "if", "condition": "[ -n \"${vtdebug_flag}\" ]", "complexity": {"has_and": false, "has_or": false, "has_negation": false, "has_file_test": false, "has_string_test": false, "has_numeric_test": true}, "variables_used": ["vtdebug_flag"], "operators": ["-n", "-n"]}, {"type": "if", "condition": "[ $vt_chosen_size -GT 33554432 -a $vt_chosen_size -LE 83886080 ]", "complexity": {"has_and": true, "has_or": false, "has_negation": false, "has_file_test": false, "has_string_test": false, "has_numeric_test": false}, "variables_used": ["vt_chosen_size"], "operators": []}, {"type": "if", "condition": "vt_str_begin \"$vt_volume_id\" \"embootkit\"", "complexity": {"has_and": false, "has_or": false, "has_negation": false, "has_file_test": false, "has_string_test": false, "has_numeric_test": false}, "variables_used": ["vt_volume_id"], "operators": []}, {"type": "if", "condition": "[ -e \"$1/casper/tinycore.gz\" ]", "complexity": {"has_and": false, "has_or": false, "has_negation": false, "has_file_test": true, "has_string_test": false, "has_numeric_test": true}, "variables_used": [], "operators": ["-e", "-e"]}, {"type": "if", "condition": "[ -n \"${vtdebug_flag}\" ]", "complexity": {"has_and": false, "has_or": false, "has_negation": false, "has_file_test": false, "has_string_test": false, "has_numeric_test": true}, "variables_used": ["vtdebug_flag"], "operators": ["-n", "-n"]}, {"type": "if", "condition": "[ -n \"${vtdebug_flag}\" ]", "complexity": {"has_and": false, "has_or": false, "has_negation": false, "has_file_test": false, "has_string_test": false, "has_numeric_test": true}, "variables_used": ["vtdebug_flag"], "operators": ["-n", "-n"]}, {"type": "if", "condition": "[ -d (loop)/h3pe ]", "complexity": {"has_and": false, "has_or": false, "has_negation": false, "has_file_test": true, "has_string_test": false, "has_numeric_test": false}, "variables_used": [], "operators": ["-d"]}, {"type": "if", "condition": "[ -d (loop)/2k10/winpe ]", "complexity": {"has_and": false, "has_or": false, "has_negation": false, "has_file_test": true, "has_string_test": false, "has_numeric_test": false}, "variables_used": [], "operators": ["-d"]}, {"type": "if", "condition": "[ -f (loop)/boot/boot.wim ]", "complexity": {"has_and": false, "has_or": false, "has_negation": false, "has_file_test": true, "has_string_test": false, "has_numeric_test": false}, "variables_used": [], "operators": ["-f"]}, {"type": "if", "condition": "[ -f (loop)/sources/boot.wim ]", "complexity": {"has_and": false, "has_or": false, "has_negation": false, "has_file_test": true, "has_string_test": false, "has_numeric_test": false}, "variables_used": [], "operators": ["-f"]}, {"type": "if", "condition": "vt_str_begin \"$vt_volume_id\" \"DLC Boot\"", "complexity": {"has_and": false, "has_or": false, "has_negation": false, "has_file_test": false, "has_string_test": false, "has_numeric_test": false}, "variables_used": ["vt_volume_id"], "operators": []}, {"type": "if", "condition": "[ -f (loop)/$vwfile ]", "complexity": {"has_and": false, "has_or": false, "has_negation": false, "has_file_test": true, "has_string_test": false, "has_numeric_test": false}, "variables_used": ["vwfile"], "operators": ["-f"]}, {"type": "if", "condition": "[ -e (loop)/boot/all.rdz ]", "complexity": {"has_and": false, "has_or": false, "has_negation": false, "has_file_test": true, "has_string_test": false, "has_numeric_test": true}, "variables_used": [], "operators": ["-e", "-e"]}, {"type": "if", "condition": "[ -e (loop)/boot/xen.gz ]", "complexity": {"has_and": false, "has_or": false, "has_negation": false, "has_file_test": true, "has_string_test": false, "has_numeric_test": true}, "variables_used": [], "operators": ["-e", "-e"]}, {"type": "if", "condition": "[ -e (loop)/install.img ]", "complexity": {"has_and": false, "has_or": false, "has_negation": false, "has_file_test": true, "has_string_test": false, "has_numeric_test": true}, "variables_used": [], "operators": ["-e", "-e"]}, {"type": "if", "condition": "[ -d (loop)/casper ]", "complexity": {"has_and": false, "has_or": false, "has_negation": false, "has_file_test": true, "has_string_test": false, "has_numeric_test": false}, "variables_used": [], "operators": ["-d"]}, {"type": "if", "condition": "[ -e (loop)/casper/initrd ]", "complexity": {"has_and": false, "has_or": false, "has_negation": false, "has_file_test": true, "has_string_test": false, "has_numeric_test": true}, "variables_used": [], "operators": ["-e", "-e"]}, {"type": "if", "condition": "[ -e (loop)/casper/initrd.gz ]", "complexity": {"has_and": false, "has_or": false, "has_negation": false, "has_file_test": true, "has_string_test": false, "has_numeric_test": true}, "variables_used": [], "operators": ["-e", "-e"]}, {"type": "if", "condition": "[ -e (loop)/casper/initrd-oem ]", "complexity": {"has_and": false, "has_or": true, "has_negation": false, "has_file_test": true, "has_string_test": false, "has_numeric_test": true}, "variables_used": [], "operators": ["-e", "-e"]}, {"type": "if", "condition": "[ -e (loop)/boot/grub/initrd.xz ]", "complexity": {"has_and": false, "has_or": false, "has_negation": false, "has_file_test": true, "has_string_test": false, "has_numeric_test": true}, "variables_used": [], "operators": ["-e", "-e"]}, {"type": "if", "condition": "[ -e (loop)/initrd.gz ]", "complexity": {"has_and": false, "has_or": false, "has_negation": false, "has_file_test": true, "has_string_test": false, "has_numeric_test": true}, "variables_used": [], "operators": ["-e", "-e"]}, {"type": "if", "condition": "[ -e (loop)/slax/boot/initrfs.img ]", "complexity": {"has_and": false, "has_or": false, "has_negation": false, "has_file_test": true, "has_string_test": false, "has_numeric_test": true}, "variables_used": [], "operators": ["-e", "-e"]}, {"type": "if", "condition": "[ -e (loop)/minios/boot/initrfs.img ]", "complexity": {"has_and": false, "has_or": false, "has_negation": false, "has_file_test": true, "has_string_test": false, "has_numeric_test": true}, "variables_used": [], "operators": ["-e", "-e"]}, {"type": "if", "condition": "[ -e (loop)/pmagic/initrd.img ]", "complexity": {"has_and": false, "has_or": false, "has_negation": false, "has_file_test": true, "has_string_test": false, "has_numeric_test": true}, "variables_used": [], "operators": ["-e", "-e"]}, {"type": "if", "condition": "[ -e (loop)/boot/initrd.xz ]", "complexity": {"has_and": false, "has_or": false, "has_negation": false, "has_file_test": true, "has_string_test": false, "has_numeric_test": true}, "variables_used": [], "operators": ["-e", "-e"]}, {"type": "if", "condition": "[ -e (loop)/boot/initrd.gz ]", "complexity": {"has_and": false, "has_or": false, "has_negation": false, "has_file_test": true, "has_string_test": false, "has_numeric_test": true}, "variables_used": [], "operators": ["-e", "-e"]}, {"type": "if", "condition": "[ -f (loop)/boot/initrd ]", "complexity": {"has_and": false, "has_or": false, "has_negation": false, "has_file_test": true, "has_string_test": false, "has_numeric_test": false}, "variables_used": [], "operators": ["-f"]}, {"type": "if", "condition": "[ -f (loop)/boot/x86_64/loader/initrd ]", "complexity": {"has_and": false, "has_or": false, "has_negation": false, "has_file_test": true, "has_string_test": false, "has_numeric_test": false}, "variables_used": [], "operators": ["-f"]}, {"type": "if", "condition": "[ -f (loop)/boot/initramfs-x86_64.img ]", "complexity": {"has_and": false, "has_or": false, "has_negation": false, "has_file_test": true, "has_string_test": false, "has_numeric_test": false}, "variables_used": [], "operators": ["-f", "-x"]}, {"type": "if", "condition": "[ -f (loop)/boot/isolinux/initramfs_data64.cpio.gz ]", "complexity": {"has_and": false, "has_or": false, "has_negation": false, "has_file_test": true, "has_string_test": false, "has_numeric_test": false}, "variables_used": [], "operators": ["-f"]}, {"type": "if", "condition": "[ -f (loop)/boot/initrd.img ]", "complexity": {"has_and": false, "has_or": false, "has_negation": false, "has_file_test": true, "has_string_test": false, "has_numeric_test": false}, "variables_used": [], "operators": ["-f"]}, {"type": "if", "condition": "[ -f (loop)/isolinux/initrd.gz ]", "complexity": {"has_and": false, "has_or": false, "has_negation": false, "has_file_test": true, "has_string_test": false, "has_numeric_test": false}, "variables_used": [], "operators": ["-f"]}, {"type": "if", "condition": "vt_str_begin \"$vt_volume_id\" \"QUBES\"", "complexity": {"has_and": false, "has_or": false, "has_negation": false, "has_file_test": false, "has_string_test": false, "has_numeric_test": false}, "variables_used": ["vt_volume_id"], "operators": []}, {"type": "if", "condition": "[ \"$vt_chosen_size\" = \"1133375488\" ]", "complexity": {"has_and": false, "has_or": false, "has_negation": false, "has_file_test": false, "has_string_test": true, "has_numeric_test": false}, "variables_used": ["vt_chosen_size"], "operators": ["="]}, {"type": "if", "condition": "[ -d (loop)/boot/grub/x86_64-efi ]", "complexity": {"has_and": false, "has_or": false, "has_negation": false, "has_file_test": true, "has_string_test": false, "has_numeric_test": true}, "variables_used": [], "operators": ["-d", "-e", "-e"]}, {"type": "if", "condition": "[ -f (loop)/boot/initrd.img ]", "complexity": {"has_and": false, "has_or": false, "has_negation": false, "has_file_test": true, "has_string_test": false, "has_numeric_test": false}, "variables_used": [], "operators": ["-f"]}, {"type": "if", "condition": "[ -f (loop)/Setup/initrd.gz ]", "complexity": {"has_and": false, "has_or": false, "has_negation": false, "has_file_test": true, "has_string_test": false, "has_numeric_test": false}, "variables_used": [], "operators": ["-f"]}, {"type": "if", "condition": "[ -f (loop)/isolinux/initramfs ]", "complexity": {"has_and": false, "has_or": false, "has_negation": false, "has_file_test": true, "has_string_test": false, "has_numeric_test": false}, "variables_used": [], "operators": ["-f"]}, {"type": "if", "condition": "[ -f (loop)/boot/iniramfs.igz ]", "complexity": {"has_and": false, "has_or": false, "has_negation": false, "has_file_test": true, "has_string_test": false, "has_numeric_test": false}, "variables_used": [], "operators": ["-f"]}, {"type": "if", "condition": "[ -f (loop)/initrd-x86_64 ]", "complexity": {"has_and": false, "has_or": false, "has_negation": false, "has_file_test": true, "has_string_test": false, "has_numeric_test": false}, "variables_used": [], "operators": ["-f", "-x"]}, {"type": "if", "condition": "[ -f (loop)/live/initrd.img ]", "complexity": {"has_and": false, "has_or": false, "has_negation": false, "has_file_test": true, "has_string_test": false, "has_numeric_test": false}, "variables_used": [], "operators": ["-f"]}, {"type": "if", "condition": "[ -f (loop)/initrd.img ]", "complexity": {"has_and": false, "has_or": false, "has_negation": false, "has_file_test": true, "has_string_test": false, "has_numeric_test": false}, "variables_used": [], "operators": ["-f"]}, {"type": "if", "condition": "[ -f (loop)/sysresccd/boot/x86_64/sysresccd.img ]", "complexity": {"has_and": false, "has_or": false, "has_negation": false, "has_file_test": true, "has_string_test": false, "has_numeric_test": false}, "variables_used": [], "operators": ["-f"]}, {"type": "if", "condition": "[ -f (loop)/CDlinux/initrd ]", "complexity": {"has_and": false, "has_or": false, "has_negation": false, "has_file_test": true, "has_string_test": false, "has_numeric_test": false}, "variables_used": [], "operators": ["-f"]}, {"type": "if", "condition": "[ -f (loop)/parabola/boot/x86_64/parabolaiso.img ]", "complexity": {"has_and": false, "has_or": false, "has_negation": false, "has_file_test": true, "has_string_test": false, "has_numeric_test": false}, "variables_used": [], "operators": ["-f"]}, {"type": "if", "condition": "[ -f (loop)/parabola/boot/i686/parabolaiso.img ]", "complexity": {"has_and": false, "has_or": false, "has_negation": false, "has_file_test": true, "has_string_test": false, "has_numeric_test": false}, "variables_used": [], "operators": ["-f"]}, {"type": "if", "condition": "[ -f (loop)/parabola/boot/x86_64/initramfs-linux-libre.img ]", "complexity": {"has_and": false, "has_or": false, "has_negation": false, "has_file_test": true, "has_string_test": false, "has_numeric_test": true}, "variables_used": [], "operators": ["-f", "-l", "-l"]}, {"type": "if", "condition": "[ -f (loop)/parabola/boot/i686/initramfs-linux-libre.img ]", "complexity": {"has_and": false, "has_or": false, "has_negation": false, "has_file_test": true, "has_string_test": false, "has_numeric_test": true}, "variables_used": [], "operators": ["-f", "-l", "-l"]}, {"type": "if", "condition": "[ -f (loop)/hyperbola/boot/x86_64/hyperiso.img ]", "complexity": {"has_and": false, "has_or": false, "has_negation": false, "has_file_test": true, "has_string_test": false, "has_numeric_test": false}, "variables_used": [], "operators": ["-f"]}, {"type": "if", "condition": "[ -f (loop)/hyperbola/boot/i686/hyperiso.img ]", "complexity": {"has_and": false, "has_or": false, "has_negation": false, "has_file_test": true, "has_string_test": false, "has_numeric_test": false}, "variables_used": [], "operators": ["-f"]}, {"type": "if", "condition": "[ -f (loop)/EFI/BOOT/initrd.img ]", "complexity": {"has_and": false, "has_or": false, "has_negation": false, "has_file_test": true, "has_string_test": false, "has_numeric_test": false}, "variables_used": [], "operators": ["-f"]}, {"type": "if", "condition": "[ \"$grub_platform\" != \"pc\" ]", "complexity": {"has_and": false, "has_or": false, "has_negation": true, "has_file_test": false, "has_string_test": true, "has_numeric_test": false}, "variables_used": ["grub_platform"], "operators": ["!=", "!"]}, {"type": "if", "condition": "[ -f (loop)/initrd ]", "complexity": {"has_and": false, "has_or": false, "has_negation": false, "has_file_test": true, "has_string_test": false, "has_numeric_test": false}, "variables_used": [], "operators": ["-f"]}, {"type": "if", "condition": "[ -f (loop)/live/initrd1 ]", "complexity": {"has_and": false, "has_or": false, "has_negation": false, "has_file_test": true, "has_string_test": false, "has_numeric_test": false}, "variables_used": [], "operators": ["-f"]}, {"type": "if", "condition": "[ -f (loop)/isolinux/initrd.img ]", "complexity": {"has_and": false, "has_or": false, "has_negation": false, "has_file_test": true, "has_string_test": false, "has_numeric_test": false}, "variables_used": [], "operators": ["-f"]}, {"type": "if", "condition": "[ -f (loop)/isolinux/initrd.gz ]", "complexity": {"has_and": false, "has_or": false, "has_negation": false, "has_file_test": true, "has_string_test": false, "has_numeric_test": false}, "variables_used": [], "operators": ["-f"]}, {"type": "if", "condition": "[ -f (loop)/syslinux/kernel/initramfs.gz ]", "complexity": {"has_and": false, "has_or": false, "has_negation": false, "has_file_test": true, "has_string_test": false, "has_numeric_test": false}, "variables_used": [], "operators": ["-f"]}, {"type": "if", "condition": "vt_strstr \"$vt_volume_id\" \"Daphile\"", "complexity": {"has_and": false, "has_or": false, "has_negation": false, "has_file_test": false, "has_string_test": false, "has_numeric_test": false}, "variables_used": ["vt_volume_id"], "operators": []}, {"type": "if", "condition": "[ -f (loop)/boot/rootfs.xz ]", "complexity": {"has_and": false, "has_or": false, "has_negation": false, "has_file_test": true, "has_string_test": false, "has_numeric_test": false}, "variables_used": [], "operators": ["-f"]}, {"type": "if", "condition": "[ \"$grub_platform\" != \"pc\" ]", "complexity": {"has_and": false, "has_or": false, "has_negation": true, "has_file_test": false, "has_string_test": true, "has_numeric_test": false}, "variables_used": ["grub_platform"], "operators": ["!=", "!"]}, {"type": "if", "condition": "[ -f (loop)/arch/boot/x86_64/archiso.img ]", "complexity": {"has_and": false, "has_or": false, "has_negation": false, "has_file_test": true, "has_string_test": false, "has_numeric_test": false}, "variables_used": [], "operators": ["-f"]}, {"type": "if", "condition": "[ \"$grub_platform\" != \"pc\" ]", "complexity": {"has_and": false, "has_or": false, "has_negation": true, "has_file_test": false, "has_string_test": true, "has_numeric_test": false}, "variables_used": ["grub_platform"], "operators": ["!=", "!"]}, {"type": "if", "condition": "[ -f (loop)/blackarch/boot/x86_64/archiso.img ]", "complexity": {"has_and": false, "has_or": false, "has_negation": false, "has_file_test": true, "has_string_test": false, "has_numeric_test": false}, "variables_used": [], "operators": ["-f"]}, {"type": "if", "condition": "[ -f (loop)/blackarch/boot/x86_64/initramfs-linux.img ]", "complexity": {"has_and": false, "has_or": false, "has_negation": false, "has_file_test": true, "has_string_test": false, "has_numeric_test": true}, "variables_used": [], "operators": ["-f", "-l"]}, {"type": "if", "condition": "[ -f (loop)/install.amd/initrd.gz ]", "complexity": {"has_and": false, "has_or": false, "has_negation": false, "has_file_test": true, "has_string_test": false, "has_numeric_test": false}, "variables_used": [], "operators": ["-f"]}, {"type": "if", "condition": "[ -f (loop)/boot/grub/kernels.cfg ]", "complexity": {"has_and": false, "has_or": false, "has_negation": false, "has_file_test": true, "has_string_test": false, "has_numeric_test": false}, "variables_used": [], "operators": ["-f"]}, {"type": "if", "condition": "[ -f (loop)/austrumi/initrd.gz ]", "complexity": {"has_and": false, "has_or": false, "has_negation": false, "has_file_test": true, "has_string_test": false, "has_numeric_test": false}, "variables_used": [], "operators": ["-f"]}, {"type": "if", "condition": "[ -f (loop)/EFI/BOOT/bootx64.efi ]", "complexity": {"has_and": false, "has_or": false, "has_negation": false, "has_file_test": true, "has_string_test": false, "has_numeric_test": false}, "variables_used": [], "operators": ["-f"]}, {"type": "if", "condition": "[ -f (loop)/boot/initfs.x86_64-efi ]", "complexity": {"has_and": false, "has_or": false, "has_negation": false, "has_file_test": true, "has_string_test": false, "has_numeric_test": true}, "variables_used": [], "operators": ["-f", "-e", "-e"]}, {"type": "if", "condition": "[ -f (loop)/boot/initfs.i386-pc ]", "complexity": {"has_and": false, "has_or": false, "has_negation": false, "has_file_test": true, "has_string_test": false, "has_numeric_test": false}, "variables_used": [], "operators": ["-f"]}, {"type": "if", "condition": "[ -f (loop)/antiX/initrd.gz ]", "complexity": {"has_and": false, "has_or": false, "has_negation": false, "has_file_test": true, "has_string_test": false, "has_numeric_test": false}, "variables_used": [], "operators": ["-f"]}, {"type": "if", "condition": "[ -f (loop)/360Disk/initrd.gz ]", "complexity": {"has_and": false, "has_or": false, "has_negation": false, "has_file_test": true, "has_string_test": false, "has_numeric_test": false}, "variables_used": [], "operators": ["-f"]}, {"type": "if", "condition": "[ -f (loop)/porteus/initrd.xz ]", "complexity": {"has_and": false, "has_or": false, "has_negation": false, "has_file_test": true, "has_string_test": false, "has_numeric_test": false}, "variables_used": [], "operators": ["-f"]}, {"type": "if", "condition": "[ -f (loop)/pyabr/boot/initrfs.img ]", "complexity": {"has_and": false, "has_or": false, "has_negation": false, "has_file_test": true, "has_string_test": false, "has_numeric_test": false}, "variables_used": [], "operators": ["-f"]}, {"type": "if", "condition": "[ -f (loop)/initrd0.img ]", "complexity": {"has_and": false, "has_or": false, "has_negation": false, "has_file_test": true, "has_string_test": false, "has_numeric_test": false}, "variables_used": [], "operators": ["-f"]}, {"type": "if", "condition": "[ -f (loop)/sysresccd/boot/i686/sysresccd.img ]", "complexity": {"has_and": false, "has_or": false, "has_negation": false, "has_file_test": true, "has_string_test": false, "has_numeric_test": false}, "variables_used": [], "operators": ["-f"]}, {"type": "if", "condition": "[ -f (loop)/boot/full.cz ]", "complexity": {"has_and": false, "has_or": false, "has_negation": false, "has_file_test": true, "has_string_test": false, "has_numeric_test": false}, "variables_used": [], "operators": ["-f"]}, {"type": "if", "condition": "[ -f (loop)/images/pxeboot/initrd.img ]", "complexity": {"has_and": false, "has_or": false, "has_negation": false, "has_file_test": true, "has_string_test": false, "has_numeric_test": false}, "variables_used": [], "operators": ["-f"]}, {"type": "if", "condition": "[ -f (loop)/live/initrd ]", "complexity": {"has_and": false, "has_or": false, "has_negation": false, "has_file_test": true, "has_string_test": false, "has_numeric_test": false}, "variables_used": [], "operators": ["-f"]}, {"type": "if", "condition": "[ -f (loop)/initramfs-linux.img ]", "complexity": {"has_and": false, "has_or": false, "has_negation": false, "has_file_test": true, "has_string_test": false, "has_numeric_test": true}, "variables_used": [], "operators": ["-f", "-l"]}, {"type": "if", "condition": "[ -f (loop)/boot/isolinux/initrd.gz ]", "complexity": {"has_and": false, "has_or": false, "has_negation": false, "has_file_test": true, "has_string_test": false, "has_numeric_test": false}, "variables_used": [], "operators": ["-f"]}, {"type": "if", "condition": "regexp --set 1:vtF<PERSON><PERSON><PERSON> \"(14|13)\\.[0-9]\" \"$2\"", "complexity": {"has_and": false, "has_or": false, "has_negation": false, "has_file_test": false, "has_string_test": false, "has_numeric_test": false}, "variables_used": [], "operators": []}, {"type": "if", "condition": "[ -e (loop)/FreeNAS-MANIFEST ]", "complexity": {"has_and": false, "has_or": false, "has_negation": false, "has_file_test": true, "has_string_test": false, "has_numeric_test": true}, "variables_used": [], "operators": ["-e", "-e"]}, {"type": "if", "condition": "regexp --set 1:vtNasVer \"^(14|13|12|11)\\.[0-9]\" \"$vt_freenas_ver\"", "complexity": {"has_and": false, "has_or": false, "has_negation": false, "has_file_test": false, "has_string_test": false, "has_numeric_test": false}, "variables_used": ["vt_freenas_ver"], "operators": []}, {"type": "if", "condition": "[ -e (loop)/TrueNAS-MANIFEST ]", "complexity": {"has_and": false, "has_or": false, "has_negation": false, "has_file_test": true, "has_string_test": false, "has_numeric_test": true}, "variables_used": [], "operators": ["-e", "-e"]}, {"type": "if", "condition": "regexp --set 1:vtTNasVer \"^(14|13|12|11)\\.[0-9]\" \"$vt_truenas_ver\"", "complexity": {"has_and": false, "has_or": false, "has_negation": false, "has_file_test": false, "has_string_test": false, "has_numeric_test": false}, "variables_used": ["vt_truenas_ver"], "operators": []}, {"type": "if", "condition": "vt_str_begin \"$vt_volume_id\" \"1_\"", "complexity": {"has_and": false, "has_or": false, "has_negation": false, "has_file_test": false, "has_string_test": false, "has_numeric_test": false}, "variables_used": ["vt_volume_id"], "operators": []}, {"type": "if", "condition": "vt_str_begin \"$vt_volume_id\" \"2_\"", "complexity": {"has_and": false, "has_or": false, "has_negation": false, "has_file_test": false, "has_string_test": false, "has_numeric_test": false}, "variables_used": ["vt_volume_id"], "operators": []}, {"type": "if", "condition": "vt_str_begin \"$vt_volume_id\" \"3_\"", "complexity": {"has_and": false, "has_or": false, "has_negation": false, "has_file_test": false, "has_string_test": false, "has_numeric_test": false}, "variables_used": ["vt_volume_id"], "operators": []}, {"type": "if", "condition": "[ -e (loop)/boot/kernel/geom_ventoy.ko ]", "complexity": {"has_and": false, "has_or": false, "has_negation": false, "has_file_test": true, "has_string_test": false, "has_numeric_test": true}, "variables_used": [], "operators": ["-e", "-e"]}, {"type": "if", "condition": "vt_strstr \"$vt_volume_id\" \"GHOSTBSD\"", "complexity": {"has_and": false, "has_or": false, "has_negation": false, "has_file_test": false, "has_string_test": false, "has_numeric_test": false}, "variables_used": ["vt_volume_id"], "operators": []}, {"type": "if", "condition": "vt_strstr \"$vt_volume_id\" \"FREENAS\"", "complexity": {"has_and": false, "has_or": false, "has_negation": false, "has_file_test": false, "has_string_test": false, "has_numeric_test": false}, "variables_used": ["vt_volume_id"], "operators": []}, {"type": "if", "condition": "vt_strstr \"$vt_volume_id\" \"TRUENAS\"", "complexity": {"has_and": false, "has_or": false, "has_negation": false, "has_file_test": false, "has_string_test": false, "has_numeric_test": false}, "variables_used": ["vt_volume_id"], "operators": []}, {"type": "if", "condition": "vt_strstr \"$vt_volume_id\" \"FURYBSD\"", "complexity": {"has_and": false, "has_or": false, "has_negation": false, "has_file_test": false, "has_string_test": false, "has_numeric_test": false}, "variables_used": ["vt_volume_id"], "operators": []}, {"type": "if", "condition": "regexp --set 1:vtBsdVerNum \"^(14|13|12|11|10|9)_[0-9]\" \"$vt_volume_id\"", "complexity": {"has_and": false, "has_or": false, "has_negation": false, "has_file_test": false, "has_string_test": false, "has_numeric_test": false}, "variables_used": ["vt_volume_id"], "operators": []}, {"type": "if", "condition": "[ -d (loop)/usr/midnightbsd-dist ]", "complexity": {"has_and": false, "has_or": false, "has_negation": false, "has_file_test": true, "has_string_test": false, "has_numeric_test": false}, "variables_used": [], "operators": ["-d", "-d"]}, {"type": "if", "condition": "[ -e (loop)/bin/freebsd-version ]", "complexity": {"has_and": false, "has_or": false, "has_negation": false, "has_file_test": true, "has_string_test": false, "has_numeric_test": true}, "variables_used": [], "operators": ["-e", "-e"]}, {"type": "if", "condition": "regexp --set 1:vtBsdVerNum \"\\\"(14|13|12|11|10|9)\\.[0-9]-\" \"$vt_userland_ver\"", "complexity": {"has_and": false, "has_or": false, "has_negation": false, "has_file_test": false, "has_string_test": false, "has_numeric_test": false}, "variables_used": ["vt_userland_ver"], "operators": []}, {"type": "if", "condition": "[ -e (loop)/README.TXT ]", "complexity": {"has_and": false, "has_or": false, "has_negation": false, "has_file_test": true, "has_string_test": false, "has_numeric_test": true}, "variables_used": [], "operators": ["-e", "-e"]}, {"type": "if", "condition": "regexp --set 1:vtBsdVerNum \"FreeBSD (14|13|12|11|10|9)\\.[0-9]-\" \"$vt_freebsd_line1\"", "complexity": {"has_and": false, "has_or": false, "has_negation": false, "has_file_test": false, "has_string_test": false, "has_numeric_test": false}, "variables_used": ["vt_freebsd_line1"], "operators": []}, {"type": "if", "condition": "vt_strstr \"${chosen_path}\" \"MidnightBSD\"", "complexity": {"has_and": false, "has_or": false, "has_negation": false, "has_file_test": false, "has_string_test": false, "has_numeric_test": false}, "variables_used": ["chosen_path"], "operators": []}, {"type": "if", "condition": "[ -e (loop)/usr/freebsd-dist/cloninst.sh ]", "complexity": {"has_and": false, "has_or": false, "has_negation": false, "has_file_test": true, "has_string_test": false, "has_numeric_test": true}, "variables_used": [], "operators": ["-e", "-d", "-e"]}, {"type": "if", "condition": "[ -e (loop)/$file ]", "complexity": {"has_and": false, "has_or": false, "has_negation": false, "has_file_test": true, "has_string_test": false, "has_numeric_test": true}, "variables_used": ["file"], "operators": ["-e", "-e"]}, {"type": "if", "condition": "file --is-i386-kfreebsd (loop)/$file", "complexity": {"has_and": false, "has_or": false, "has_negation": false, "has_file_test": false, "has_string_test": false, "has_numeric_test": false}, "variables_used": ["file"], "operators": []}, {"type": "if", "condition": "[ \"$vt_freebsd_ver\" = \"xx\" ]", "complexity": {"has_and": false, "has_or": false, "has_negation": false, "has_file_test": false, "has_string_test": true, "has_numeric_test": false}, "variables_used": ["vt_freebsd_ver"], "operators": ["="]}, {"type": "if", "condition": "[ -e (loop)/boot/kernel/kernel ]", "complexity": {"has_and": false, "has_or": false, "has_negation": false, "has_file_test": true, "has_string_test": false, "has_numeric_test": true}, "variables_used": [], "operators": ["-e", "-e"]}, {"type": "if", "condition": "[ -e (loop)/boot/kernel/kernel.gz ]", "complexity": {"has_and": false, "has_or": false, "has_negation": false, "has_file_test": true, "has_string_test": false, "has_numeric_test": true}, "variables_used": [], "operators": ["-e", "-e"]}, {"type": "if", "condition": "[ \"$vt_freebsd_ver\" = \"xx\" ]", "complexity": {"has_and": false, "has_or": false, "has_negation": false, "has_file_test": false, "has_string_test": true, "has_numeric_test": false}, "variables_used": ["vt_freebsd_ver"], "operators": ["="]}, {"type": "if", "condition": "[ \"$vt_freebsd_ver\" = \"14.x\" ]", "complexity": {"has_and": false, "has_or": false, "has_negation": false, "has_file_test": false, "has_string_test": true, "has_numeric_test": false}, "variables_used": ["vt_freebsd_ver"], "operators": ["="]}, {"type": "if", "condition": "[ -e (loop)/boot/lua/brand-pfSense.lua ]", "complexity": {"has_and": false, "has_or": false, "has_negation": false, "has_file_test": true, "has_string_test": false, "has_numeric_test": true}, "variables_used": [], "operators": ["-e", "-e"]}, {"type": "if", "condition": "[ -n \"${vtdebug_flag}\" ]", "complexity": {"has_and": false, "has_or": false, "has_negation": false, "has_file_test": false, "has_string_test": false, "has_numeric_test": true}, "variables_used": ["vtdebug_flag"], "operators": ["-n", "-n"]}, {"type": "if", "condition": "[ -e (loop)${file} ]", "complexity": {"has_and": false, "has_or": false, "has_negation": false, "has_file_test": true, "has_string_test": false, "has_numeric_test": true}, "variables_used": ["file"], "operators": ["-e", "-e"]}, {"type": "if", "condition": "[ -n \"$vt_unix_mod_path\" ]", "complexity": {"has_and": false, "has_or": false, "has_negation": false, "has_file_test": false, "has_string_test": false, "has_numeric_test": true}, "variables_used": ["vt_unix_mod_path"], "operators": ["-n", "-n"]}, {"type": "if", "condition": "[ -e (loop)/easyre.ufs.uzip ]", "complexity": {"has_and": false, "has_or": false, "has_negation": false, "has_file_test": true, "has_string_test": false, "has_numeric_test": true}, "variables_used": [], "operators": ["-e", "-e"]}, {"type": "if", "condition": "[ \"$grub_platform\" = \"pc\" ]", "complexity": {"has_and": false, "has_or": false, "has_negation": false, "has_file_test": false, "has_string_test": true, "has_numeric_test": false}, "variables_used": ["grub_platform"], "operators": ["="]}, {"type": "if", "condition": "[ -e (loop)${file} ]", "complexity": {"has_and": false, "has_or": false, "has_negation": false, "has_file_test": true, "has_string_test": false, "has_numeric_test": true}, "variables_used": ["file"], "operators": ["-e", "-e"]}, {"type": "if", "condition": "[ \"$ventoy_compatible\" = \"NO\" ]", "complexity": {"has_and": false, "has_or": false, "has_negation": false, "has_file_test": false, "has_string_test": true, "has_numeric_test": false}, "variables_used": ["ventoy_compatible"], "operators": ["="]}, {"type": "if", "condition": "[ \"$vt_unix_type\" = \"FreeBSD\" ]", "complexity": {"has_and": false, "has_or": false, "has_negation": false, "has_file_test": false, "has_string_test": true, "has_numeric_test": false}, "variables_used": ["vt_unix_type"], "operators": ["="]}, {"type": "if", "condition": "[ \"$vt_unix_type\" = \"DragonFly\" ]", "complexity": {"has_and": false, "has_or": false, "has_negation": false, "has_file_test": false, "has_string_test": true, "has_numeric_test": false}, "variables_used": ["vt_unix_type"], "operators": ["="]}, {"type": "if", "condition": "[ \"$vt_unix_type\" = \"NetBSD\" ]", "complexity": {"has_and": false, "has_or": false, "has_negation": false, "has_file_test": false, "has_string_test": true, "has_numeric_test": false}, "variables_used": ["vt_unix_type"], "operators": ["="]}, {"type": "if", "condition": "[ -n \"${vtdebug_flag}\" ]", "complexity": {"has_and": false, "has_or": false, "has_negation": false, "has_file_test": false, "has_string_test": false, "has_numeric_test": true}, "variables_used": ["vtdebug_flag"], "operators": ["-n", "-n"]}, {"type": "if", "condition": "vt_check_mode 4 \"$vt_chosen_name\"", "complexity": {"has_and": false, "has_or": false, "has_negation": false, "has_file_test": false, "has_string_test": false, "has_numeric_test": false}, "variables_used": ["vt_chosen_name"], "operators": []}, {"type": "if", "condition": "[ \"$ventoy_compatible\" = \"NO\" -o \"$vt_cur_wimboot_mode\" = \"1\" ]", "complexity": {"has_and": false, "has_or": true, "has_negation": false, "has_file_test": false, "has_string_test": true, "has_numeric_test": false}, "variables_used": ["ventoy_compatible", "vt_cur_wimboot_mode"], "operators": ["=", "="]}, {"type": "if", "condition": "[ \"$ventoy_fs_probe\" = \"iso9660\" ]", "complexity": {"has_and": false, "has_or": false, "has_negation": false, "has_file_test": false, "has_string_test": true, "has_numeric_test": false}, "variables_used": ["ventoy_fs_probe"], "operators": ["="]}, {"type": "if", "condition": "[ $vt_wim_cnt -eq 0 ]", "complexity": {"has_and": false, "has_or": false, "has_negation": false, "has_file_test": true, "has_string_test": false, "has_numeric_test": true}, "variables_used": ["vt_wim_cnt"], "operators": ["-e", "-e"]}, {"type": "if", "condition": "[ \"$vt_cur_wimboot_mode\" = \"1\" ]", "complexity": {"has_and": false, "has_or": false, "has_negation": false, "has_file_test": false, "has_string_test": true, "has_numeric_test": false}, "variables_used": ["vt_cur_wimboot_mode"], "operators": ["="]}, {"type": "if", "condition": "[ -n \"$vtoy_chain_mem_addr\" ]", "complexity": {"has_and": false, "has_or": false, "has_negation": false, "has_file_test": false, "has_string_test": false, "has_numeric_test": true}, "variables_used": ["vtoy_chain_mem_addr"], "operators": ["-n", "-n"]}, {"type": "if", "condition": "vt_get_efi_vdisk_offset \"${1}${2}\" vt_efivdisk_offset", "complexity": {"has_and": false, "has_or": false, "has_negation": false, "has_file_test": false, "has_string_test": false, "has_numeric_test": false}, "variables_used": [], "operators": []}, {"type": "if", "condition": "[ -n \"$vt_rp_initrd\" ]", "complexity": {"has_and": false, "has_or": false, "has_negation": false, "has_file_test": false, "has_string_test": false, "has_numeric_test": true}, "variables_used": ["vt_rp_initrd"], "operators": ["-n", "-n"]}, {"type": "if", "condition": "[ \"$ventoy_compatible\" = \"NO\" ]", "complexity": {"has_and": false, "has_or": false, "has_negation": false, "has_file_test": false, "has_string_test": true, "has_numeric_test": false}, "variables_used": ["ventoy_compatible"], "operators": ["="]}, {"type": "if", "condition": "[ \"$ventoy_fs_probe\" = \"udf\" ]", "complexity": {"has_and": false, "has_or": false, "has_negation": false, "has_file_test": false, "has_string_test": true, "has_numeric_test": false}, "variables_used": ["ventoy_fs_probe"], "operators": ["="]}, {"type": "if", "condition": "[ -d (loop)/pmagic ]", "complexity": {"has_and": false, "has_or": false, "has_negation": false, "has_file_test": true, "has_string_test": false, "has_numeric_test": false}, "variables_used": [], "operators": ["-d"]}, {"type": "if", "condition": "[ -e (loop)/$file ]", "complexity": {"has_and": false, "has_or": false, "has_negation": false, "has_file_test": true, "has_string_test": false, "has_numeric_test": true}, "variables_used": ["file"], "operators": ["-e", "-e"]}, {"type": "if", "condition": "[ -d (loop)/loader/entries ]", "complexity": {"has_and": false, "has_or": false, "has_negation": false, "has_file_test": true, "has_string_test": false, "has_numeric_test": false}, "variables_used": [], "operators": ["-d"]}, {"type": "if", "condition": "[ -d (loop)/boot/grub ]", "complexity": {"has_and": false, "has_or": false, "has_negation": false, "has_file_test": true, "has_string_test": false, "has_numeric_test": false}, "variables_used": [], "operators": ["-d"]}, {"type": "if", "condition": "[ $vtcount -eq 0 ]", "complexity": {"has_and": false, "has_or": false, "has_negation": false, "has_file_test": true, "has_string_test": false, "has_numeric_test": true}, "variables_used": ["vtcount"], "operators": ["-e", "-e"]}, {"type": "if", "condition": "[ -e (loop)/EFI/boot/livegrub.cfg ]", "complexity": {"has_and": false, "has_or": false, "has_negation": false, "has_file_test": true, "has_string_test": false, "has_numeric_test": true}, "variables_used": [], "operators": ["-e", "-e"]}, {"type": "if", "condition": "[ \"$vt_efi_dir\" = \"NO\" ]", "complexity": {"has_and": false, "has_or": false, "has_negation": false, "has_file_test": false, "has_string_test": true, "has_numeric_test": false}, "variables_used": ["vt_efi_dir"], "operators": ["="]}, {"type": "if", "condition": "[ -f (loop)/efi.img ]", "complexity": {"has_and": false, "has_or": false, "has_negation": false, "has_file_test": true, "has_string_test": false, "has_numeric_test": false}, "variables_used": [], "operators": ["-f"]}, {"type": "if", "condition": "[ -d (loop)/loader/entries ]", "complexity": {"has_and": false, "has_or": false, "has_negation": false, "has_file_test": true, "has_string_test": false, "has_numeric_test": false}, "variables_used": [], "operators": ["-d"]}, {"type": "if", "condition": "[ -d (loop)/arch ]", "complexity": {"has_and": false, "has_or": false, "has_negation": false, "has_file_test": true, "has_string_test": false, "has_numeric_test": false}, "variables_used": [], "operators": ["-d"]}, {"type": "if", "condition": "[ -f (loop)/arch/boot/x86_64/archiso.img ]", "complexity": {"has_and": false, "has_or": false, "has_negation": false, "has_file_test": true, "has_string_test": false, "has_numeric_test": false}, "variables_used": [], "operators": ["-f"]}, {"type": "if", "condition": "[ -f (loop)/arch/boot/x86_64/initramfs-linux.img ]", "complexity": {"has_and": false, "has_or": false, "has_negation": false, "has_file_test": true, "has_string_test": false, "has_numeric_test": true}, "variables_used": [], "operators": ["-f", "-l"]}, {"type": "if", "condition": "[ -f (loop)/boot/initramfs_x86_64.img ]", "complexity": {"has_and": false, "has_or": false, "has_negation": false, "has_file_test": true, "has_string_test": false, "has_numeric_test": false}, "variables_used": [], "operators": ["-f"]}, {"type": "if", "condition": "[ -d (loop)/blackarch ]", "complexity": {"has_and": false, "has_or": false, "has_negation": false, "has_file_test": true, "has_string_test": false, "has_numeric_test": false}, "variables_used": [], "operators": ["-d"]}, {"type": "if", "condition": "[ -f (loop)/blackarch/boot/x86_64/archiso.img ]", "complexity": {"has_and": false, "has_or": false, "has_negation": false, "has_file_test": true, "has_string_test": false, "has_numeric_test": false}, "variables_used": [], "operators": ["-f"]}, {"type": "if", "condition": "[ -f (loop)/blackarch/boot/x86_64/initramfs-linux.img ]", "complexity": {"has_and": false, "has_or": false, "has_negation": false, "has_file_test": true, "has_string_test": false, "has_numeric_test": true}, "variables_used": [], "operators": ["-f", "-l"]}, {"type": "if", "condition": "[ -d (loop)/anarchy ]", "complexity": {"has_and": false, "has_or": false, "has_negation": false, "has_file_test": true, "has_string_test": false, "has_numeric_test": false}, "variables_used": [], "operators": ["-d"]}, {"type": "if", "condition": "[ -f (loop)/anarchy/boot/x86_64/initramfs-linux.img ]", "complexity": {"has_and": false, "has_or": false, "has_negation": false, "has_file_test": true, "has_string_test": false, "has_numeric_test": true}, "variables_used": [], "operators": ["-f", "-l"]}, {"type": "if", "condition": "[ -d (loop)/parabola ]", "complexity": {"has_and": false, "has_or": false, "has_negation": false, "has_file_test": true, "has_string_test": false, "has_numeric_test": false}, "variables_used": [], "operators": ["-d"]}, {"type": "if", "condition": "[ -f (loop)/parabola/boot/x86_64/parabolaiso.img ]", "complexity": {"has_and": false, "has_or": false, "has_negation": false, "has_file_test": true, "has_string_test": false, "has_numeric_test": false}, "variables_used": [], "operators": ["-f"]}, {"type": "if", "condition": "[ -f (loop)/parabola/boot/x86_64/initramfs-linux-libre.img ]", "complexity": {"has_and": false, "has_or": false, "has_negation": false, "has_file_test": true, "has_string_test": false, "has_numeric_test": true}, "variables_used": [], "operators": ["-f", "-l", "-l"]}, {"type": "if", "condition": "[ -f (loop)/EFI/BOOT/initrd.gz ]", "complexity": {"has_and": false, "has_or": false, "has_negation": false, "has_file_test": true, "has_string_test": false, "has_numeric_test": false}, "variables_used": [], "operators": ["-f"]}, {"type": "if", "condition": "[ -f (loop)/loader/entries/thinstation.conf ]", "complexity": {"has_and": false, "has_or": false, "has_negation": false, "has_file_test": true, "has_string_test": false, "has_numeric_test": false}, "variables_used": [], "operators": ["-f"]}, {"type": "if", "condition": "[ -f (loop)/loader/entries/pisi-efi-x86_64.conf ]", "complexity": {"has_and": false, "has_or": false, "has_negation": false, "has_file_test": true, "has_string_test": false, "has_numeric_test": true}, "variables_used": [], "operators": ["-f", "-e", "-x", "-e"]}, {"type": "if", "condition": "[ $vt_replace_cnt -eq 0 ]", "complexity": {"has_and": false, "has_or": false, "has_negation": false, "has_file_test": true, "has_string_test": false, "has_numeric_test": true}, "variables_used": ["vt_replace_cnt"], "operators": ["-e", "-e"]}, {"type": "if", "condition": "[ -d (loop)/EFI/boot/entries ]", "complexity": {"has_and": false, "has_or": false, "has_negation": false, "has_file_test": true, "has_string_test": false, "has_numeric_test": false}, "variables_used": [], "operators": ["-d"]}, {"type": "if", "condition": "[ -f (loop)/parabola/boot/x86_64/parabolaiso.img ]", "complexity": {"has_and": false, "has_or": false, "has_negation": false, "has_file_test": true, "has_string_test": false, "has_numeric_test": false}, "variables_used": [], "operators": ["-f"]}, {"type": "if", "condition": "[ -f (loop)/hyperbola/boot/x86_64/hyperiso.img ]", "complexity": {"has_and": false, "has_or": false, "has_negation": false, "has_file_test": true, "has_string_test": false, "has_numeric_test": false}, "variables_used": [], "operators": ["-f"]}, {"type": "if", "condition": "[ -d (loop)/EFI/BOOT/entries ]", "complexity": {"has_and": false, "has_or": false, "has_negation": false, "has_file_test": true, "has_string_test": false, "has_numeric_test": false}, "variables_used": [], "operators": ["-d"]}, {"type": "if", "condition": "[ -f (loop)/parabola/boot/x86_64/parabolaiso.img ]", "complexity": {"has_and": false, "has_or": false, "has_negation": false, "has_file_test": true, "has_string_test": false, "has_numeric_test": false}, "variables_used": [], "operators": ["-f"]}, {"type": "if", "condition": "[ -f (loop)/parabola/boot/x86_64/initramfs-linux-libre.img ]", "complexity": {"has_and": false, "has_or": false, "has_negation": false, "has_file_test": true, "has_string_test": false, "has_numeric_test": true}, "variables_used": [], "operators": ["-f", "-l", "-l"]}, {"type": "if", "condition": "[ -e (loop)/syslinux/alt0/full.cz ]", "complexity": {"has_and": false, "has_or": false, "has_negation": false, "has_file_test": true, "has_string_test": false, "has_numeric_test": true}, "variables_used": [], "operators": ["-e", "-e"]}, {"type": "if", "condition": "vt_str_begin \"$vt_volume_id\" \"SolusLive\"", "complexity": {"has_and": false, "has_or": false, "has_negation": false, "has_file_test": false, "has_string_test": false, "has_numeric_test": false}, "variables_used": ["vt_volume_id"], "operators": []}, {"type": "if", "condition": "[ -n \"$LoadIsoEfiDriver\" -a $vt_chosen_size -LT 104857600 ]", "complexity": {"has_and": true, "has_or": false, "has_negation": false, "has_file_test": false, "has_string_test": false, "has_numeric_test": true}, "variables_used": ["vt_chosen_size", "LoadIsoEfiDriver"], "operators": ["-n", "-n"]}, {"type": "if", "condition": "[ -f (loop)/efi/clover/cloverx64.efi ]", "complexity": {"has_and": false, "has_or": false, "has_negation": false, "has_file_test": true, "has_string_test": false, "has_numeric_test": false}, "variables_used": [], "operators": ["-f"]}, {"type": "if", "condition": "[ -n \"$vtoy_chain_mem_addr\" ]", "complexity": {"has_and": false, "has_or": false, "has_negation": false, "has_file_test": false, "has_string_test": false, "has_numeric_test": true}, "variables_used": ["vtoy_chain_mem_addr"], "operators": ["-n", "-n"]}, {"type": "if", "condition": "vt_check_mode 3 \"$vt_chosen_name\"", "complexity": {"has_and": false, "has_or": false, "has_negation": false, "has_file_test": false, "has_string_test": false, "has_numeric_test": false}, "variables_used": ["vt_chosen_name"], "operators": []}, {"type": "if", "condition": "vt_str_begin \"$vt_volume_id\" \"HOLO_\"", "complexity": {"has_and": false, "has_or": false, "has_negation": false, "has_file_test": false, "has_string_test": false, "has_numeric_test": false}, "variables_used": ["vt_volume_id"], "operators": []}, {"type": "if", "condition": "[ -d (loop)/loader/entries ]", "complexity": {"has_and": false, "has_or": false, "has_negation": false, "has_file_test": true, "has_string_test": false, "has_numeric_test": false}, "variables_used": [], "operators": ["-d"]}, {"type": "if", "condition": "vt_str_begin \"$vt_volume_id\" \"KRD\"", "complexity": {"has_and": false, "has_or": false, "has_negation": false, "has_file_test": false, "has_string_test": false, "has_numeric_test": false}, "variables_used": ["vt_volume_id"], "operators": []}, {"type": "if", "condition": "[ -f (loop)/boot/grub/grub.cfg.sig ]", "complexity": {"has_and": false, "has_or": false, "has_negation": false, "has_file_test": true, "has_string_test": false, "has_numeric_test": false}, "variables_used": [], "operators": ["-f"]}, {"type": "if", "condition": "[ -n \"$vtGrub2Mode\" ]", "complexity": {"has_and": false, "has_or": false, "has_negation": false, "has_file_test": false, "has_string_test": false, "has_numeric_test": true}, "variables_used": ["vtGrub2Mode"], "operators": ["-n", "-n"]}, {"type": "if", "condition": "[ \"$VTOY_EFI_ARCH\" != \"mips\" ]", "complexity": {"has_and": false, "has_or": false, "has_negation": true, "has_file_test": false, "has_string_test": true, "has_numeric_test": false}, "variables_used": ["VTOY_EFI_ARCH"], "operators": ["!=", "!"]}, {"type": "if", "condition": "[ -e \"$cfg\" ]", "complexity": {"has_and": false, "has_or": false, "has_negation": false, "has_file_test": true, "has_string_test": false, "has_numeric_test": true}, "variables_used": ["cfg"], "operators": ["-e", "-e"]}, {"type": "if", "condition": "[ $vtback_cfg_find -eq 0 ]", "complexity": {"has_and": false, "has_or": false, "has_negation": false, "has_file_test": true, "has_string_test": false, "has_numeric_test": true}, "variables_used": ["vtback_cfg_find"], "operators": ["-e", "-e"]}, {"type": "if", "condition": "[ -f (loop)/loader/loader.conf -a -d (loop)/loader/entries ]", "complexity": {"has_and": true, "has_or": false, "has_negation": false, "has_file_test": true, "has_string_test": false, "has_numeric_test": false}, "variables_used": [], "operators": ["-f", "-d"]}, {"type": "if", "condition": "vt_str_begin \"$vt_volume_id\" \"HOLO_\"", "complexity": {"has_and": false, "has_or": false, "has_negation": false, "has_file_test": false, "has_string_test": false, "has_numeric_test": false}, "variables_used": ["vt_volume_id"], "operators": []}, {"type": "if", "condition": "[ $vtback_cfg_find -eq 0 ]", "complexity": {"has_and": false, "has_or": false, "has_negation": false, "has_file_test": true, "has_string_test": false, "has_numeric_test": true}, "variables_used": ["vtback_cfg_find"], "operators": ["-e", "-e"]}, {"type": "if", "condition": "[ -f (loop)/boot/isolinux/syslnx64.cfg ]", "complexity": {"has_and": false, "has_or": false, "has_negation": false, "has_file_test": true, "has_string_test": false, "has_numeric_test": false}, "variables_used": [], "operators": ["-f"]}, {"type": "if", "condition": "[ -f (loop)/boot/syslinux/porteus.cfg ]", "complexity": {"has_and": false, "has_or": false, "has_negation": false, "has_file_test": true, "has_string_test": false, "has_numeric_test": false}, "variables_used": [], "operators": ["-f"]}, {"type": "if", "condition": "[ \"$vtback_cfg_find\" = \"0\" ]", "complexity": {"has_and": false, "has_or": false, "has_negation": false, "has_file_test": false, "has_string_test": true, "has_numeric_test": false}, "variables_used": ["vtback_cfg_find"], "operators": ["="]}, {"type": "if", "condition": "[ -n \"$vtoy_chain_mem_addr\" ]", "complexity": {"has_and": false, "has_or": false, "has_negation": false, "has_file_test": false, "has_string_test": false, "has_numeric_test": true}, "variables_used": ["vtoy_chain_mem_addr"], "operators": ["-n", "-n"]}, {"type": "if", "condition": "vt_str_begin \"$vt_volume_id\" \"ARCARESCUE\"", "complexity": {"has_and": false, "has_or": false, "has_negation": false, "has_file_test": false, "has_string_test": false, "has_numeric_test": false}, "variables_used": ["vt_volume_id"], "operators": []}, {"type": "if", "condition": "[ -n \"$vtisouefi\" ]", "complexity": {"has_and": false, "has_or": false, "has_negation": false, "has_file_test": false, "has_string_test": false, "has_numeric_test": true}, "variables_used": ["vtisouefi"], "operators": ["-n", "-n"]}, {"type": "if", "condition": "vt_check_mode 2 \"$vt_chosen_name\"", "complexity": {"has_and": false, "has_or": false, "has_negation": false, "has_file_test": false, "has_string_test": false, "has_numeric_test": false}, "variables_used": ["vt_chosen_name"], "operators": []}, {"type": "if", "condition": "! vt_is_udf \"${1}${chosen_path}\"", "complexity": {"has_and": false, "has_or": false, "has_negation": true, "has_file_test": false, "has_string_test": false, "has_numeric_test": false}, "variables_used": ["chosen_path"], "operators": ["!"]}, {"type": "if", "condition": "vt_str_begin \"$vt_volume_id\" \"EasyStartup\"", "complexity": {"has_and": false, "has_or": false, "has_negation": false, "has_file_test": false, "has_string_test": false, "has_numeric_test": false}, "variables_used": ["vt_volume_id"], "operators": []}, {"type": "if", "condition": "[ -d (loop)/EFI ]", "complexity": {"has_and": false, "has_or": false, "has_negation": false, "has_file_test": true, "has_string_test": false, "has_numeric_test": false}, "variables_used": [], "operators": ["-d"]}, {"type": "if", "condition": "[ -d (loop)/efi ]", "complexity": {"has_and": false, "has_or": false, "has_negation": false, "has_file_test": true, "has_string_test": false, "has_numeric_test": false}, "variables_used": [], "operators": ["-d"]}, {"type": "if", "condition": "[ -n \"$vtcompat\" ]", "complexity": {"has_and": false, "has_or": false, "has_negation": false, "has_file_test": false, "has_string_test": false, "has_numeric_test": true}, "variables_used": ["vtcompat"], "operators": ["-n", "-n"]}, {"type": "if", "condition": "vt_check_mode 1 \"$vt_chosen_name\"", "complexity": {"has_and": false, "has_or": false, "has_negation": false, "has_file_test": false, "has_string_test": false, "has_numeric_test": false}, "variables_used": ["vt_chosen_name"], "operators": []}, {"type": "if", "condition": "[ \"$ventoy_fs_probe\" = \"iso9660\" ]", "complexity": {"has_and": false, "has_or": false, "has_negation": false, "has_file_test": false, "has_string_test": true, "has_numeric_test": false}, "variables_used": ["ventoy_fs_probe"], "operators": ["="]}, {"type": "if", "condition": "[ \"$vtoy_os\" = \"Windows\" ]", "complexity": {"has_and": false, "has_or": false, "has_negation": false, "has_file_test": false, "has_string_test": true, "has_numeric_test": false}, "variables_used": ["vtoy_os"], "operators": ["="]}, {"type": "if", "condition": "[ \"$vtoy_os\" = \"Unix\" ]", "complexity": {"has_and": false, "has_or": false, "has_negation": false, "has_file_test": false, "has_string_test": true, "has_numeric_test": false}, "variables_used": ["vtoy_os"], "operators": ["="]}, {"type": "if", "condition": "[ -f (loop)/x86/sources/boot.wim -a -f (loop)/x64/sources/boot.wim ]", "complexity": {"has_and": true, "has_or": false, "has_negation": false, "has_file_test": true, "has_string_test": false, "has_numeric_test": false}, "variables_used": [], "operators": ["-f", "-f"]}, {"type": "if", "condition": "[ \"$vtoy_wimboot_bit\" = \"32\" ]", "complexity": {"has_and": false, "has_or": false, "has_negation": false, "has_file_test": false, "has_string_test": true, "has_numeric_test": false}, "variables_used": ["vtoy_wimboot_bit"], "operators": ["="]}, {"type": "if", "condition": "vt_is_pe64 $vtoy_wimboot_prefix/setup.exe", "complexity": {"has_and": false, "has_or": false, "has_negation": false, "has_file_test": false, "has_string_test": false, "has_numeric_test": false}, "variables_used": ["vtoy_wimboot_prefix"], "operators": []}, {"type": "if", "condition": "[ -n \"${vtdebug_flag}\" ]", "complexity": {"has_and": false, "has_or": false, "has_negation": false, "has_file_test": false, "has_string_test": false, "has_numeric_test": true}, "variables_used": ["vtdebug_flag"], "operators": ["-n", "-n"]}, {"type": "if", "condition": "[ \"$grub_platform\" = \"pc\" ]", "complexity": {"has_and": false, "has_or": false, "has_negation": false, "has_file_test": false, "has_string_test": true, "has_numeric_test": false}, "variables_used": ["grub_platform"], "operators": ["="]}, {"type": "if", "condition": "[ $? -eq 0 ]", "complexity": {"has_and": false, "has_or": false, "has_negation": false, "has_file_test": true, "has_string_test": false, "has_numeric_test": true}, "variables_used": [], "operators": ["-e", "-e"]}, {"type": "if", "condition": "[ -n \"${vtdebug_flag}\" ]", "complexity": {"has_and": false, "has_or": false, "has_negation": false, "has_file_test": false, "has_string_test": false, "has_numeric_test": true}, "variables_used": ["vtdebug_flag"], "operators": ["-n", "-n"]}, {"type": "if", "condition": "[ \"$2\" != \"0\" ]", "complexity": {"has_and": false, "has_or": false, "has_negation": true, "has_file_test": false, "has_string_test": true, "has_numeric_test": false}, "variables_used": [], "operators": ["!=", "!"]}, {"type": "if", "condition": "[ \"$grub_platform\" = \"pc\" ]", "complexity": {"has_and": false, "has_or": false, "has_negation": false, "has_file_test": false, "has_string_test": true, "has_numeric_test": false}, "variables_used": ["grub_platform"], "operators": ["="]}, {"type": "if", "condition": "[ \"$VTOY_EFI_ARCH\" = \"x64\" -a \"$vtoy_wim_bit\" = \"32\" ]", "complexity": {"has_and": true, "has_or": false, "has_negation": false, "has_file_test": false, "has_string_test": true, "has_numeric_test": false}, "variables_used": ["vtoy_wim_bit", "VTOY_EFI_ARCH"], "operators": ["=", "="]}, {"type": "if", "condition": "[ $? -eq 0 ]", "complexity": {"has_and": false, "has_or": false, "has_negation": false, "has_file_test": true, "has_string_test": false, "has_numeric_test": true}, "variables_used": [], "operators": ["-e", "-e"]}, {"type": "if", "condition": "[ -F (loop)/efi/boot/boot${VTOY_EFI_ARCH}.efi ]", "complexity": {"has_and": false, "has_or": false, "has_negation": false, "has_file_test": false, "has_string_test": false, "has_numeric_test": false}, "variables_used": ["VTOY_EFI_ARCH"], "operators": []}, {"type": "if", "condition": "[ \"$grub_platform\" = \"pc\" ]", "complexity": {"has_and": false, "has_or": false, "has_negation": false, "has_file_test": false, "has_string_test": true, "has_numeric_test": false}, "variables_used": ["grub_platform"], "operators": ["="]}, {"type": "if", "condition": "[ \"$grub_cpu\" = \"i386\" ]", "complexity": {"has_and": false, "has_or": false, "has_negation": false, "has_file_test": false, "has_string_test": true, "has_numeric_test": false}, "variables_used": ["grub_cpu"], "operators": ["="]}, {"type": "if", "condition": "vt_is_standard_winiso (loop)", "complexity": {"has_and": false, "has_or": false, "has_negation": false, "has_file_test": false, "has_string_test": false, "has_numeric_test": false}, "variables_used": [], "operators": []}, {"type": "if", "condition": "[ -n \"$vtoy_pe_wim_path\" ]", "complexity": {"has_and": false, "has_or": false, "has_negation": false, "has_file_test": false, "has_string_test": false, "has_numeric_test": true}, "variables_used": ["vtoy_pe_wim_path"], "operators": ["-n", "-n"]}, {"type": "if", "condition": "[ -F \"(loop)/$vsdi\" ]", "complexity": {"has_and": false, "has_or": false, "has_negation": false, "has_file_test": false, "has_string_test": false, "has_numeric_test": false}, "variables_used": ["vsdi"], "operators": []}, {"type": "if", "condition": "[ \"$grub_platform\" = \"pc\" ]", "complexity": {"has_and": false, "has_or": false, "has_negation": false, "has_file_test": false, "has_string_test": true, "has_numeric_test": false}, "variables_used": ["grub_platform"], "operators": ["="]}, {"type": "if", "condition": "vt_check_mode 4 \"$vt_chosen_name\"", "complexity": {"has_and": false, "has_or": false, "has_negation": false, "has_file_test": false, "has_string_test": false, "has_numeric_test": false}, "variables_used": ["vt_chosen_name"], "operators": []}, {"type": "if", "condition": "[ \"$ventoy_compatible\" = \"NO\" -o \"$vt_cur_wimboot_mode\" = \"1\" ]", "complexity": {"has_and": false, "has_or": true, "has_negation": false, "has_file_test": false, "has_string_test": true, "has_numeric_test": false}, "variables_used": ["ventoy_compatible", "vt_cur_wimboot_mode"], "operators": ["=", "="]}, {"type": "if", "condition": "[ \"$ventoy_fs_probe\" = \"iso9660\" ]", "complexity": {"has_and": false, "has_or": false, "has_negation": false, "has_file_test": false, "has_string_test": true, "has_numeric_test": false}, "variables_used": ["ventoy_fs_probe"], "operators": ["="]}, {"type": "if", "condition": "[ $vt_wim_cnt -eq 0 ]", "complexity": {"has_and": false, "has_or": false, "has_negation": false, "has_file_test": true, "has_string_test": false, "has_numeric_test": true}, "variables_used": ["vt_wim_cnt"], "operators": ["-e", "-e"]}, {"type": "if", "condition": "[ -z \"$vt_cur_wimboot_mode\" ]", "complexity": {"has_and": false, "has_or": false, "has_negation": false, "has_file_test": false, "has_string_test": false, "has_numeric_test": false}, "variables_used": ["vt_cur_wimboot_mode"], "operators": ["-z"]}, {"type": "if", "condition": "[ \"$vt_cur_wimboot_mode\" = \"1\" ]", "complexity": {"has_and": false, "has_or": false, "has_negation": false, "has_file_test": false, "has_string_test": true, "has_numeric_test": false}, "variables_used": ["vt_cur_wimboot_mode"], "operators": ["="]}, {"type": "if", "condition": "[ -n \"$vtoy_chain_mem_addr\" ]", "complexity": {"has_and": false, "has_or": false, "has_negation": false, "has_file_test": false, "has_string_test": false, "has_numeric_test": true}, "variables_used": ["vtoy_chain_mem_addr"], "operators": ["-n", "-n"]}, {"type": "if", "condition": "[ \"$ventoy_compatible\" = \"NO\" ]", "complexity": {"has_and": false, "has_or": false, "has_negation": false, "has_file_test": false, "has_string_test": true, "has_numeric_test": false}, "variables_used": ["ventoy_compatible"], "operators": ["="]}, {"type": "if", "condition": "[ \"$ventoy_compatible\" = \"NO\" ]", "complexity": {"has_and": false, "has_or": false, "has_negation": false, "has_file_test": false, "has_string_test": true, "has_numeric_test": false}, "variables_used": ["ventoy_compatible"], "operators": ["="]}, {"type": "if", "condition": "[ \"$ventoy_fs_probe\" = \"udf\" ]", "complexity": {"has_and": false, "has_or": false, "has_negation": false, "has_file_test": false, "has_string_test": true, "has_numeric_test": false}, "variables_used": ["ventoy_fs_probe"], "operators": ["="]}, {"type": "if", "condition": "vt_syslinux_need_nojoliet \"$1$2\"", "complexity": {"has_and": false, "has_or": false, "has_negation": false, "has_file_test": false, "has_string_test": false, "has_numeric_test": false}, "variables_used": [], "operators": []}, {"type": "if", "condition": "[ -d (loop)/pmagic ]", "complexity": {"has_and": false, "has_or": false, "has_negation": false, "has_file_test": true, "has_string_test": false, "has_numeric_test": false}, "variables_used": [], "operators": ["-d"]}, {"type": "if", "condition": "[ -d (loop)/$dir ]", "complexity": {"has_and": false, "has_or": false, "has_negation": false, "has_file_test": true, "has_string_test": false, "has_numeric_test": false}, "variables_used": ["dir"], "operators": ["-d"]}, {"type": "if", "condition": "[ -d (loop)/arch/boot/syslinux ]", "complexity": {"has_and": false, "has_or": false, "has_negation": false, "has_file_test": true, "has_string_test": false, "has_numeric_test": false}, "variables_used": [], "operators": ["-d"]}, {"type": "if", "condition": "[ -d (loop)/anarchy/boot/syslinux ]", "complexity": {"has_and": false, "has_or": false, "has_negation": false, "has_file_test": true, "has_string_test": false, "has_numeric_test": false}, "variables_used": [], "operators": ["-d"]}, {"type": "if", "condition": "[ -d (loop)/manjaro ]", "complexity": {"has_and": false, "has_or": false, "has_negation": false, "has_file_test": true, "has_string_test": false, "has_numeric_test": false}, "variables_used": [], "operators": ["-d"]}, {"type": "if", "condition": "[ -e (loop)/boot/grub/kernels.cfg ]", "complexity": {"has_and": false, "has_or": false, "has_negation": false, "has_file_test": true, "has_string_test": false, "has_numeric_test": true}, "variables_used": [], "operators": ["-e", "-e"]}, {"type": "if", "condition": "[ -e (loop)/boot/grub/grub.cfg ]", "complexity": {"has_and": false, "has_or": false, "has_negation": false, "has_file_test": true, "has_string_test": false, "has_numeric_test": true}, "variables_used": [], "operators": ["-e", "-e"]}, {"type": "if", "condition": "[ $vtcount -eq 0 ]", "complexity": {"has_and": false, "has_or": false, "has_negation": false, "has_file_test": true, "has_string_test": false, "has_numeric_test": true}, "variables_used": ["vtcount"], "operators": ["-e", "-e"]}, {"type": "if", "condition": "[ -d (loop)/rancheros ]", "complexity": {"has_and": false, "has_or": false, "has_negation": false, "has_file_test": true, "has_string_test": false, "has_numeric_test": false}, "variables_used": [], "operators": ["-d"]}, {"type": "if", "condition": "[ -n \"$vtoy_chain_mem_addr\" ]", "complexity": {"has_and": false, "has_or": false, "has_negation": false, "has_file_test": false, "has_string_test": false, "has_numeric_test": true}, "variables_used": ["vtoy_chain_mem_addr"], "operators": ["-n", "-n"]}, {"type": "if", "condition": "vt_check_mode 3 \"$vt_chosen_name\"", "complexity": {"has_and": false, "has_or": false, "has_negation": false, "has_file_test": false, "has_string_test": false, "has_numeric_test": false}, "variables_used": ["vt_chosen_name"], "operators": []}, {"type": "if", "condition": "vt_str_begin \"$vt_volume_id\" \"HOLO_\"", "complexity": {"has_and": false, "has_or": false, "has_negation": false, "has_file_test": false, "has_string_test": false, "has_numeric_test": false}, "variables_used": ["vt_volume_id"], "operators": []}, {"type": "if", "condition": "[ -d (loop)/loader/entries ]", "complexity": {"has_and": false, "has_or": false, "has_negation": false, "has_file_test": true, "has_string_test": false, "has_numeric_test": false}, "variables_used": [], "operators": ["-d"]}, {"type": "if", "condition": "[ -n \"$vtGrub2Mode\" ]", "complexity": {"has_and": false, "has_or": false, "has_negation": false, "has_file_test": false, "has_string_test": false, "has_numeric_test": true}, "variables_used": ["vtGrub2Mode"], "operators": ["-n", "-n"]}, {"type": "if", "condition": "[ -e \"$cfg\" ]", "complexity": {"has_and": false, "has_or": false, "has_negation": false, "has_file_test": true, "has_string_test": false, "has_numeric_test": true}, "variables_used": ["cfg"], "operators": ["-e", "-e"]}, {"type": "if", "condition": "[ $vtback_cfg_find -eq 0 ]", "complexity": {"has_and": false, "has_or": false, "has_negation": false, "has_file_test": true, "has_string_test": false, "has_numeric_test": true}, "variables_used": ["vtback_cfg_find"], "operators": ["-e", "-e"]}, {"type": "if", "condition": "[ -f (loop)/loader/loader.conf -a -d (loop)/loader/entries ]", "complexity": {"has_and": true, "has_or": false, "has_negation": false, "has_file_test": true, "has_string_test": false, "has_numeric_test": false}, "variables_used": [], "operators": ["-f", "-d"]}, {"type": "if", "condition": "vt_str_begin \"$vt_volume_id\" \"HOLO_\"", "complexity": {"has_and": false, "has_or": false, "has_negation": false, "has_file_test": false, "has_string_test": false, "has_numeric_test": false}, "variables_used": ["vt_volume_id"], "operators": []}, {"type": "if", "condition": "[ $vtback_cfg_find -eq 0 ]", "complexity": {"has_and": false, "has_or": false, "has_negation": false, "has_file_test": true, "has_string_test": false, "has_numeric_test": true}, "variables_used": ["vtback_cfg_find"], "operators": ["-e", "-e"]}, {"type": "if", "condition": "[ -f (loop)/boot/isolinux/syslnx64.cfg ]", "complexity": {"has_and": false, "has_or": false, "has_negation": false, "has_file_test": true, "has_string_test": false, "has_numeric_test": false}, "variables_used": [], "operators": ["-f"]}, {"type": "if", "condition": "[ -f (loop)/boot/syslinux/porteus.cfg ]", "complexity": {"has_and": false, "has_or": false, "has_negation": false, "has_file_test": true, "has_string_test": false, "has_numeric_test": false}, "variables_used": [], "operators": ["-f"]}, {"type": "if", "condition": "[ -n \"$vtoy_chain_mem_addr\" ]", "complexity": {"has_and": false, "has_or": false, "has_negation": false, "has_file_test": false, "has_string_test": false, "has_numeric_test": true}, "variables_used": ["vtoy_chain_mem_addr"], "operators": ["-n", "-n"]}, {"type": "if", "condition": "[ -n \"$vtcompat\" ]", "complexity": {"has_and": false, "has_or": false, "has_negation": false, "has_file_test": false, "has_string_test": false, "has_numeric_test": true}, "variables_used": ["vtcompat"], "operators": ["-n", "-n"]}, {"type": "if", "condition": "vt_check_mode 1 \"$vt_chosen_name\"", "complexity": {"has_and": false, "has_or": false, "has_negation": false, "has_file_test": false, "has_string_test": false, "has_numeric_test": false}, "variables_used": ["vt_chosen_name"], "operators": []}, {"type": "if", "condition": "[ \"$ventoy_fs_probe\" = \"iso9660\" ]", "complexity": {"has_and": false, "has_or": false, "has_negation": false, "has_file_test": false, "has_string_test": true, "has_numeric_test": false}, "variables_used": ["ventoy_fs_probe"], "operators": ["="]}, {"type": "if", "condition": "[ \"$vtoy_os\" = \"Windows\" ]", "complexity": {"has_and": false, "has_or": false, "has_negation": false, "has_file_test": false, "has_string_test": true, "has_numeric_test": false}, "variables_used": ["vtoy_os"], "operators": ["="]}, {"type": "if", "condition": "[ \"$vtoy_os\" = \"Unix\" ]", "complexity": {"has_and": false, "has_or": false, "has_negation": false, "has_file_test": false, "has_string_test": true, "has_numeric_test": false}, "variables_used": ["vtoy_os"], "operators": ["="]}, {"type": "if", "condition": "[ -d (loop)/ ]", "complexity": {"has_and": false, "has_or": false, "has_negation": false, "has_file_test": true, "has_string_test": false, "has_numeric_test": false}, "variables_used": [], "operators": ["-d"]}, {"type": "if", "condition": "[ \"$VTOY_EFI_ARCH\" = \"aa64\" ]", "complexity": {"has_and": false, "has_or": false, "has_negation": false, "has_file_test": false, "has_string_test": true, "has_numeric_test": false}, "variables_used": ["VTOY_EFI_ARCH"], "operators": ["="]}, {"type": "if", "condition": "[ \"$VTOY_EFI_ARCH\" = \"mips\" ]", "complexity": {"has_and": false, "has_or": false, "has_negation": false, "has_file_test": false, "has_string_test": true, "has_numeric_test": false}, "variables_used": ["VTOY_EFI_ARCH"], "operators": ["="]}, {"type": "if", "condition": "[ \"$vt_chosen_size\" = \"403701760\" ]", "complexity": {"has_and": false, "has_or": false, "has_negation": false, "has_file_test": false, "has_string_test": true, "has_numeric_test": false}, "variables_used": ["vt_chosen_size"], "operators": ["="]}, {"type": "if", "condition": "vt_str_str \"$vt_chosen_path\" \"/deepin-live\"", "complexity": {"has_and": false, "has_or": false, "has_negation": false, "has_file_test": false, "has_string_test": false, "has_numeric_test": true}, "variables_used": ["vt_chosen_path"], "operators": ["-l"]}, {"type": "if", "condition": "vt_str_begin \"$vt_volume_id\" \"PHOTON_\"", "complexity": {"has_and": false, "has_or": false, "has_negation": false, "has_file_test": false, "has_string_test": false, "has_numeric_test": false}, "variables_used": ["vt_volume_id"], "operators": []}, {"type": "if", "condition": "vt_str_begin \"$vt_volume_id\" \"smgl-test-quinq-x86_64\"", "complexity": {"has_and": false, "has_or": false, "has_negation": false, "has_file_test": true, "has_string_test": false, "has_numeric_test": true}, "variables_used": ["vt_volume_id"], "operators": ["-x", "-t", "-q"]}, {"type": "if", "condition": "vt_str_begin \"$vt_volume_id\" \"LDiagBootable\"", "complexity": {"has_and": false, "has_or": false, "has_negation": false, "has_file_test": false, "has_string_test": false, "has_numeric_test": false}, "variables_used": ["vt_volume_id"], "operators": []}, {"type": "if", "condition": "vt_str_begin \"$vt_volume_id\" \"KAOS_\"", "complexity": {"has_and": false, "has_or": false, "has_negation": false, "has_file_test": false, "has_string_test": false, "has_numeric_test": false}, "variables_used": ["vt_volume_id"], "operators": []}, {"type": "if", "condition": "[ $vt_volume_space -NE $vt_chosen_size ]", "complexity": {"has_and": false, "has_or": false, "has_negation": false, "has_file_test": false, "has_string_test": false, "has_numeric_test": false}, "variables_used": ["vt_volume_space", "vt_chosen_size"], "operators": []}, {"type": "if", "condition": "[ $vt_chosen_size_mod -ne 0 ]", "complexity": {"has_and": false, "has_or": false, "has_negation": false, "has_file_test": false, "has_string_test": false, "has_numeric_test": true}, "variables_used": ["vt_chosen_size_mod"], "operators": ["-n", "-n"]}, {"type": "if", "condition": "vt_check_password \"${vt_chosen_path}\"", "complexity": {"has_and": false, "has_or": false, "has_negation": false, "has_file_test": false, "has_string_test": false, "has_numeric_test": false}, "variables_used": ["vt_chosen_path"], "operators": []}, {"type": "if", "condition": "ventoy_vcfg_proc \"${vt_chosen_path}\"", "complexity": {"has_and": false, "has_or": false, "has_negation": false, "has_file_test": false, "has_string_test": false, "has_numeric_test": false}, "variables_used": ["vt_chosen_path"], "operators": []}, {"type": "if", "condition": "vt_is_udf \"${vtoy_iso_part}${vt_chosen_path}\"", "complexity": {"has_and": false, "has_or": false, "has_negation": false, "has_file_test": false, "has_string_test": false, "has_numeric_test": false}, "variables_used": ["vtoy_iso_part", "vt_chosen_path"], "operators": []}, {"type": "if", "condition": "[ -d (loop)/ ]", "complexity": {"has_and": false, "has_or": false, "has_negation": false, "has_file_test": true, "has_string_test": false, "has_numeric_test": false}, "variables_used": [], "operators": ["-d"]}, {"type": "if", "condition": "vt_need_secondary_menu \"$vt_chosen_name\"", "complexity": {"has_and": false, "has_or": false, "has_negation": false, "has_file_test": false, "has_string_test": false, "has_numeric_test": false}, "variables_used": ["vt_chosen_name"], "operators": []}, {"type": "if", "condition": "[ \"$VTOY_SECOND_EXIT\" = \"1\" ]", "complexity": {"has_and": false, "has_or": false, "has_negation": false, "has_file_test": false, "has_string_test": true, "has_numeric_test": false}, "variables_used": ["VTOY_SECOND_EXIT"], "operators": ["="]}, {"type": "if", "condition": "vt_str_begin \"$vt_volume_id\" \"Avira\"", "complexity": {"has_and": false, "has_or": false, "has_negation": false, "has_file_test": false, "has_string_test": false, "has_numeric_test": false}, "variables_used": ["vt_volume_id"], "operators": []}, {"type": "if", "condition": "vt_str_begin \"$vt_volume_id\" \"Endless-OS\"", "complexity": {"has_and": false, "has_or": false, "has_negation": false, "has_file_test": false, "has_string_test": false, "has_numeric_test": false}, "variables_used": ["vt_volume_id"], "operators": []}, {"type": "if", "condition": "vt_str_begin \"$vt_volume_id\" \"TENS-Public\"", "complexity": {"has_and": false, "has_or": false, "has_negation": false, "has_file_test": false, "has_string_test": false, "has_numeric_test": false}, "variables_used": ["vt_volume_id"], "operators": []}, {"type": "if", "condition": "vt_check_mode 0 \"$vt_chosen_name\"", "complexity": {"has_and": false, "has_or": false, "has_negation": false, "has_file_test": false, "has_string_test": false, "has_numeric_test": false}, "variables_used": ["vt_chosen_name"], "operators": []}, {"type": "if", "condition": "[ \"$grub_platform\" = \"pc\" ]", "complexity": {"has_and": false, "has_or": false, "has_negation": false, "has_file_test": false, "has_string_test": true, "has_numeric_test": false}, "variables_used": ["grub_platform"], "operators": ["="]}, {"type": "if", "condition": "vt_iso_vd_id_begin 1 0 \"Memtest86+\"", "complexity": {"has_and": false, "has_or": false, "has_negation": false, "has_file_test": false, "has_string_test": false, "has_numeric_test": false}, "variables_used": [], "operators": []}, {"type": "if", "condition": "vt_iso_vd_id_begin 0 1 \"KolibriOS\"", "complexity": {"has_and": false, "has_or": false, "has_negation": false, "has_file_test": false, "has_string_test": false, "has_numeric_test": false}, "variables_used": [], "operators": []}, {"type": "if", "condition": "[ $vt_chosen_size -LE 67108864 ]", "complexity": {"has_and": false, "has_or": false, "has_negation": false, "has_file_test": false, "has_string_test": false, "has_numeric_test": false}, "variables_used": ["vt_chosen_size"], "operators": []}, {"type": "if", "condition": "vt_str_begin \"$vt_chosen_name\" \"iKuai\"", "complexity": {"has_and": false, "has_or": false, "has_negation": false, "has_file_test": false, "has_string_test": false, "has_numeric_test": false}, "variables_used": ["vt_chosen_name"], "operators": []}, {"type": "if", "condition": "[ \"$grub_platform\" = \"pc\" ]", "complexity": {"has_and": false, "has_or": false, "has_negation": false, "has_file_test": false, "has_string_test": true, "has_numeric_test": false}, "variables_used": ["grub_platform"], "operators": ["="]}, {"type": "if", "condition": "[ -n \"$vtMemDiskBoot\" ]", "complexity": {"has_and": false, "has_or": false, "has_negation": false, "has_file_test": false, "has_string_test": false, "has_numeric_test": true}, "variables_used": ["vtMemDiskBoot"], "operators": ["-n", "-n"]}, {"type": "if", "condition": "[ -n \"$vtMemDiskBoot\" ]", "complexity": {"has_and": false, "has_or": false, "has_negation": false, "has_file_test": false, "has_string_test": false, "has_numeric_test": true}, "variables_used": ["vtMemDiskBoot"], "operators": ["-n", "-n"]}, {"type": "if", "condition": "vt_check_password \"${vt_chosen_path}\"", "complexity": {"has_and": false, "has_or": false, "has_negation": false, "has_file_test": false, "has_string_test": false, "has_numeric_test": false}, "variables_used": ["vt_chosen_path"], "operators": []}, {"type": "if", "condition": "[ \"$grub_platform\" = \"pc\" ]", "complexity": {"has_and": false, "has_or": false, "has_negation": false, "has_file_test": false, "has_string_test": true, "has_numeric_test": false}, "variables_used": ["grub_platform"], "operators": ["="]}, {"type": "if", "condition": "vt_check_password \"${vt_chosen_path}\"", "complexity": {"has_and": false, "has_or": false, "has_negation": false, "has_file_test": false, "has_string_test": false, "has_numeric_test": false}, "variables_used": ["vt_chosen_path"], "operators": []}, {"type": "if", "condition": "ventoy_vcfg_proc \"${vt_chosen_path}\"", "complexity": {"has_and": false, "has_or": false, "has_negation": false, "has_file_test": false, "has_string_test": false, "has_numeric_test": false}, "variables_used": ["vt_chosen_path"], "operators": []}, {"type": "if", "condition": "vt_wim_check_bootable \"${vtoy_iso_part}${vt_chosen_path}\"", "complexity": {"has_and": false, "has_or": false, "has_negation": false, "has_file_test": false, "has_string_test": false, "has_numeric_test": false}, "variables_used": ["vtoy_iso_part", "vt_chosen_path"], "operators": []}, {"type": "if", "condition": "[ -n \"$vtoy_chain_mem_addr\" ]", "complexity": {"has_and": false, "has_or": false, "has_negation": false, "has_file_test": false, "has_string_test": false, "has_numeric_test": true}, "variables_used": ["vtoy_chain_mem_addr"], "operators": ["-n", "-n"]}, {"type": "if", "condition": "[ \"$grub_platform\" = \"pc\" ]", "complexity": {"has_and": false, "has_or": false, "has_negation": false, "has_file_test": false, "has_string_test": true, "has_numeric_test": false}, "variables_used": ["grub_platform"], "operators": ["="]}, {"type": "if", "condition": "vt_check_password \"${vt_chosen_path}\"", "complexity": {"has_and": false, "has_or": false, "has_negation": false, "has_file_test": false, "has_string_test": false, "has_numeric_test": false}, "variables_used": ["vt_chosen_path"], "operators": []}, {"type": "if", "condition": "ventoy_vcfg_proc \"${vt_chosen_path}\"", "complexity": {"has_and": false, "has_or": false, "has_negation": false, "has_file_test": false, "has_string_test": false, "has_numeric_test": false}, "variables_used": ["vt_chosen_path"], "operators": []}, {"type": "if", "condition": "vt_is_vlnk_name \"${vt_chosen_path}\"", "complexity": {"has_and": false, "has_or": false, "has_negation": false, "has_file_test": false, "has_string_test": false, "has_numeric_test": false}, "variables_used": ["vt_chosen_path"], "operators": []}, {"type": "if", "condition": "[ -z \"$vt_vlnk_dst\" ]", "complexity": {"has_and": false, "has_or": false, "has_negation": false, "has_file_test": false, "has_string_test": false, "has_numeric_test": false}, "variables_used": ["vt_vlnk_dst"], "operators": ["-z"]}, {"type": "if", "condition": "[ -n \"$vtoy_vhd_buf_addr\" ]", "complexity": {"has_and": false, "has_or": false, "has_negation": false, "has_file_test": false, "has_string_test": false, "has_numeric_test": true}, "variables_used": ["vtoy_vhd_buf_addr"], "operators": ["-n", "-n"]}, {"type": "if", "condition": "[ \"$grub_platform\" = \"pc\" ]", "complexity": {"has_and": false, "has_or": false, "has_negation": false, "has_file_test": false, "has_string_test": true, "has_numeric_test": false}, "variables_used": ["grub_platform"], "operators": ["="]}, {"type": "if", "condition": "vt_check_password \"${vt_chosen_path}\"", "complexity": {"has_and": false, "has_or": false, "has_negation": false, "has_file_test": false, "has_string_test": false, "has_numeric_test": false}, "variables_used": ["vt_chosen_path"], "operators": []}, {"type": "if", "condition": "ventoy_vcfg_proc \"${vt_chosen_path}\"", "complexity": {"has_and": false, "has_or": false, "has_negation": false, "has_file_test": false, "has_string_test": false, "has_numeric_test": false}, "variables_used": ["vt_chosen_path"], "operators": []}, {"type": "if", "condition": "vt_is_vlnk_name \"${vt_chosen_path}\"", "complexity": {"has_and": false, "has_or": false, "has_negation": false, "has_file_test": false, "has_string_test": false, "has_numeric_test": false}, "variables_used": ["vt_chosen_path"], "operators": []}, {"type": "if", "condition": "[ -z \"$vt_vlnk_dst\" ]", "complexity": {"has_and": false, "has_or": false, "has_negation": false, "has_file_test": false, "has_string_test": false, "has_numeric_test": false}, "variables_used": ["vt_vlnk_dst"], "operators": ["-z"]}, {"type": "if", "condition": "[ \"$VTOY_VHD_NO_WARNING\" != \"1\" ]", "complexity": {"has_and": false, "has_or": false, "has_negation": true, "has_file_test": false, "has_string_test": true, "has_numeric_test": false}, "variables_used": ["VTOY_VHD_NO_WARNING"], "operators": ["!=", "!"]}, {"type": "if", "condition": "[ \"$vtoy_iso_fs\" != \"ntfs\" ]", "complexity": {"has_and": false, "has_or": false, "has_negation": true, "has_file_test": false, "has_string_test": true, "has_numeric_test": false}, "variables_used": ["vtoy_iso_fs"], "operators": ["!=", "!"]}, {"type": "if", "condition": "vt_str_begin $vtoytype vhd", "complexity": {"has_and": false, "has_or": false, "has_negation": false, "has_file_test": false, "has_string_test": false, "has_numeric_test": false}, "variables_used": ["vtoytype"], "operators": []}, {"type": "if", "condition": "[ \"$vtoytype\" = \"raw\" ]", "complexity": {"has_and": false, "has_or": false, "has_negation": false, "has_file_test": false, "has_string_test": true, "has_numeric_test": false}, "variables_used": ["vtoytype"], "operators": ["="]}, {"type": "if", "condition": "[ \"$vtoytype\" = \"vdi\" ]", "complexity": {"has_and": false, "has_or": false, "has_negation": false, "has_file_test": false, "has_string_test": true, "has_numeric_test": false}, "variables_used": ["vtoytype"], "operators": ["="]}, {"type": "if", "condition": "[ $vtoysupport -eq 1 ]", "complexity": {"has_and": false, "has_or": false, "has_negation": false, "has_file_test": true, "has_string_test": false, "has_numeric_test": true}, "variables_used": ["vtoysupport"], "operators": ["-e", "-e"]}, {"type": "if", "condition": "[ \"$grub_platform\" = \"pc\" ]", "complexity": {"has_and": false, "has_or": false, "has_negation": false, "has_file_test": false, "has_string_test": true, "has_numeric_test": false}, "variables_used": ["grub_platform"], "operators": ["="]}, {"type": "if", "condition": "[ \"$parttype\" = \"gpt\" -a $AltBootPart -eq 0 ]", "complexity": {"has_and": true, "has_or": false, "has_negation": false, "has_file_test": true, "has_string_test": true, "has_numeric_test": true}, "variables_used": ["AltBootPart", "parttype"], "operators": ["-e", "-e", "="]}, {"type": "if", "condition": "[ \"$parttype\" = \"mbr\" -a $AltBootPart -eq 0 ]", "complexity": {"has_and": true, "has_or": false, "has_negation": false, "has_file_test": true, "has_string_test": true, "has_numeric_test": true}, "variables_used": ["AltBootPart", "parttype"], "operators": ["-e", "-e", "="]}, {"type": "if", "condition": "[ -n \"$vtoy_chain_mem_addr\" ]", "complexity": {"has_and": false, "has_or": false, "has_negation": false, "has_file_test": false, "has_string_test": false, "has_numeric_test": true}, "variables_used": ["vtoy_chain_mem_addr"], "operators": ["-n", "-n"]}, {"type": "if", "condition": "[ \"$grub_platform\" = \"pc\" ]", "complexity": {"has_and": false, "has_or": false, "has_negation": false, "has_file_test": false, "has_string_test": true, "has_numeric_test": false}, "variables_used": ["grub_platform"], "operators": ["="]}, {"type": "if", "condition": "vt_check_secureboot_var", "complexity": {"has_and": false, "has_or": false, "has_negation": false, "has_file_test": false, "has_string_test": false, "has_numeric_test": false}, "variables_used": [], "operators": []}, {"type": "if", "condition": "vt_check_password \"${vt_chosen_path}\"", "complexity": {"has_and": false, "has_or": false, "has_negation": false, "has_file_test": false, "has_string_test": false, "has_numeric_test": false}, "variables_used": ["vt_chosen_path"], "operators": []}, {"type": "if", "condition": "ventoy_vcfg_proc \"${vt_chosen_path}\"", "complexity": {"has_and": false, "has_or": false, "has_negation": false, "has_file_test": false, "has_string_test": false, "has_numeric_test": false}, "variables_used": ["vt_chosen_path"], "operators": []}, {"type": "if", "condition": "vt_is_vlnk_name \"${vt_chosen_path}\"", "complexity": {"has_and": false, "has_or": false, "has_negation": false, "has_file_test": false, "has_string_test": false, "has_numeric_test": false}, "variables_used": ["vt_chosen_path"], "operators": []}, {"type": "if", "condition": "[ -z \"$vt_vlnk_dst\" ]", "complexity": {"has_and": false, "has_or": false, "has_negation": false, "has_file_test": false, "has_string_test": false, "has_numeric_test": false}, "variables_used": ["vt_vlnk_dst"], "operators": ["-z"]}, {"type": "if", "condition": "[ -n \"$vt_module_ver\" ]", "complexity": {"has_and": false, "has_or": false, "has_negation": false, "has_file_test": false, "has_string_test": false, "has_numeric_test": true}, "variables_used": ["vt_module_ver"], "operators": ["-n", "-n"]}, {"type": "if", "condition": "[ -e (easysfs)/lib/modules/$vt_module_ver/$mod ]", "complexity": {"has_and": false, "has_or": false, "has_negation": false, "has_file_test": true, "has_string_test": false, "has_numeric_test": true}, "variables_used": ["vt_module_ver", "mod"], "operators": ["-e", "-e"]}, {"type": "if", "condition": "[ -e (vtimghd,2)/easyos/easy.sfs ]", "complexity": {"has_and": false, "has_or": false, "has_negation": false, "has_file_test": true, "has_string_test": false, "has_numeric_test": true}, "variables_used": [], "operators": ["-e", "-e"]}, {"type": "if", "condition": "[ -d (vtimghd,2)/easyos/releases ]", "complexity": {"has_and": false, "has_or": false, "has_negation": false, "has_file_test": true, "has_string_test": false, "has_numeric_test": false}, "variables_used": [], "operators": ["-d"]}, {"type": "if", "condition": "[ -n \"$vt_module_ver\" ]", "complexity": {"has_and": false, "has_or": false, "has_negation": false, "has_file_test": false, "has_string_test": false, "has_numeric_test": true}, "variables_used": ["vt_module_ver"], "operators": ["-n", "-n"]}, {"type": "if", "condition": "[ -e (easysfs)/lib/modules/$vt_module_ver/$mod ]", "complexity": {"has_and": false, "has_or": false, "has_negation": false, "has_file_test": true, "has_string_test": false, "has_numeric_test": true}, "variables_used": ["vt_module_ver", "mod"], "operators": ["-e", "-e"]}, {"type": "if", "condition": "[ \"$elec_ver\" = \"LibreELEC\" ]", "complexity": {"has_and": false, "has_or": false, "has_negation": false, "has_file_test": false, "has_string_test": true, "has_numeric_test": false}, "variables_used": ["elec_ver"], "operators": ["="]}, {"type": "if", "condition": "[ -f (vtimghd,1)/system ]", "complexity": {"has_and": false, "has_or": false, "has_negation": false, "has_file_test": true, "has_string_test": false, "has_numeric_test": false}, "variables_used": [], "operators": ["-f"]}, {"type": "if", "condition": "[ -n \"$vt_module_ver\" ]", "complexity": {"has_and": false, "has_or": false, "has_negation": false, "has_file_test": false, "has_string_test": false, "has_numeric_test": true}, "variables_used": ["vt_module_ver"], "operators": ["-n", "-n"]}, {"type": "if", "condition": "[ -e (elecsfs)/usr/lib/kernel-overlays/base/lib/modules/$vt_module_ver/$mod ]", "complexity": {"has_and": false, "has_or": true, "has_negation": false, "has_file_test": true, "has_string_test": false, "has_numeric_test": true}, "variables_used": ["vt_module_ver", "mod"], "operators": ["-e", "-e"]}, {"type": "if", "condition": "[ -n \"$vt_module_ver\" ]", "complexity": {"has_and": false, "has_or": false, "has_negation": false, "has_file_test": false, "has_string_test": false, "has_numeric_test": true}, "variables_used": ["vt_module_ver"], "operators": ["-n", "-n"]}, {"type": "if", "condition": "[ -n \"$vt_module_ver\" ]", "complexity": {"has_and": false, "has_or": false, "has_negation": false, "has_file_test": false, "has_string_test": false, "has_numeric_test": true}, "variables_used": ["vt_module_ver"], "operators": ["-n", "-n"]}, {"type": "if", "condition": "[ $vtoy_img_max_part_end -GT $vt_chosen_size ]", "complexity": {"has_and": false, "has_or": false, "has_negation": false, "has_file_test": false, "has_string_test": false, "has_numeric_test": false}, "variables_used": ["vt_chosen_size", "vtoy_img_max_part_end"], "operators": []}, {"type": "if", "condition": "[ -e (vtimghd,1)/boot/recalbox ]", "complexity": {"has_and": false, "has_or": false, "has_negation": false, "has_file_test": true, "has_string_test": false, "has_numeric_test": true}, "variables_used": [], "operators": ["-e", "-e"]}, {"type": "if", "condition": "[ -n \"$vt_module_ver\" ]", "complexity": {"has_and": false, "has_or": false, "has_negation": false, "has_file_test": false, "has_string_test": false, "has_numeric_test": true}, "variables_used": ["vt_module_ver"], "operators": ["-n", "-n"]}, {"type": "if", "condition": "[ -e (vtimghd,2)/lib64 ]", "complexity": {"has_and": false, "has_or": false, "has_negation": false, "has_file_test": true, "has_string_test": false, "has_numeric_test": true}, "variables_used": [], "operators": ["-e", "-e"]}, {"type": "if", "condition": "[ -f (vtimghd,2)/lib/modules/$vt_dir_name/dm-mod.ko ]", "complexity": {"has_and": false, "has_or": false, "has_negation": false, "has_file_test": true, "has_string_test": false, "has_numeric_test": false}, "variables_used": ["vt_dir_name"], "operators": ["-f"]}, {"type": "if", "condition": "[ -f (vtimghd,2)/lib/modules/$vt_dir_name/dax.ko ]", "complexity": {"has_and": false, "has_or": false, "has_negation": false, "has_file_test": true, "has_string_test": false, "has_numeric_test": false}, "variables_used": ["vt_dir_name"], "operators": ["-f"]}, {"type": "if", "condition": "[ ! -f ${vtoy_iso_part}/ventoy/ventoy_openwrt.xz ]", "complexity": {"has_and": false, "has_or": false, "has_negation": true, "has_file_test": true, "has_string_test": false, "has_numeric_test": false}, "variables_used": ["vtoy_iso_part"], "operators": ["-f", "!"]}, {"type": "if", "condition": "vt_img_check_range \"${vtoy_iso_part}${vt_chosen_path}\"", "complexity": {"has_and": false, "has_or": false, "has_negation": false, "has_file_test": false, "has_string_test": false, "has_numeric_test": false}, "variables_used": ["vtoy_iso_part", "vt_chosen_path"], "operators": []}, {"type": "if", "condition": "[ $openwrt_plugin_need -eq 1 ]", "complexity": {"has_and": false, "has_or": false, "has_negation": false, "has_file_test": true, "has_string_test": false, "has_numeric_test": true}, "variables_used": ["openwrt_plugin_need"], "operators": ["-e", "-e"]}, {"type": "if", "condition": "[ -f ${vtoy_iso_part}/ventoy/ventoy_openwrt.xz ]", "complexity": {"has_and": false, "has_or": false, "has_negation": false, "has_file_test": true, "has_string_test": false, "has_numeric_test": false}, "variables_used": ["vtoy_iso_part"], "operators": ["-f"]}, {"type": "if", "condition": "[ \"$grub_platform\" = \"pc\" ]", "complexity": {"has_and": false, "has_or": false, "has_negation": false, "has_file_test": false, "has_string_test": true, "has_numeric_test": false}, "variables_used": ["grub_platform"], "operators": ["="]}, {"type": "if", "condition": "[ \"$grub_platform\" = \"pc\" ]", "complexity": {"has_and": false, "has_or": false, "has_negation": false, "has_file_test": false, "has_string_test": true, "has_numeric_test": false}, "variables_used": ["grub_platform"], "operators": ["="]}, {"type": "if", "condition": "vt_check_password \"${vt_chosen_path}\"", "complexity": {"has_and": false, "has_or": false, "has_negation": false, "has_file_test": false, "has_string_test": false, "has_numeric_test": false}, "variables_used": ["vt_chosen_path"], "operators": []}, {"type": "if", "condition": "ventoy_vcfg_proc \"${vt_chosen_path}\"", "complexity": {"has_and": false, "has_or": false, "has_negation": false, "has_file_test": false, "has_string_test": false, "has_numeric_test": false}, "variables_used": ["vt_chosen_path"], "operators": []}, {"type": "if", "condition": "[ \"$grub_platform\" = \"pc\" ]", "complexity": {"has_and": false, "has_or": false, "has_negation": false, "has_file_test": false, "has_string_test": true, "has_numeric_test": false}, "variables_used": ["grub_platform"], "operators": ["="]}, {"type": "if", "condition": "vt_check_mode 0 \"$vt_chosen_name\"", "complexity": {"has_and": false, "has_or": false, "has_negation": false, "has_file_test": false, "has_string_test": false, "has_numeric_test": false}, "variables_used": ["vt_chosen_name"], "operators": []}, {"type": "if", "condition": "[ \"$vtImgHd1Label\" = \"STATE\" ]", "complexity": {"has_and": false, "has_or": false, "has_negation": false, "has_file_test": false, "has_string_test": true, "has_numeric_test": false}, "variables_used": ["vtImgHd1Label"], "operators": ["="]}, {"type": "if", "condition": "[ -d (vtimghd,2)/lib ]", "complexity": {"has_and": false, "has_or": false, "has_negation": false, "has_file_test": true, "has_string_test": false, "has_numeric_test": false}, "variables_used": [], "operators": ["-d"]}, {"type": "if", "condition": "[ -z \"$vtImgHd1Label\" ]", "complexity": {"has_and": false, "has_or": false, "has_negation": false, "has_file_test": false, "has_string_test": false, "has_numeric_test": false}, "variables_used": ["vtImgHd1Label"], "operators": ["-z"]}, {"type": "if", "condition": "[ -d (vtimghd,2)/efi ]", "complexity": {"has_and": false, "has_or": false, "has_negation": false, "has_file_test": true, "has_string_test": false, "has_numeric_test": false}, "variables_used": [], "operators": ["-d"]}, {"type": "if", "condition": "[ -d (vtimghd,12)/efi ]", "complexity": {"has_and": false, "has_or": false, "has_negation": false, "has_file_test": true, "has_string_test": false, "has_numeric_test": false}, "variables_used": [], "operators": ["-d"]}, {"type": "if", "condition": "[ -e (vtimghd,1)/etc/hostname ]", "complexity": {"has_and": false, "has_or": false, "has_negation": false, "has_file_test": true, "has_string_test": false, "has_numeric_test": true}, "variables_used": [], "operators": ["-e", "-e"]}, {"type": "if", "condition": "vt_str_begin \"$vtImgHd3Label\" \"ROOT-\"", "complexity": {"has_and": false, "has_or": false, "has_negation": false, "has_file_test": false, "has_string_test": false, "has_numeric_test": false}, "variables_used": ["vtImgHd3Label"], "operators": []}, {"type": "if", "condition": "[ -f (vtimghd,3)/etc/os-release.d/ID ]", "complexity": {"has_and": false, "has_or": false, "has_negation": false, "has_file_test": true, "has_string_test": false, "has_numeric_test": false}, "variables_used": [], "operators": ["-f", "-r"]}, {"type": "if", "condition": "vt_str_begin \"$vt_release_line1\" \"FydeOS\"", "complexity": {"has_and": false, "has_or": false, "has_negation": false, "has_file_test": false, "has_string_test": false, "has_numeric_test": false}, "variables_used": ["vt_release_line1"], "operators": []}, {"type": "if", "condition": "[ -f (vtimghd,3)/etc/cloudready-release ]", "complexity": {"has_and": false, "has_or": false, "has_negation": false, "has_file_test": true, "has_string_test": false, "has_numeric_test": false}, "variables_used": [], "operators": ["-f", "-r"]}, {"type": "if", "condition": "[ -f (vtimghd,3)/etc/chrome_dev.conf ]", "complexity": {"has_and": false, "has_or": false, "has_negation": false, "has_file_test": true, "has_string_test": false, "has_numeric_test": false}, "variables_used": [], "operators": ["-f"]}, {"type": "if", "condition": "vt_str_begin \"$vtImgHd3Label\" \"fwts-result\"", "complexity": {"has_and": false, "has_or": false, "has_negation": false, "has_file_test": true, "has_string_test": false, "has_numeric_test": false}, "variables_used": ["vtImgHd3Label"], "operators": ["-r"]}, {"type": "if", "condition": "vt_str_begin \"$vtImgHd1Label\" \"LAKKA\"", "complexity": {"has_and": false, "has_or": false, "has_negation": false, "has_file_test": false, "has_string_test": false, "has_numeric_test": false}, "variables_used": ["vtImgHd1Label"], "operators": []}, {"type": "if", "condition": "vt_str_begin \"$vtImgHd1Label\" \"LIBREELEC\"", "complexity": {"has_and": false, "has_or": false, "has_negation": false, "has_file_test": false, "has_string_test": false, "has_numeric_test": false}, "variables_used": ["vtImgHd1Label"], "operators": []}, {"type": "if", "condition": "vt_str_begin \"$vtImgHd1Label\" \"paldo-live\"", "complexity": {"has_and": false, "has_or": false, "has_negation": false, "has_file_test": false, "has_string_test": false, "has_numeric_test": true}, "variables_used": ["vtImgHd1Label"], "operators": ["-l"]}, {"type": "if", "condition": "vt_str_begin \"$vtImgHostname\" \"freedombox\"", "complexity": {"has_and": false, "has_or": false, "has_negation": false, "has_file_test": false, "has_string_test": false, "has_numeric_test": false}, "variables_used": ["vtImgHostname"], "operators": []}, {"type": "if", "condition": "vt_str_begin \"$vtImgHd1Label\" \"BATOCERA\"", "complexity": {"has_and": false, "has_or": false, "has_negation": false, "has_file_test": false, "has_string_test": false, "has_numeric_test": false}, "variables_used": ["vtImgHd1Label"], "operators": []}, {"type": "if", "condition": "vt_str_begin \"$vtImgHd1Label\" \"Tails\"", "complexity": {"has_and": false, "has_or": false, "has_negation": false, "has_file_test": false, "has_string_test": false, "has_numeric_test": false}, "variables_used": ["vtImgHd1Label"], "operators": []}, {"type": "if", "condition": "[ \"$vtImgHd2Label\" = \"RECALBOX\" -o \"$vtImgHd1Label\" = \"RECALBOX\" ]", "complexity": {"has_and": false, "has_or": true, "has_negation": false, "has_file_test": false, "has_string_test": true, "has_numeric_test": false}, "variables_used": ["vtImgHd2Label", "vtImgHd1Label"], "operators": ["=", "="]}, {"type": "if", "condition": "[ \"$vtImgHd1Label\" = \"ESYSRESCUE\" ]", "complexity": {"has_and": false, "has_or": false, "has_negation": false, "has_file_test": false, "has_string_test": true, "has_numeric_test": false}, "variables_used": ["vtImgHd1Label"], "operators": ["="]}, {"type": "if", "condition": "[ -e (vtimghd,1)/easy.sfs ]", "complexity": {"has_and": false, "has_or": false, "has_negation": false, "has_file_test": true, "has_string_test": false, "has_numeric_test": true}, "variables_used": [], "operators": ["-e", "-e"]}, {"type": "if", "condition": "[ -d (vtimghd,2)/easyos ]", "complexity": {"has_and": false, "has_or": false, "has_negation": false, "has_file_test": true, "has_string_test": false, "has_numeric_test": false}, "variables_used": [], "operators": ["-d"]}, {"type": "if", "condition": "[ -e (vtimghd,1)/volumio.initrd ]", "complexity": {"has_and": false, "has_or": false, "has_negation": false, "has_file_test": true, "has_string_test": false, "has_numeric_test": true}, "variables_used": [], "operators": ["-e", "-e"]}, {"type": "if", "condition": "[ -f (vtimghd,2)/loader/entries/ubos.conf ]", "complexity": {"has_and": false, "has_or": false, "has_negation": false, "has_file_test": true, "has_string_test": false, "has_numeric_test": false}, "variables_used": [], "operators": ["-f"]}, {"type": "if", "condition": "[ -f (vtimghd,2)/etc/openwrt_version ]", "complexity": {"has_and": false, "has_or": false, "has_negation": false, "has_file_test": true, "has_string_test": false, "has_numeric_test": false}, "variables_used": [], "operators": ["-f"]}, {"type": "if", "condition": "[ -f (vtimghd,1)/efi/boot/mt86.png ]", "complexity": {"has_and": false, "has_or": false, "has_negation": false, "has_file_test": true, "has_string_test": false, "has_numeric_test": false}, "variables_used": [], "operators": ["-f"]}, {"type": "if", "condition": "[ \"$grub_platform\" = \"pc\" ]", "complexity": {"has_and": false, "has_or": false, "has_negation": false, "has_file_test": false, "has_string_test": true, "has_numeric_test": false}, "variables_used": ["grub_platform"], "operators": ["="]}, {"type": "if", "condition": "[ \"$grub_platform\" = \"pc\" ]", "complexity": {"has_and": false, "has_or": false, "has_negation": false, "has_file_test": false, "has_string_test": true, "has_numeric_test": false}, "variables_used": ["grub_platform"], "operators": ["="]}, {"type": "if", "condition": "vt_check_password \"${vt_chosen_path}\"", "complexity": {"has_and": false, "has_or": false, "has_negation": false, "has_file_test": false, "has_string_test": false, "has_numeric_test": false}, "variables_used": ["vt_chosen_path"], "operators": []}, {"type": "if", "condition": "[ \"$grub_platform\" = \"pc\" ]", "complexity": {"has_and": false, "has_or": false, "has_negation": false, "has_file_test": false, "has_string_test": true, "has_numeric_test": false}, "variables_used": ["grub_platform"], "operators": ["="]}, {"type": "if", "condition": "[ \"$grub_platform\" = \"pc\" ]", "complexity": {"has_and": false, "has_or": false, "has_negation": false, "has_file_test": false, "has_string_test": true, "has_numeric_test": false}, "variables_used": ["grub_platform"], "operators": ["="]}, {"type": "if", "condition": "[ \"$grub_cpu\" = \"i386\" ]", "complexity": {"has_and": false, "has_or": false, "has_negation": false, "has_file_test": false, "has_string_test": true, "has_numeric_test": false}, "variables_used": ["grub_cpu"], "operators": ["="]}, {"type": "if", "condition": "[ \"$grub_cpu\" = \"arm64\" ]", "complexity": {"has_and": false, "has_or": false, "has_negation": false, "has_file_test": false, "has_string_test": true, "has_numeric_test": false}, "variables_used": ["grub_cpu"], "operators": ["="]}, {"type": "if", "condition": "[ \"$grub_cpu\" = \"mips64el\" ]", "complexity": {"has_and": false, "has_or": false, "has_negation": false, "has_file_test": false, "has_string_test": true, "has_numeric_test": false}, "variables_used": ["grub_cpu"], "operators": ["="]}, {"type": "if", "condition": "[ \"$vtoy_dev\" = \"tftp\" ]", "complexity": {"has_and": false, "has_or": false, "has_negation": false, "has_file_test": false, "has_string_test": true, "has_numeric_test": false}, "variables_used": ["vtoy_dev"], "operators": ["="]}, {"type": "if", "condition": "[ -f (hd$vtid,2)/ventoy/ventoy.cpio ]", "complexity": {"has_and": false, "has_or": false, "has_negation": false, "has_file_test": true, "has_string_test": false, "has_numeric_test": false}, "variables_used": ["vtid"], "operators": ["-f"]}, {"type": "if", "condition": "[ -n \"$vtoy_efi_part\" ]", "complexity": {"has_and": false, "has_or": false, "has_negation": false, "has_file_test": false, "has_string_test": false, "has_numeric_test": true}, "variables_used": ["vtoy_efi_part"], "operators": ["-n", "-n"]}, {"type": "if", "condition": "[ -f $vtoy_efi_part/ventoy/ventoy.json ]", "complexity": {"has_and": false, "has_or": false, "has_negation": false, "has_file_test": true, "has_string_test": false, "has_numeric_test": false}, "variables_used": ["vtoy_efi_part"], "operators": ["-f"]}, {"type": "if", "condition": "[ \"$prefix\" = \"(ventoydisk)/grub\" ]", "complexity": {"has_and": false, "has_or": false, "has_negation": false, "has_file_test": false, "has_string_test": true, "has_numeric_test": false}, "variables_used": ["prefix"], "operators": ["="]}, {"type": "if", "condition": "[ -f $vtoy_efi_part/ventoy/ventoy.json ]", "complexity": {"has_and": false, "has_or": false, "has_negation": false, "has_file_test": true, "has_string_test": false, "has_numeric_test": false}, "variables_used": ["vtoy_efi_part"], "operators": ["-f"]}, {"type": "if", "condition": "[ -n \"$VTOY_MENU_LANGUAGE\" ]", "complexity": {"has_and": false, "has_or": false, "has_negation": false, "has_file_test": false, "has_string_test": false, "has_numeric_test": true}, "variables_used": ["VTOY_MENU_LANGUAGE"], "operators": ["-n", "-n"]}, {"type": "if", "condition": "[ -n \"$VTOY_MENU_TIMEOUT\" ]", "complexity": {"has_and": false, "has_or": false, "has_negation": false, "has_file_test": false, "has_string_test": false, "has_numeric_test": true}, "variables_used": ["VTOY_MENU_TIMEOUT"], "operators": ["-n", "-n"]}, {"type": "if", "condition": "[ -f $vtoy_efi_part/ventoy/ventoy_wimboot.img ]", "complexity": {"has_and": false, "has_or": false, "has_negation": false, "has_file_test": true, "has_string_test": false, "has_numeric_test": false}, "variables_used": ["vtoy_efi_part"], "operators": ["-f"]}, {"type": "if", "condition": "[ -f $vtoy_efi_part/ventoy/ventoy_wimboot.img ]", "complexity": {"has_and": false, "has_or": false, "has_negation": false, "has_file_test": true, "has_string_test": false, "has_numeric_test": false}, "variables_used": ["vtoy_efi_part"], "operators": ["-f"]}, {"type": "if", "condition": "[ -f $vtoy_efi_part/ventoy/ventoy_vhdboot.img ]", "complexity": {"has_and": false, "has_or": false, "has_negation": false, "has_file_test": true, "has_string_test": false, "has_numeric_test": false}, "variables_used": ["vtoy_efi_part"], "operators": ["-f"]}, {"type": "if", "condition": "[ -f $vtoy_efi_part/ventoy/ventoy_vhdboot.img ]", "complexity": {"has_and": false, "has_or": false, "has_negation": false, "has_file_test": true, "has_string_test": false, "has_numeric_test": false}, "variables_used": ["vtoy_efi_part"], "operators": ["-f"]}, {"type": "if", "condition": "[ $VTOY_DEFAULT_MENU_MODE -eq 0 ]", "complexity": {"has_and": false, "has_or": false, "has_negation": false, "has_file_test": true, "has_string_test": false, "has_numeric_test": true}, "variables_used": ["VTOY_DEFAULT_MENU_MODE"], "operators": ["-e", "-e"]}, {"type": "if", "condition": "[ -n \"$vtoy_gfxmode\" ]", "complexity": {"has_and": false, "has_or": false, "has_negation": false, "has_file_test": false, "has_string_test": false, "has_numeric_test": true}, "variables_used": ["vtoy_gfxmode"], "operators": ["-n", "-n"]}, {"type": "if", "condition": "[ \"$vtoy_display_mode\" = \"CLI\" ]", "complexity": {"has_and": false, "has_or": false, "has_negation": false, "has_file_test": false, "has_string_test": true, "has_numeric_test": false}, "variables_used": ["vtoy_display_mode"], "operators": ["="]}, {"type": "if", "condition": "[ \"$vtoy_display_mode\" = \"serial\" ]", "complexity": {"has_and": false, "has_or": false, "has_negation": false, "has_file_test": false, "has_string_test": true, "has_numeric_test": false}, "variables_used": ["vtoy_display_mode"], "operators": ["="]}, {"type": "if", "condition": "[ -n \"$vtoy_serial_param\" ]", "complexity": {"has_and": false, "has_or": false, "has_negation": false, "has_file_test": false, "has_string_test": false, "has_numeric_test": true}, "variables_used": ["vtoy_serial_param"], "operators": ["-n", "-n"]}, {"type": "if", "condition": "[ \"$vtoy_display_mode\" = \"serial_console\" ]", "complexity": {"has_and": false, "has_or": false, "has_negation": false, "has_file_test": false, "has_string_test": true, "has_numeric_test": false}, "variables_used": ["vtoy_display_mode"], "operators": ["="]}, {"type": "if", "condition": "[ -n \"$vtoy_serial_param\" ]", "complexity": {"has_and": false, "has_or": false, "has_negation": false, "has_file_test": false, "has_string_test": false, "has_numeric_test": true}, "variables_used": ["vtoy_serial_param"], "operators": ["-n", "-n"]}, {"type": "if", "condition": "[ \"$vtoy_gfxmode\" = \"max\" ]", "complexity": {"has_and": false, "has_or": false, "has_negation": false, "has_file_test": false, "has_string_test": true, "has_numeric_test": false}, "variables_used": ["vtoy_gfxmode"], "operators": ["="]}, {"type": "if", "condition": "[ \"$vtoy_res_fit\" = \"1\" ]", "complexity": {"has_and": false, "has_or": false, "has_negation": false, "has_file_test": false, "has_string_test": true, "has_numeric_test": false}, "variables_used": ["vtoy_res_fit"], "operators": ["="]}, {"type": "if", "condition": "[ -n \"$vtoy_theme\" ]", "complexity": {"has_and": false, "has_or": false, "has_negation": false, "has_file_test": false, "has_string_test": false, "has_numeric_test": true}, "variables_used": ["vtoy_theme"], "operators": ["-n", "-n"]}, {"type": "if", "condition": "[ \"$grub_platform\" = \"efi\" ]", "complexity": {"has_and": false, "has_or": false, "has_negation": false, "has_file_test": false, "has_string_test": true, "has_numeric_test": false}, "variables_used": ["grub_platform"], "operators": ["="]}, {"type": "if", "condition": "[ -n \"$VTOY_DEFAULT_KBD_LAYOUT\" ]", "complexity": {"has_and": false, "has_or": false, "has_negation": false, "has_file_test": false, "has_string_test": false, "has_numeric_test": true}, "variables_used": ["VTOY_DEFAULT_KBD_LAYOUT"], "operators": ["-n", "-n"]}, {"type": "if", "condition": "[ -n \"$VTOY_PLUGIN_PATH_CASE_MISMATCH\" ]", "complexity": {"has_and": false, "has_or": false, "has_negation": false, "has_file_test": false, "has_string_test": false, "has_numeric_test": true}, "variables_used": ["VTOY_PLUGIN_PATH_CASE_MISMATCH"], "operators": ["-n", "-n"]}, {"type": "if", "condition": "[ $VTOY_DEFAULT_MENU_MODE -eq 0 ]", "complexity": {"has_and": false, "has_or": false, "has_negation": false, "has_file_test": true, "has_string_test": false, "has_numeric_test": true}, "variables_used": ["VTOY_DEFAULT_MENU_MODE"], "operators": ["-e", "-e"]}, {"type": "if", "condition": "[ -n \"$VTOY_DEFAULT_IMAGE\" ]", "complexity": {"has_and": false, "has_or": false, "has_negation": false, "has_file_test": false, "has_string_test": false, "has_numeric_test": true}, "variables_used": ["VTOY_DEFAULT_IMAGE"], "operators": ["-n", "-n"]}, {"type": "if", "condition": "regexp --set 1:vtHotkey --set 2:vtDefault \"(F[2-9])>(.*)\" \"$VTOY_DEFAULT_IMAGE\"", "complexity": {"has_and": false, "has_or": false, "has_negation": false, "has_file_test": false, "has_string_test": false, "has_numeric_test": false}, "variables_used": ["VTOY_DEFAULT_IMAGE"], "operators": []}, {"type": "if", "condition": "[ -z \"$VTOY_MENU_TIMEOUT\" ]", "complexity": {"has_and": false, "has_or": false, "has_negation": false, "has_file_test": false, "has_string_test": false, "has_numeric_test": false}, "variables_used": ["VTOY_MENU_TIMEOUT"], "operators": ["-z"]}, {"type": "if", "condition": "[ \"$vtHotkey\" = \"F2\" ]", "complexity": {"has_and": false, "has_or": false, "has_negation": false, "has_file_test": false, "has_string_test": true, "has_numeric_test": false}, "variables_used": ["vtHotkey"], "operators": ["="]}, {"type": "if", "condition": "[ \"$vtHotkey\" = \"F4\" ]", "complexity": {"has_and": false, "has_or": false, "has_negation": false, "has_file_test": false, "has_string_test": true, "has_numeric_test": false}, "variables_used": ["vtHotkey"], "operators": ["="]}, {"type": "if", "condition": "[ \"$vtHotkey\" = \"F5\" ]", "complexity": {"has_and": false, "has_or": false, "has_negation": false, "has_file_test": false, "has_string_test": true, "has_numeric_test": false}, "variables_used": ["vtHotkey"], "operators": ["="]}, {"type": "if", "condition": "[ \"$vtHotkey\" = \"F6\" ]", "complexity": {"has_and": false, "has_or": false, "has_negation": false, "has_file_test": false, "has_string_test": true, "has_numeric_test": false}, "variables_used": ["vtHotkey"], "operators": ["="]}], "security_mechanisms": {"compatibility_checks": ["vt_check_compatible", "vt_check_compatible", "vt_check_compatible", "vt_check_compatible", "vt_check_compatible", "vt_check_compatible", "vt_check_compatible", "vt_check_compatible", "ventoy_compatible", "ventoy_compatible", "ventoy_compatible", "ventoy_compatible", "ventoy_compatible", "ventoy_compatible", "ventoy_compatible", "ventoy_compatible", "ventoy_compatible", "ventoy_compatible", "ventoy_compatible", "ventoy_compatible", "ventoy_compatible", "ventoy_compatible", "ventoy_compatible"], "file_verification": [], "signature_validation": [], "secure_boot_features": ["secureboot"], "access_controls": []}}, "grub/ventoy_grub.cfg": {"config_path": "grub/ventoy_grub.cfg", "basic_stats": {"total_lines": 20, "non_empty_lines": 16, "comment_lines": 0, "function_count": 0}, "functions": {}, "variables": {"timeout": {"assignments": ["2"], "references": [], "type": "unknown", "reference_count": 0, "assignment_count": 1}, "default": {"assignments": ["\"Vtd\""], "references": [], "type": "unknown", "reference_count": 0, "assignment_count": 1}}, "conditionals": [], "security_mechanisms": {"compatibility_checks": [], "file_verification": [], "signature_validation": [], "secure_boot_features": [], "access_controls": []}}}