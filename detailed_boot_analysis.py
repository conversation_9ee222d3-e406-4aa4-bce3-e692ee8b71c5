#!/usr/bin/env python3
import struct
import re

def detailed_boot_efi_analysis():
    filename = r"D:\新建文件夹 (2)\新建文件夹\EFI\BOOT\BOOT.EFI"
    
    with open(filename, 'rb') as f:
        data = f.read()
    
    print("=== BOOT.EFI 详细二进制分析 ===")
    print(f"文件大小: {len(data)} 字节\n")
    
    # 1. PE文件头详细分析
    print("1. PE文件头分析:")
    dos_header = struct.unpack('<H', data[0:2])[0]
    print(f"  DOS签名: 0x{dos_header:04X}")
    
    pe_offset = struct.unpack('<I', data[0x3C:0x40])[0]
    print(f"  PE头偏移: 0x{pe_offset:04X}")
    
    if pe_offset < len(data) - 24:
        pe_signature = data[pe_offset:pe_offset+4]
        print(f"  PE签名: {pe_signature}")
        
        machine = struct.unpack('<H', data[pe_offset+4:pe_offset+6])[0]
        sections = struct.unpack('<H', data[pe_offset+6:pe_offset+8])[0]
        timestamp = struct.unpack('<I', data[pe_offset+8:pe_offset+12])[0]
        
        print(f"  机器类型: 0x{machine:04X}")
        print(f"  节数量: {sections}")
        print(f"  时间戳: 0x{timestamp:08X}")
    
    # 2. 搜索所有ASCII字符串
    print("\n2. ASCII字符串 (长度>=6):")
    strings = []
    current = ""
    start_pos = 0
    
    for i, byte in enumerate(data):
        if 32 <= byte <= 126:
            if not current:
                start_pos = i
            current += chr(byte)
        else:
            if len(current) >= 6:
                strings.append((start_pos, current))
            current = ""
    
    if len(current) >= 6:
        strings.append((len(data) - len(current), current))
    
    print(f"  找到 {len(strings)} 个字符串:")
    for offset, string in strings[:50]:
        print(f"    0x{offset:08X}: {string}")
    
    # 3. 搜索特定模式
    print("\n3. 验证相关模式搜索:")
    patterns = {
        b"MBR": "MBR相关",
        b"check": "检查功能", 
        b"verify": "验证功能",
        b"error": "错误处理",
        b"device": "设备相关",
        b"DmarInsert": "VTD功能",
        b"DMAR": "DMAR表",
        b"ACPI": "ACPI表",
        b"General_UDisk": "硬件标识"
    }
    
    for pattern, desc in patterns.items():
        positions = []
        start = 0
        while True:
            pos = data.find(pattern, start)
            if pos == -1:
                break
            positions.append(pos)
            start = pos + 1
        
        if positions:
            print(f"  {desc} ('{pattern.decode('ascii', errors='ignore')}'): {len(positions)} 次")
            for pos in positions[:3]:
                context = data[max(0, pos-20):pos+len(pattern)+20]
                ascii_context = ''.join(chr(b) if 32 <= b <= 126 else '.' for b in context)
                print(f"    0x{pos:08X}: {ascii_context}")
    
    # 4. 搜索call和jmp指令
    print("\n4. 函数调用分析:")
    calls = []
    
    for i in range(len(data) - 5):
        if data[i] == 0xE8:  # call near
            offset = struct.unpack('<i', data[i+1:i+5])[0]
            target = (i + 5 + offset) & 0xFFFFFFFF
            calls.append(('call', i, target))
        elif data[i] == 0xE9:  # jmp near
            offset = struct.unpack('<i', data[i+1:i+5])[0]
            target = (i + 5 + offset) & 0xFFFFFFFF
            calls.append(('jmp', i, target))
    
    print(f"  找到 {len(calls)} 个call/jmp指令")
    print("  前20个:")
    for instr, addr, target in calls[:20]:
        print(f"    0x{addr:08X}: {instr} -> 0x{target:08X}")
    
    # 5. 搜索错误代码12
    print("\n5. 错误代码12分析:")
    error_patterns = [
        (b'\xB8\x0C\x00\x00\x00', 'mov eax, 12'),
        (b'\x48\xC7\xC0\x0C\x00\x00\x00', 'mov rax, 12'),
        (b'\x83\xF8\x0C', 'cmp eax, 12'),
        (b'\x48\x83\xF8\x0C', 'cmp rax, 12')
    ]
    
    for pattern, desc in error_patterns:
        positions = []
        start = 0
        while True:
            pos = data.find(pattern, start)
            if pos == -1:
                break
            positions.append(pos)
            start = pos + 1
        
        if positions:
            print(f"  {desc}: {len(positions)} 次")
            for pos in positions:
                context = data[max(0, pos-15):pos+len(pattern)+15]
                hex_str = ' '.join(f'{b:02x}' for b in context)
                print(f"    0x{pos:08X}: {hex_str}")
    
    print("\n=== 分析完成 ===")

if __name__ == "__main__":
    detailed_boot_efi_analysis()
