#!/usr/bin/env python3
"""
详细的EFI文件结构分析工具
Detailed EFI File Structure Analysis Tool
"""

import os
import sys
import struct
import hashlib
import json
from pathlib import Path
from typing import Dict, List, Any, Optional, Tuple

class DetailedEFIAnalyzer:
    """详细的EFI文件分析器"""
    
    def __init__(self, file_path: str):
        self.file_path = file_path
        self.file_data = None
        self.analysis_results = {}
        
    def load_file(self) -> bool:
        """加载EFI文件"""
        try:
            with open(self.file_path, 'rb') as f:
                self.file_data = f.read()
            return True
        except Exception as e:
            print(f"Error loading file {self.file_path}: {e}")
            return False
    
    def analyze_file_header(self) -> Dict[str, Any]:
        """分析文件头信息"""
        if not self.file_data or len(self.file_data) < 64:
            return {"error": "File too small or not loaded"}
        
        header_info = {
            "file_size": len(self.file_data),
            "file_hash_md5": hashlib.md5(self.file_data).hexdigest(),
            "file_hash_sha1": hashlib.sha1(self.file_data).hexdigest(),
            "file_hash_sha256": hashlib.sha256(self.file_data).hexdigest(),
        }
        
        # DOS头分析
        dos_signature = struct.unpack('<H', self.file_data[:2])[0]
        header_info["dos_signature"] = f"0x{dos_signature:04X}"
        header_info["dos_signature_valid"] = dos_signature == 0x5A4D
        
        if dos_signature == 0x5A4D:
            pe_offset = struct.unpack('<L', self.file_data[60:64])[0]
            header_info["pe_offset"] = f"0x{pe_offset:08X}"
            
            # PE签名检查
            if pe_offset + 4 <= len(self.file_data):
                pe_signature = struct.unpack('<L', self.file_data[pe_offset:pe_offset+4])[0]
                header_info["pe_signature"] = f"0x{pe_signature:08X}"
                header_info["pe_signature_valid"] = pe_signature == 0x00004550
        
        return header_info
    
    def analyze_pe_coff_header(self) -> Dict[str, Any]:
        """分析PE COFF头"""
        if not self.file_data:
            return {}
        
        try:
            pe_offset = struct.unpack('<L', self.file_data[60:64])[0]
            coff_offset = pe_offset + 4
            
            if coff_offset + 20 > len(self.file_data):
                return {"error": "COFF header extends beyond file"}
            
            # 读取COFF头的前6个字段
            machine, num_sections, timestamp, ptr_to_symtab, num_symbols, opt_hdr_size = \
                struct.unpack('<HHLLHH', self.file_data[coff_offset:coff_offset+20])
            
            coff_info = {
                "machine_type": f"0x{machine:04X}",
                "machine_description": self._get_machine_description(machine),
                "number_of_sections": num_sections,
                "timestamp": timestamp,
                "timestamp_readable": self._timestamp_to_readable(timestamp),
                "pointer_to_symbol_table": f"0x{ptr_to_symtab:08X}",
                "number_of_symbols": num_symbols,
                "optional_header_size": opt_hdr_size
            }
            
            # 读取特征标志
            if coff_offset + 22 <= len(self.file_data):
                characteristics = struct.unpack('<H', self.file_data[coff_offset+18:coff_offset+20])[0]
                coff_info["characteristics"] = f"0x{characteristics:04X}"
                coff_info["characteristics_flags"] = self._parse_characteristics(characteristics)
            
            return coff_info
            
        except struct.error as e:
            return {"error": f"COFF header parsing error: {e}"}
    
    def analyze_optional_header(self) -> Dict[str, Any]:
        """分析可选头"""
        if not self.file_data:
            return {}
        
        try:
            pe_offset = struct.unpack('<L', self.file_data[60:64])[0]
            opt_hdr_offset = pe_offset + 24  # PE signature (4) + COFF header (20)
            
            if opt_hdr_offset + 2 > len(self.file_data):
                return {"error": "Optional header not found"}
            
            magic = struct.unpack('<H', self.file_data[opt_hdr_offset:opt_hdr_offset+2])[0]
            
            opt_info = {
                "magic": f"0x{magic:04X}",
                "pe_type": "PE32+" if magic == 0x20b else "PE32" if magic == 0x10b else "Unknown"
            }
            
            if magic == 0x20b:  # PE32+
                if opt_hdr_offset + 24 <= len(self.file_data):
                    major_linker, minor_linker, size_of_code, size_of_init_data, size_of_uninit_data, entry_point = \
                        struct.unpack('<BBLLLL', self.file_data[opt_hdr_offset+2:opt_hdr_offset+18])
                    
                    opt_info.update({
                        "linker_version": f"{major_linker}.{minor_linker}",
                        "size_of_code": size_of_code,
                        "size_of_initialized_data": size_of_init_data,
                        "size_of_uninitialized_data": size_of_uninit_data,
                        "entry_point": f"0x{entry_point:08X}"
                    })
                
                # 读取更多PE32+字段
                if opt_hdr_offset + 88 <= len(self.file_data):
                    image_base = struct.unpack('<Q', self.file_data[opt_hdr_offset+24:opt_hdr_offset+32])[0]
                    section_alignment = struct.unpack('<L', self.file_data[opt_hdr_offset+32:opt_hdr_offset+36])[0]
                    file_alignment = struct.unpack('<L', self.file_data[opt_hdr_offset+36:opt_hdr_offset+40])[0]
                    
                    opt_info.update({
                        "image_base": f"0x{image_base:016X}",
                        "section_alignment": section_alignment,
                        "file_alignment": file_alignment
                    })
                
                # 读取子系统信息
                if opt_hdr_offset + 68 <= len(self.file_data):
                    subsystem = struct.unpack('<H', self.file_data[opt_hdr_offset+68:opt_hdr_offset+70])[0]
                    opt_info["subsystem"] = subsystem
                    opt_info["subsystem_description"] = self._get_subsystem_description(subsystem)
            
            return opt_info
            
        except struct.error as e:
            return {"error": f"Optional header parsing error: {e}"}
    
    def analyze_sections(self) -> List[Dict[str, Any]]:
        """分析节表"""
        if not self.file_data:
            return []
        
        try:
            pe_offset = struct.unpack('<L', self.file_data[60:64])[0]
            coff_offset = pe_offset + 4
            
            # 获取节数量和可选头大小
            num_sections = struct.unpack('<H', self.file_data[coff_offset+2:coff_offset+4])[0]
            opt_hdr_size = struct.unpack('<H', self.file_data[coff_offset+16:coff_offset+18])[0]
            
            section_table_offset = pe_offset + 24 + opt_hdr_size
            sections = []
            
            for i in range(min(num_sections, 20)):  # 限制最多20个节
                section_offset = section_table_offset + i * 40
                
                if section_offset + 40 > len(self.file_data):
                    break
                
                # 读取节头
                section_data = self.file_data[section_offset:section_offset+40]
                name = section_data[:8].rstrip(b'\x00').decode('ascii', errors='ignore')
                
                virtual_size, virtual_address, raw_size, raw_address, ptr_relocs, ptr_line_nums, num_relocs, num_line_nums, characteristics = \
                    struct.unpack('<LLLLLLLHH', section_data[8:36])
                
                section_info = {
                    "name": name,
                    "virtual_size": virtual_size,
                    "virtual_address": f"0x{virtual_address:08X}",
                    "raw_size": raw_size,
                    "raw_address": f"0x{raw_address:08X}",
                    "pointer_to_relocations": f"0x{ptr_relocs:08X}",
                    "pointer_to_line_numbers": f"0x{ptr_line_nums:08X}",
                    "number_of_relocations": num_relocs,
                    "number_of_line_numbers": num_line_nums,
                    "characteristics": f"0x{characteristics:08X}",
                    "characteristics_flags": self._parse_section_characteristics(characteristics)
                }
                
                # 计算熵值（用于检测加密/压缩）
                if raw_address < len(self.file_data) and raw_size > 0:
                    section_content = self.file_data[raw_address:raw_address+min(raw_size, len(self.file_data)-raw_address)]
                    section_info["entropy"] = self._calculate_entropy(section_content)
                    section_info["hash_sha256"] = hashlib.sha256(section_content).hexdigest()
                
                sections.append(section_info)
            
            return sections
            
        except (struct.error, UnicodeDecodeError) as e:
            return [{"error": f"Section parsing error: {e}"}]
    
    def _get_machine_description(self, machine: int) -> str:
        """获取机器类型描述"""
        machine_types = {
            0x014c: "Intel 386",
            0x0162: "MIPS R3000",
            0x0166: "MIPS R4000",
            0x0168: "MIPS R10000",
            0x0169: "MIPS WCE v2",
            0x0184: "Alpha AXP",
            0x01a2: "Hitachi SH3",
            0x01a3: "Hitachi SH3 DSP",
            0x01a6: "Hitachi SH4",
            0x01a8: "Hitachi SH5",
            0x01c0: "ARM little endian",
            0x01c2: "Thumb",
            0x01c4: "ARM Thumb-2 little endian",
            0x01d3: "Matsushita AM33",
            0x01f0: "PowerPC little endian",
            0x01f1: "PowerPC with floating point support",
            0x0200: "Intel IA64",
            0x0266: "MIPS16",
            0x0284: "Alpha AXP 64-bit",
            0x0366: "MIPS with FPU",
            0x0466: "MIPS16 with FPU",
            0x0520: "Infineon TriCore",
            0x0cef: "CEF",
            0x0ebc: "EFI Byte Code",
            0x8664: "AMD64 (K8)",
            0x9041: "Mitsubishi M32R little endian",
            0xaa64: "ARM64 little endian",
            0xc0ee: "clr pure MSIL"
        }
        return machine_types.get(machine, f"Unknown (0x{machine:04X})")
    
    def _get_subsystem_description(self, subsystem: int) -> str:
        """获取子系统描述"""
        subsystems = {
            0: "Unknown",
            1: "Native",
            2: "Windows GUI",
            3: "Windows CUI",
            5: "OS/2 CUI",
            7: "POSIX CUI",
            8: "Native Win9x driver",
            9: "Windows CE GUI",
            10: "EFI application",
            11: "EFI boot service driver",
            12: "EFI runtime driver",
            13: "EFI ROM",
            14: "XBOX",
            16: "Windows boot application"
        }
        return subsystems.get(subsystem, f"Unknown ({subsystem})")
    
    def _parse_characteristics(self, characteristics: int) -> List[str]:
        """解析特征标志"""
        flags = []
        flag_meanings = {
            0x0001: "RELOCS_STRIPPED",
            0x0002: "EXECUTABLE_IMAGE",
            0x0004: "LINE_NUMS_STRIPPED",
            0x0008: "LOCAL_SYMS_STRIPPED",
            0x0010: "AGGR_WS_TRIM",
            0x0020: "LARGE_ADDRESS_AWARE",
            0x0080: "BYTES_REVERSED_LO",
            0x0100: "32BIT_MACHINE",
            0x0200: "DEBUG_STRIPPED",
            0x0400: "REMOVABLE_RUN_FROM_SWAP",
            0x0800: "NET_RUN_FROM_SWAP",
            0x1000: "SYSTEM",
            0x2000: "DLL",
            0x4000: "UP_SYSTEM_ONLY",
            0x8000: "BYTES_REVERSED_HI"
        }
        
        for flag, meaning in flag_meanings.items():
            if characteristics & flag:
                flags.append(meaning)
        
        return flags
    
    def _parse_section_characteristics(self, characteristics: int) -> List[str]:
        """解析节特征标志"""
        flags = []
        flag_meanings = {
            0x00000020: "CODE",
            0x00000040: "INITIALIZED_DATA",
            0x00000080: "UNINITIALIZED_DATA",
            0x00000200: "LNK_INFO",
            0x00000800: "LNK_REMOVE",
            0x00001000: "LNK_COMDAT",
            0x00008000: "GPREL",
            0x00020000: "MEM_PURGEABLE",
            0x00040000: "MEM_16BIT",
            0x00080000: "MEM_LOCKED",
            0x00100000: "MEM_PRELOAD",
            0x01000000: "LNK_NRELOC_OVFL",
            0x02000000: "MEM_DISCARDABLE",
            0x04000000: "MEM_NOT_CACHED",
            0x08000000: "MEM_NOT_PAGED",
            0x10000000: "MEM_SHARED",
            0x20000000: "MEM_EXECUTE",
            0x40000000: "MEM_READ",
            0x80000000: "MEM_WRITE"
        }
        
        for flag, meaning in flag_meanings.items():
            if characteristics & flag:
                flags.append(meaning)
        
        return flags
    
    def _timestamp_to_readable(self, timestamp: int) -> str:
        """将时间戳转换为可读格式"""
        try:
            import datetime
            return datetime.datetime.fromtimestamp(timestamp).strftime('%Y-%m-%d %H:%M:%S')
        except:
            return f"Invalid timestamp: {timestamp}"
    
    def _calculate_entropy(self, data: bytes) -> float:
        """计算数据熵值"""
        if not data:
            return 0.0
        
        # 计算字节频率
        byte_counts = [0] * 256
        for byte in data:
            byte_counts[byte] += 1
        
        # 计算熵
        entropy = 0.0
        data_len = len(data)
        
        for count in byte_counts:
            if count > 0:
                probability = count / data_len
                entropy -= probability * (probability.bit_length() - 1)
        
        return entropy
    
    def analyze_all(self) -> Dict[str, Any]:
        """执行完整分析"""
        if not self.load_file():
            return {"error": "Failed to load file"}
        
        results = {
            "file_path": self.file_path,
            "header_info": self.analyze_file_header(),
            "coff_header": self.analyze_pe_coff_header(),
            "optional_header": self.analyze_optional_header(),
            "sections": self.analyze_sections()
        }
        
        return results

def main():
    """主函数"""
    print("=== 详细EFI文件结构分析工具 ===")
    print("=== Detailed EFI File Structure Analysis Tool ===\n")
    
    efi_files = [
        "EFI/BOOT/BOOTX64.EFI",
        "EFI/BOOT/grub.efi",
        "EFI/BOOT/BOOTAA64.EFI",
        "EFI/BOOT/BOOTIA32.EFI"
    ]
    
    all_results = {}
    
    for efi_file in efi_files:
        if os.path.exists(efi_file):
            print(f"详细分析EFI文件: {efi_file}")
            analyzer = DetailedEFIAnalyzer(efi_file)
            all_results[efi_file] = analyzer.analyze_all()
    
    # 保存结果
    with open('detailed_efi_analysis_results.json', 'w', encoding='utf-8') as f:
        json.dump(all_results, f, indent=2, ensure_ascii=False)
    
    print(f"\n详细分析完成！结果已保存到 detailed_efi_analysis_results.json")
    return all_results

if __name__ == "__main__":
    main()
