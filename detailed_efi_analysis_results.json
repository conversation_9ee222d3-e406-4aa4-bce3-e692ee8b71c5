{"EFI/BOOT/BOOTX64.EFI": {"file_path": "EFI/BOOT/BOOTX64.EFI", "header_info": {"file_size": 965672, "file_hash_md5": "a47b8bb55bd2bbd277c59009aa477879", "file_hash_sha1": "f33701d80f1614ad1c7e0a114f298196660c760f", "file_hash_sha256": "a60d256c802849a0a5e23fe5298ddcf7f78445cc71f519b64573dcb61af0e6ff", "dos_signature": "0x5A4D", "dos_signature_valid": true, "pe_offset": "0x00000080", "pe_signature": "0x00004550", "pe_signature_valid": true}, "coff_header": {"error": "COFF header parsing error: unpack requires a buffer of 16 bytes"}, "optional_header": {"error": "Optional header parsing error: unpack requires a buffer of 18 bytes"}, "sections": [{"error": "Section parsing error: unpack requires a buffer of 32 bytes"}]}, "EFI/BOOT/grub.efi": {"file_path": "EFI/BOOT/grub.efi", "header_info": {"file_size": 64120, "file_hash_md5": "f5787f65638d6e2688bf5c651fcd33d0", "file_hash_sha1": "19e997ac4b8b848a357520e8a38b0ec7cb5b0934", "file_hash_sha256": "290fad7c528fbc694c4963da6c0ec74543fba1e425c8f3e77c3c4218ff0d7bb3", "dos_signature": "0x5A4D", "dos_signature_valid": true, "pe_offset": "0x00000080", "pe_signature": "0x00004550", "pe_signature_valid": true}, "coff_header": {"error": "COFF header parsing error: unpack requires a buffer of 16 bytes"}, "optional_header": {"error": "Optional header parsing error: unpack requires a buffer of 18 bytes"}, "sections": [{"error": "Section parsing error: unpack requires a buffer of 32 bytes"}]}, "EFI/BOOT/BOOTAA64.EFI": {"file_path": "EFI/BOOT/BOOTAA64.EFI", "header_info": {"file_size": 2220032, "file_hash_md5": "c93db17afcd179ed7a852bca7323bbe2", "file_hash_sha1": "218612848af8361e703fd9b057a5f958514a74db", "file_hash_sha256": "bf14944a518acb3f2c3abcf0aa5301a0fa65e98590f1b87714cf05f5de6b05dd", "dos_signature": "0x5A4D", "dos_signature_valid": true, "pe_offset": "0x00000080", "pe_signature": "0x00004550", "pe_signature_valid": true}, "coff_header": {"error": "COFF header parsing error: unpack requires a buffer of 16 bytes"}, "optional_header": {"error": "Optional header parsing error: unpack requires a buffer of 18 bytes"}, "sections": [{"error": "Section parsing error: unpack requires a buffer of 32 bytes"}]}, "EFI/BOOT/BOOTIA32.EFI": {"file_path": "EFI/BOOT/BOOTIA32.EFI", "header_info": {"file_size": 742064, "file_hash_md5": "b6410a7309a1aa9a05df02ea1954c02d", "file_hash_sha1": "a9d468686be854bc40bb964c623e2368b1ce1d2e", "file_hash_sha256": "f6f6b9369bcdac99fbc24c457d9010479f47af62b00fb19f1dfd865bfb75d369", "dos_signature": "0x5A4D", "dos_signature_valid": true, "pe_offset": "0x00000080", "pe_signature": "0x00004550", "pe_signature_valid": true}, "coff_header": {"error": "COFF header parsing error: unpack requires a buffer of 16 bytes"}, "optional_header": {"magic": "0x010B", "pe_type": "PE32"}, "sections": [{"error": "Section parsing error: unpack requires a buffer of 32 bytes"}]}}