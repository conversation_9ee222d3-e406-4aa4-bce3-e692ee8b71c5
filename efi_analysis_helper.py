#!/usr/bin/env python3
"""
EFI分析辅助工具
EFI Analysis Helper Tool
配合Ghidra使用的辅助分析工具
"""

import os
import sys
import struct
import re
import json
from pathlib import Path
from typing import Dict, List, Any, Optional, Tuple

class EFIAnalysisHelper:
    """EFI分析辅助工具"""
    
    def __init__(self, efi_file_path: str):
        self.efi_file_path = efi_file_path
        self.file_data = None
        self.load_file()
    
    def load_file(self) -> bool:
        """加载EFI文件"""
        try:
            with open(self.efi_file_path, 'rb') as f:
                self.file_data = f.read()
            return True
        except Exception as e:
            print(f"Error loading file: {e}")
            return False
    
    def extract_strings_detailed(self) -> Dict[str, List[Dict[str, Any]]]:
        """提取详细的字符串信息"""
        if not self.file_data:
            return {}
        
        strings_info = {
            "efi_protocols": [],
            "file_paths": [],
            "error_messages": [],
            "debug_strings": [],
            "function_names": [],
            "registry_keys": [],
            "urls": [],
            "other_strings": []
        }
        
        # ASCII字符串
        ascii_pattern = rb'[\x20-\x7E]{4,}'
        for match in re.finditer(ascii_pattern, self.file_data):
            string = match.group().decode('ascii', errors='ignore')
            offset = match.start()
            
            string_info = {
                "string": string,
                "offset": f"0x{offset:08x}",
                "length": len(string),
                "type": "ASCII"
            }
            
            # 分类字符串
            string_lower = string.lower()
            
            if any(keyword in string_lower for keyword in ['protocol', 'guid', 'interface', 'service']):
                strings_info["efi_protocols"].append(string_info)
            elif any(keyword in string_lower for keyword in ['\\', '/', '.efi', '.exe', '.dll', '.sys']):
                strings_info["file_paths"].append(string_info)
            elif any(keyword in string_lower for keyword in ['error', 'fail', 'invalid', 'corrupt', 'denied']):
                strings_info["error_messages"].append(string_info)
            elif any(keyword in string_lower for keyword in ['debug', 'trace', 'log', 'print', 'output']):
                strings_info["debug_strings"].append(string_info)
            elif any(keyword in string_lower for keyword in ['function', 'proc', 'call', 'invoke']):
                strings_info["function_names"].append(string_info)
            elif any(keyword in string_lower for keyword in ['hkey', 'registry', 'software', 'system']):
                strings_info["registry_keys"].append(string_info)
            elif any(keyword in string_lower for keyword in ['http', 'https', 'ftp', 'www']):
                strings_info["urls"].append(string_info)
            else:
                strings_info["other_strings"].append(string_info)
        
        # Unicode字符串
        unicode_pattern = rb'(?:[\x20-\x7E]\x00){4,}'
        for match in re.finditer(unicode_pattern, self.file_data):
            try:
                string = match.group().decode('utf-16le', errors='ignore').rstrip('\x00')
                offset = match.start()
                
                if len(string) >= 4:
                    string_info = {
                        "string": string,
                        "offset": f"0x{offset:08x}",
                        "length": len(string),
                        "type": "Unicode"
                    }
                    strings_info["other_strings"].append(string_info)
            except:
                continue
        
        # 限制每个类别的数量
        for category in strings_info:
            strings_info[category] = strings_info[category][:50]
        
        return strings_info
    
    def find_efi_guids(self) -> List[Dict[str, Any]]:
        """查找EFI GUID"""
        guids = []
        
        # GUID是16字节的结构
        for i in range(0, len(self.file_data) - 16, 4):
            try:
                # 尝试解析GUID格式
                guid_bytes = self.file_data[i:i+16]
                
                # GUID格式：{XXXXXXXX-XXXX-XXXX-XXXX-XXXXXXXXXXXX}
                data1 = struct.unpack('<L', guid_bytes[0:4])[0]
                data2 = struct.unpack('<H', guid_bytes[4:6])[0]
                data3 = struct.unpack('<H', guid_bytes[6:8])[0]
                data4 = guid_bytes[8:16]
                
                # 检查是否是有效的GUID（简单启发式）
                if data1 != 0 and data2 != 0 and data3 != 0:
                    guid_str = f"{data1:08X}-{data2:04X}-{data3:04X}-{data4[0]:02X}{data4[1]:02X}-{data4[2]:02X}{data4[3]:02X}{data4[4]:02X}{data4[5]:02X}{data4[6]:02X}{data4[7]:02X}"
                    
                    # 检查是否是已知的EFI GUID
                    known_guid = self._identify_efi_guid(guid_str)
                    
                    guids.append({
                        "offset": f"0x{i:08x}",
                        "guid": guid_str,
                        "name": known_guid,
                        "bytes": guid_bytes.hex().upper()
                    })
                    
                    if len(guids) >= 100:  # 限制数量
                        break
            except:
                continue
        
        return guids
    
    def _identify_efi_guid(self, guid_str: str) -> str:
        """识别已知的EFI GUID"""
        known_guids = {
            "09576E91-6D3F-11D2-8E39-00A0C969723B": "EFI_LOADED_IMAGE_PROTOCOL_GUID",
            "964E5B21-6459-11D2-8E39-00A0C969723B": "EFI_SIMPLE_FILE_SYSTEM_PROTOCOL_GUID",
            "964E5B22-6459-11D2-8E39-00A0C969723B": "EFI_FILE_PROTOCOL_GUID",
            "8BE4DF61-93CA-11D2-AA0D-00E098032B8C": "EFI_SYSTEM_TABLE_GUID",
            "5B1B31A1-9562-11D2-8E3F-00A0C969723B": "EFI_BOOT_SERVICES_GUID",
            "EB9D2D30-2D88-11D3-9A16-0090273FC14D": "EFI_RUNTIME_SERVICES_GUID",
            "8868E871-E4F1-11D3-BC22-0080C73C8881": "EFI_ACPI_TABLE_PROTOCOL_GUID",
            "387477C1-69C7-11D2-8E39-00A0C969723B": "EFI_SIMPLE_NETWORK_PROTOCOL_GUID",
            "A19832B9-AC25-11D3-9A2D-0090273FC14D": "EFI_SIMPLE_INPUT_PROTOCOL_GUID",
            "387477C2-69C7-11D2-8E39-00A0C969723B": "EFI_SIMPLE_TEXT_OUTPUT_PROTOCOL_GUID"
        }
        
        return known_guids.get(guid_str, "Unknown GUID")
    
    def analyze_pe_imports(self) -> List[Dict[str, Any]]:
        """分析PE导入表"""
        imports = []
        
        try:
            # 查找PE头
            pe_offset = struct.unpack('<L', self.file_data[60:64])[0]
            
            # 查找可选头
            opt_header_offset = pe_offset + 24
            magic = struct.unpack('<H', self.file_data[opt_header_offset:opt_header_offset+2])[0]
            
            if magic == 0x20b:  # PE32+
                # 导入表在数据目录的第2个条目
                import_table_rva_offset = opt_header_offset + 112
                if import_table_rva_offset + 8 <= len(self.file_data):
                    import_rva, import_size = struct.unpack('<LL', 
                        self.file_data[import_table_rva_offset:import_table_rva_offset+8])
                    
                    if import_rva != 0:
                        # 这里需要更复杂的RVA到文件偏移的转换
                        # 简化处理：直接搜索可能的导入函数名
                        self._find_import_names(imports)
        except:
            # 如果PE解析失败，使用启发式方法
            self._find_import_names(imports)
        
        return imports[:50]  # 限制数量
    
    def _find_import_names(self, imports: List[Dict[str, Any]]):
        """使用启发式方法查找导入函数名"""
        # 常见的EFI函数名模式
        efi_functions = [
            b'AllocatePool', b'FreePool', b'AllocatePages', b'FreePages',
            b'LoadImage', b'StartImage', b'UnloadImage', b'ExitBootServices',
            b'GetMemoryMap', b'SetWatchdogTimer', b'ConnectController',
            b'DisconnectController', b'OpenProtocol', b'CloseProtocol',
            b'HandleProtocol', b'LocateProtocol', b'LocateHandle',
            b'GetVariable', b'SetVariable', b'GetNextVariableName',
            b'GetTime', b'SetTime', b'GetWakeupTime', b'SetWakeupTime'
        ]
        
        for func_name in efi_functions:
            for match in re.finditer(func_name, self.file_data):
                imports.append({
                    "name": func_name.decode('ascii'),
                    "offset": f"0x{match.start():08x}",
                    "type": "EFI_FUNCTION"
                })
    
    def find_function_patterns(self) -> List[Dict[str, Any]]:
        """查找函数模式"""
        patterns = []
        
        # 函数序言模式
        prologue_patterns = [
            (rb'\x55\x48\x89\xe5', "push rbp; mov rbp, rsp"),
            (rb'\x48\x83\xec', "sub rsp, imm8"),
            (rb'\x48\x89\x5c\x24', "mov [rsp+offset], rbx"),
            (rb'\x40\x53', "push rbx (with REX)"),
            (rb'\x41\x54', "push r12"),
            (rb'\x41\x55', "push r13"),
            (rb'\x41\x56', "push r14"),
            (rb'\x41\x57', "push r15")
        ]
        
        for pattern, description in prologue_patterns:
            for match in re.finditer(pattern, self.file_data):
                patterns.append({
                    "offset": f"0x{match.start():08x}",
                    "pattern": pattern.hex().upper(),
                    "description": description,
                    "type": "function_prologue"
                })
                
                if len(patterns) >= 200:  # 限制数量
                    break
        
        return patterns
    
    def generate_ghidra_script(self, output_file: str = "efi_analysis_script.py"):
        """生成Ghidra Python脚本"""
        strings_info = self.extract_strings_detailed()
        guids = self.find_efi_guids()
        
        script_content = f'''# Ghidra EFI Analysis Script
# Generated by EFI Analysis Helper

# Import Ghidra modules
from ghidra.program.model.symbol import SourceType
from ghidra.program.model.data import DataType
from ghidra.program.model.listing import CodeUnit

def main():
    print("Starting EFI Analysis...")
    
    # Get current program
    program = getCurrentProgram()
    if program is None:
        print("No program loaded")
        return
    
    # Create symbol table reference
    symbolTable = program.getSymbolTable()
    listing = program.getListing()
    
    # Add known EFI GUIDs as labels
    print("Adding EFI GUID labels...")
    guids = {guids[:20]}  # Limit to first 20 GUIDs
    
    for guid_info in guids:
        try:
            offset = int(guid_info["offset"], 16)
            address = program.getAddressFactory().getDefaultAddressSpace().getAddress(offset)
            if address is not None:
                symbolTable.createLabel(address, guid_info["name"], SourceType.ANALYSIS)
                print(f"Added GUID label: {{guid_info['name']}} at {{guid_info['offset']}}")
        except:
            continue
    
    # Add string labels
    print("Adding string labels...")
    efi_protocols = {strings_info.get("efi_protocols", [])[:10]}
    
    for string_info in efi_protocols:
        try:
            offset = int(string_info["offset"], 16)
            address = program.getAddressFactory().getDefaultAddressSpace().getAddress(offset)
            if address is not None:
                label_name = "STR_" + string_info["string"][:20].replace(" ", "_").replace("-", "_")
                symbolTable.createLabel(address, label_name, SourceType.ANALYSIS)
                print(f"Added string label: {{label_name}} at {{string_info['offset']}}")
        except:
            continue
    
    # Add comments for important strings
    print("Adding comments...")
    error_messages = {strings_info.get("error_messages", [])[:10]}
    
    for error_info in error_messages:
        try:
            offset = int(error_info["offset"], 16)
            address = program.getAddressFactory().getDefaultAddressSpace().getAddress(offset)
            if address is not None:
                codeUnit = listing.getCodeUnitAt(address)
                if codeUnit is not None:
                    codeUnit.setComment(CodeUnit.EOL_COMMENT, f"Error message: {{error_info['string']}}")
        except:
            continue
    
    print("EFI Analysis complete!")

if __name__ == "__main__":
    main()
'''
        
        with open(output_file, 'w', encoding='utf-8') as f:
            f.write(script_content)
        
        print(f"Ghidra脚本已生成: {output_file}")
        return output_file
    
    def generate_analysis_report(self) -> Dict[str, Any]:
        """生成完整分析报告"""
        print("生成EFI分析报告...")
        
        report = {
            "file_info": {
                "path": self.efi_file_path,
                "size": len(self.file_data),
                "size_kb": round(len(self.file_data) / 1024, 2)
            },
            "strings": self.extract_strings_detailed(),
            "efi_guids": self.find_efi_guids(),
            "imports": self.analyze_pe_imports(),
            "function_patterns": self.find_function_patterns()
        }
        
        # 统计信息
        report["statistics"] = {
            "total_strings": sum(len(strings) for strings in report["strings"].values()),
            "efi_protocols": len(report["strings"]["efi_protocols"]),
            "error_messages": len(report["strings"]["error_messages"]),
            "total_guids": len(report["efi_guids"]),
            "known_guids": len([g for g in report["efi_guids"] if g["name"] != "Unknown GUID"]),
            "function_patterns": len(report["function_patterns"])
        }
        
        return report

def main():
    """主函数"""
    print("=== EFI分析辅助工具 ===")
    print("=== EFI Analysis Helper Tool ===\\n")
    
    efi_file = "EFI/BOOT/BOOTX64.EFI"
    
    if not Path(efi_file).exists():
        print(f"❌ EFI文件不存在: {efi_file}")
        return
    
    # 创建分析器
    analyzer = EFIAnalysisHelper(efi_file)
    
    # 生成分析报告
    report = analyzer.generate_analysis_report()
    
    # 保存报告
    report_file = "efi_analysis_report.json"
    with open(report_file, 'w', encoding='utf-8') as f:
        json.dump(report, f, indent=2, ensure_ascii=False)
    
    # 生成Ghidra脚本
    script_file = analyzer.generate_ghidra_script()
    
    # 显示统计信息
    stats = report["statistics"]
    print(f"\\n📊 分析统计:")
    print(f"  - 文件大小: {report['file_info']['size_kb']} KB")
    print(f"  - 总字符串: {stats['total_strings']}")
    print(f"  - EFI协议: {stats['efi_protocols']}")
    print(f"  - 错误消息: {stats['error_messages']}")
    print(f"  - 总GUID: {stats['total_guids']}")
    print(f"  - 已知GUID: {stats['known_guids']}")
    print(f"  - 函数模式: {stats['function_patterns']}")
    
    print(f"\\n📁 输出文件:")
    print(f"  - 分析报告: {report_file}")
    print(f"  - Ghidra脚本: {script_file}")
    print(f"  - 手动指南: ghidra_manual_decompile_guide.md")
    
    print(f"\\n🔧 下一步操作:")
    print(f"1. 启动Ghidra: D:\\ghidra_11.4.1_PUBLIC\\ghidraRun.bat")
    print(f"2. 导入EFI文件: {efi_file}")
    print(f"3. 运行生成的脚本: {script_file}")
    print(f"4. 参考手动指南进行深度分析")

if __name__ == "__main__":
    main()
