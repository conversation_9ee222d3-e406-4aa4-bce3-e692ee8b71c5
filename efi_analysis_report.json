{"file_info": {"path": "EFI/BOOT/BOOTX64.EFI", "size": 965672, "size_kb": 943.04}, "strings": {"efi_protocols": [{"string": "directory services (X.500)", "offset": "0x000810cb", "length": 26, "type": "ASCII"}, {"string": "id-mod-timestamp-protocol", "offset": "0x000827fd", "length": 25, "type": "ASCII"}, {"string": "id-regCtrl-protocolEncrKey", "offset": "0x00082b03", "length": 26, "type": "ASCII"}, {"string": "serviceLocator", "offset": "0x00082f19", "length": 14, "type": "ASCII"}, {"string": "OCSP Service Locator", "offset": "0x00082f28", "length": 20, "type": "ASCII"}, {"string": "directory services - algorithms", "offset": "0x00082f97", "length": 31, "type": "ASCII"}, {"string": "searchGuide", "offset": "0x00085717", "length": 11, "type": "ASCII"}, {"string": "enhancedSearchGuide", "offset": "0x000858e1", "length": 19, "type": "ASCII"}, {"string": "protocolInformation", "offset": "0x000858f5", "length": 19, "type": "ASCII"}, {"string": "OCSP_SERVICELOC", "offset": "0x00087365", "length": 15, "type": "ASCII"}, {"string": " protocol=\"%ssignature\";", "offset": "0x00088458", "length": 24, "type": "ASCII"}, {"string": "GuidList", "offset": "0x000ce624", "length": 8, "type": "ASCII"}, {"string": "<PERSON>ull<PERSON><PERSON>", "offset": "0x000cf50c", "length": 8, "type": "ASCII"}, {"string": "BDS_GUID", "offset": "0x000d9124", "length": 8, "type": "ASCII"}, {"string": "GV_GUID", "offset": "0x000d9f46", "length": 7, "type": "ASCII"}, {"string": "shim_lock_interface", "offset": "0x000da2e1", "length": 19, "type": "ASCII"}, {"string": "system_exit_boot_services", "offset": "0x000da4c1", "length": 25, "type": "ASCII"}, {"string": "exit_boot_services", "offset": "0x000da4fe", "length": 18, "type": "ASCII"}, {"string": "tpm_locate_protocol", "offset": "0x000da549", "length": 19, "type": "ASCII"}, {"string": "i2r_ocsp_serviceloc", "offset": "0x000db7fb", "length": 19, "type": "ASCII"}, {"string": "OCSP_SERVICELOC_seq_tt", "offset": "0x000dbc09", "length": 22, "type": "ASCII"}, {"string": "_DevPathMediaProtocol", "offset": "0x000dd6ed", "length": 21, "type": "ASCII"}, {"string": "CatPrintNetworkProtocol", "offset": "0x000dd765", "length": 23, "type": "ASCII"}, {"string": "KnownGuids", "offset": "0x000dd88e", "length": 10, "type": "ASCII"}, {"string": "ShellInterfaceProtocol", "offset": "0x000dd899", "length": 22, "type": "ASCII"}, {"string": "gEfiPlatformDriverOverrideProtocolGuid", "offset": "0x000dd8f9", "length": 38, "type": "ASCII"}, {"string": "LibCreateProtocolNotifyEvent", "offset": "0x000dd92d", "length": 28, "type": "ASCII"}, {"string": "gEfiPciIoProtocolGuid", "offset": "0x000dd954", "length": 21, "type": "ASCII"}, {"string": "gEfiDebugSupportProtocolGuid", "offset": "0x000dd982", "length": 28, "type": "ASCII"}, {"string": "ErrorOutSpliterProtocol", "offset": "0x000dd99f", "length": 23, "type": "ASCII"}, {"string": "Ip4ServiceBindingProtocol", "offset": "0x000dd9cd", "length": 25, "type": "ASCII"}, {"string": "LibLocateProtocol", "offset": "0x000dd9f8", "length": 17, "type": "ASCII"}, {"string": "TextOutSpliterProtocol", "offset": "0x000dda35", "length": 22, "type": "ASCII"}, {"string": "gEfiDriverBindingProtocolGuid", "offset": "0x000dda4c", "length": 29, "type": "ASCII"}, {"string": "AbsolutePointerProtocol", "offset": "0x000dda84", "length": 23, "type": "ASCII"}, {"string": "gEfiDriverFamilyOverrideProtocolGuid", "offset": "0x000dda9c", "length": 36, "type": "ASCII"}, {"string": "gEfiComponentName2ProtocolGuid", "offset": "0x000ddac1", "length": 30, "type": "ASCII"}, {"string": "Ip4Protocol", "offset": "0x000ddae0", "length": 11, "type": "ASCII"}, {"string": "gEfiFileInfoGuid", "offset": "0x000ddaec", "length": 16, "type": "ASCII"}, {"string": "Tcp4Protocol", "offset": "0x000ddafd", "length": 12, "type": "ASCII"}, {"string": "UnicodeInterface", "offset": "0x000ddb5b", "length": 16, "type": "ASCII"}, {"string": "SimplePointerProtocol", "offset": "0x000ddb6c", "length": 21, "type": "ASCII"}, {"string": "gEFiUiInterfaceProtocolGuid", "offset": "0x000ddbb0", "length": 27, "type": "ASCII"}, {"string": "SMBIOSTableGuid", "offset": "0x000ddbcc", "length": 15, "type": "ASCII"}, {"string": "gEfiUnicodeCollationProtocolGuid", "offset": "0x000ddc1c", "length": 32, "type": "ASCII"}, {"string": "gEfiBlockIo2ProtocolGuid", "offset": "0x000ddc6f", "length": 24, "type": "ASCII"}, {"string": "LegacyBootProtocol", "offset": "0x000ddc88", "length": 18, "type": "ASCII"}, {"string": "AcpiTableGuid", "offset": "0x000ddc9b", "length": 13, "type": "ASCII"}, {"string": "gEfiEdidDiscoveredProtocolGuid", "offset": "0x000ddca9", "length": 30, "type": "ASCII"}, {"string": "LibUninstallProtocolInterfaces", "offset": "0x000ddcd6", "length": 30, "type": "ASCII"}], "file_paths": [{"string": "B/14", "offset": "0x000001ff", "length": 4, "type": "ASCII"}, {"string": "h[]A\\A]A^A_", "offset": "0x0001e45b", "length": 11, "type": "ASCII"}, {"string": "[]A\\A]A^A_", "offset": "0x0001e4dc", "length": 10, "type": "ASCII"}, {"string": "H[]A\\A]A^A_", "offset": "0x0001e5dc", "length": 11, "type": "ASCII"}, {"string": "[]A\\A]A^A_", "offset": "0x0001e668", "length": 10, "type": "ASCII"}, {"string": "[]A\\A]A^A_", "offset": "0x0001e9c2", "length": 10, "type": "ASCII"}, {"string": "[]A\\A]A^", "offset": "0x0001ebf2", "length": 8, "type": "ASCII"}, {"string": "[]A\\A]A^A_", "offset": "0x0001f504", "length": 10, "type": "ASCII"}, {"string": "[]A\\A]A^A_", "offset": "0x0001f772", "length": 10, "type": "ASCII"}, {"string": "[]A\\A]A^A_", "offset": "0x0001f785", "length": 10, "type": "ASCII"}, {"string": "[]A\\", "offset": "0x0001f7ae", "length": 4, "type": "ASCII"}, {"string": "[]A\\", "offset": "0x0001f7b7", "length": 4, "type": "ASCII"}, {"string": "[]A\\A]A^A_", "offset": "0x0001f868", "length": 10, "type": "ASCII"}, {"string": "[]A\\A]A^A_", "offset": "0x00020131", "length": 10, "type": "ASCII"}, {"string": "[]A\\A]", "offset": "0x000206d1", "length": 6, "type": "ASCII"}, {"string": "\\$xL", "offset": "0x000209b7", "length": 4, "type": "ASCII"}, {"string": "[]A\\A]A^", "offset": "0x00020c10", "length": 8, "type": "ASCII"}, {"string": "\\$HL", "offset": "0x00020c7f", "length": 4, "type": "ASCII"}, {"string": "x[]A\\A]A^A_", "offset": "0x00020d2c", "length": 11, "type": "ASCII"}, {"string": "[]A\\A]A^A_", "offset": "0x000212d2", "length": 10, "type": "ASCII"}, {"string": "[]A\\A]A^A_", "offset": "0x00021ac7", "length": 10, "type": "ASCII"}, {"string": "[]A\\A]A^", "offset": "0x00021c0f", "length": 8, "type": "ASCII"}, {"string": "\\$ H", "offset": "0x00021dd6", "length": 4, "type": "ASCII"}, {"string": "\\$HH9", "offset": "0x00021deb", "length": 5, "type": "ASCII"}, {"string": "[^_]A\\A]A^A_", "offset": "0x00021f59", "length": 12, "type": "ASCII"}, {"string": "[]A\\", "offset": "0x000221d4", "length": 4, "type": "ASCII"}, {"string": "\\$HH9", "offset": "0x000223b3", "length": 5, "type": "ASCII"}, {"string": "[^_]A\\A]A^A_", "offset": "0x00022538", "length": 12, "type": "ASCII"}, {"string": "8v/H", "offset": "0x00023b6a", "length": 4, "type": "ASCII"}, {"string": "[]A\\A]A^A_", "offset": "0x0002421c", "length": 10, "type": "ASCII"}, {"string": "[]A\\A]A^A_", "offset": "0x00024702", "length": 10, "type": "ASCII"}, {"string": "<$/u", "offset": "0x00024c7c", "length": 4, "type": "ASCII"}, {"string": "<$/u", "offset": "0x00024c8b", "length": 4, "type": "ASCII"}, {"string": "{l/t", "offset": "0x00024c9d", "length": 4, "type": "ASCII"}, {"string": "<$/@", "offset": "0x00024ca6", "length": 4, "type": "ASCII"}, {"string": "X[]A\\A]A^A_", "offset": "0x00024ce7", "length": 11, "type": "ASCII"}, {"string": " []A\\", "offset": "0x00024dd8", "length": 5, "type": "ASCII"}, {"string": "[^_]A\\", "offset": "0x00025151", "length": 6, "type": "ASCII"}, {"string": "H[]A\\A]", "offset": "0x00025385", "length": 7, "type": "ASCII"}, {"string": "\\$0H", "offset": "0x000254e5", "length": 4, "type": "ASCII"}, {"string": "[]A\\A]A^A_", "offset": "0x00025835", "length": 10, "type": "ASCII"}, {"string": "([]A\\A]A^A_", "offset": "0x0002598d", "length": 11, "type": "ASCII"}, {"string": "H[]A\\A]A^A_", "offset": "0x00025c42", "length": 11, "type": "ASCII"}, {"string": "\\$XH9", "offset": "0x00025eb2", "length": 5, "type": "ASCII"}, {"string": "[^_]A\\A]A^A_", "offset": "0x0002604a", "length": 12, "type": "ASCII"}, {"string": "8[^_]A\\A]", "offset": "0x00026159", "length": 9, "type": "ASCII"}, {"string": "[]A\\A]A^A_", "offset": "0x0002660b", "length": 10, "type": "ASCII"}, {"string": "Z[]A\\A]", "offset": "0x00026716", "length": 7, "type": "ASCII"}, {"string": "[]A\\A]A^A_", "offset": "0x0002685e", "length": 10, "type": "ASCII"}, {"string": "\\$(E1", "offset": "0x00026974", "length": 5, "type": "ASCII"}], "error_messages": [{"string": "error:%08lX:%s:%s:%s", "offset": "0x00080f97", "length": 20, "type": "ASCII"}, {"string": "invalidityDate", "offset": "0x00081c24", "length": 14, "type": "ASCII"}, {"string": "Invalidity Date", "offset": "0x00081c33", "length": 15, "type": "ASCII"}, {"string": "setct-ErrorTBS", "offset": "0x00083c9e", "length": 14, "type": "ASCII"}, {"string": "<INVALID>", "offset": "0x0008613b", "length": 9, "type": "ASCII"}, {"string": "IP Address:<invalid>", "offset": "0x000867f0", "length": 20, "type": "ASCII"}, {"string": "Verify error:", "offset": "0x0008730e", "length": 13, "type": "ASCII"}, {"string": "ERROR", "offset": "0x00087651", "length": 5, "type": "ASCII"}, {"string": "%s(%d): OpenSSL internal error, assertion failed: %s", "offset": "0x00087657", "length": 52, "type": "ASCII"}, {"string": "<<ERROR>>", "offset": "0x0008769d", "length": 9, "type": "ASCII"}, {"string": "ERROR: selector [%d] invalid", "offset": "0x00088238", "length": 28, "type": "ASCII"}, {"string": "Error in encoding", "offset": "0x00088742", "length": 17, "type": "ASCII"}, {"string": "certificate signature failure", "offset": "0x00088a3d", "length": 29, "type": "ASCII"}, {"string": "CRL signature failure", "offset": "0x00088a5b", "length": 21, "type": "ASCII"}, {"string": "format error in certificate's notBefore field", "offset": "0x00088acb", "length": 45, "type": "ASCII"}, {"string": "format error in certificate's notAfter field", "offset": "0x00088af9", "length": 44, "type": "ASCII"}, {"string": "format error in CRL's lastUpdate field", "offset": "0x00088b26", "length": 38, "type": "ASCII"}, {"string": "format error in CRL's nextUpdate field", "offset": "0x00088b4d", "length": 38, "type": "ASCII"}, {"string": "invalid CA certificate", "offset": "0x00088c44", "length": 22, "type": "ASCII"}, {"string": "invalid non-CA certificate (has CA markings)", "offset": "0x00088c5b", "length": 44, "type": "ASCII"}, {"string": "application verification failure", "offset": "0x00088d3b", "length": 32, "type": "ASCII"}, {"string": "invalid or inconsistent certificate extension", "offset": "0x00088eb4", "length": 45, "type": "ASCII"}, {"string": "invalid or inconsistent certificate policy extension", "offset": "0x00088ee2", "length": 52, "type": "ASCII"}, {"string": "unsupported or invalid name constraint syntax", "offset": "0x0008901a", "length": 45, "type": "ASCII"}, {"string": "unsupported or invalid name syntax", "offset": "0x00089048", "length": 34, "type": "ASCII"}, {"string": "CRL path validation error", "offset": "0x0008906b", "length": 25, "type": "ASCII"}, {"string": "Suite B: certificate version invalid", "offset": "0x00089085", "length": 36, "type": "ASCII"}, {"string": "Suite B: invalid public key algorithm", "offset": "0x000890aa", "length": 37, "type": "ASCII"}, {"string": "Suite B: invalid ECC curve", "offset": "0x000890d0", "length": 26, "type": "ASCII"}, {"string": "Suite B: invalid signature algorithm", "offset": "0x000890eb", "length": 36, "type": "ASCII"}, {"string": "Invalid certificate verification context", "offset": "0x0008919b", "length": 40, "type": "ASCII"}, {"string": "Issuer certificate lookup error", "offset": "0x000891c4", "length": 31, "type": "ASCII"}, {"string": "error number %ld", "offset": "0x00089201", "length": 16, "type": "ASCII"}, {"string": "%*s<Parse Error>", "offset": "0x000893ce", "length": 16, "type": "ASCII"}, {"string": " (INVALID PSS PARAMETERS)", "offset": "0x00089656", "length": 25, "type": "ASCII"}, {"string": "INVALID", "offset": "0x0008969a", "length": 7, "type": "ASCII"}, {"string": "drain_openssl_errors", "offset": "0x000da15c", "length": 20, "type": "ASCII"}, {"string": "int_error_hash", "offset": "0x000daa8f", "length": 14, "type": "ASCII"}, {"string": "get_error_values", "offset": "0x000dabb7", "length": 16, "type": "ASCII"}, {"string": "check_id_error", "offset": "0x000db00c", "length": 14, "type": "ASCII"}, {"string": "error_table", "offset": "0x000dd6a0", "length": 11, "type": "ASCII"}, {"string": "print_errors_cb", "offset": "0x000dd6ac", "length": 15, "type": "ASCII"}, {"string": "ErrorCodeTable", "offset": "0x000ddd88", "length": 14, "type": "ASCII"}, {"string": "LogError_", "offset": "0x000de940", "length": 9, "type": "ASCII"}, {"string": "ERR_peek_error_line", "offset": "0x000deaba", "length": 19, "type": "ASCII"}, {"string": "ClearErrors", "offset": "0x000dec7f", "length": 11, "type": "ASCII"}, {"string": "console_errorbox", "offset": "0x000decb6", "length": 16, "type": "ASCII"}, {"string": "ERR_get_error_line_data", "offset": "0x000dee67", "length": 23, "type": "ASCII"}, {"string": "ERR_error_string_n", "offset": "0x000df7d7", "length": 18, "type": "ASCII"}, {"string": "X509_STORE_CTX_get_error_depth", "offset": "0x000df84a", "length": 30, "type": "ASCII"}], "debug_strings": [{"string": "msSmartcardLogin", "offset": "0x0008437c", "length": 16, "type": "ASCII"}, {"string": "Microsoft Smartcardlogin", "offset": "0x0008438d", "length": 24, "type": "ASCII"}, {"string": "ASN1_PRINTABLE", "offset": "0x000862e7", "length": 14, "type": "ASCII"}, {"string": "ASN1_PRINTABLESTRING", "offset": "0x00086397", "length": 20, "type": "ASCII"}, {"string": "debug_malloc", "offset": "0x0008772e", "length": 12, "type": "ASCII"}, {"string": "debug_malloc2", "offset": "0x00087770", "length": 13, "type": "ASCII"}, {"string": "tpm_log_pe", "offset": "0x0008ec80", "length": 10, "type": "ASCII"}, {"string": "cc_log_event_raw", "offset": "0x0008ec90", "length": 16, "type": "ASCII"}, {"string": "tpm_log_event_raw", "offset": "0x0008ecb0", "length": 17, "type": "ASCII"}, {"string": "print_ip6_addr", "offset": "0x0008ef08", "length": 14, "type": "ASCII"}, {"string": "print_ip4_addr", "offset": "0x0008ef20", "length": 14, "type": "ASCII"}, {"string": "bn_print'", "offset": "0x000ccb5a", "length": 9, "type": "ASCII"}, {"string": "SPrint", "offset": "0x000ce600", "length": 6, "type": "ASCII"}, {"string": "DbgPrint", "offset": "0x000ce6b4", "length": 8, "type": "ASCII"}, {"string": "Print", "offset": "0x000ce7d4", "length": 5, "type": "ASCII"}, {"string": "VSPrint", "offset": "0x000ce8be", "length": 7, "type": "ASCII"}, {"string": "_SPrint", "offset": "0x000ce94e", "length": 7, "type": "ASCII"}, {"string": "IPrintAt2", "offset": "0x000cebfa", "length": 9, "type": "ASCII"}, {"string": "EFIDebug", "offset": "0x000cec0c", "length": 8, "type": "ASCII"}, {"string": "Output", "offset": "0x000cecf6", "length": 6, "type": "ASCII"}, {"string": "<PERSON><PERSON><PERSON><PERSON>", "offset": "0x000ced08", "length": 9, "type": "ASCII"}, {"string": "IPrint", "offset": "0x000cee82", "length": 6, "type": "ASCII"}, {"string": "PrintAt", "offset": "0x000cf140", "length": 7, "type": "ASCII"}, {"string": "_IPrint", "offset": "0x000cf164", "length": 7, "type": "ASCII"}, {"string": "_Print", "offset": "0x000cf296", "length": 6, "type": "ASCII"}, {"string": "VPrint", "offset": "0x000cf48e", "length": 6, "type": "ASCII"}, {"string": "BN_printc&", "offset": "0x000cf9b0", "length": 10, "type": "ASCII"}, {"string": "printf", "offset": "0x000cfbde", "length": 6, "type": "ASCII"}, {"string": "fprintf", "offset": "0x000d3466", "length": 7, "type": "ASCII"}, {"string": "openlog", "offset": "0x000d433c", "length": 7, "type": "ASCII"}, {"string": "closelog", "offset": "0x000d4414", "length": 8, "type": "ASCII"}, {"string": "syslog", "offset": "0x000d5bc6", "length": 6, "type": "ASCII"}, {"string": "vdprint_~|", "offset": "0x000d5cb0", "length": 10, "type": "ASCII"}, {"string": "vfprintf", "offset": "0x000d8af4", "length": 8, "type": "ASCII"}, {"string": "wait_for_debug", "offset": "0x000da116", "length": 14, "type": "ASCII"}, {"string": "debug_hook", "offset": "0x000da354", "length": 10, "type": "ASCII"}, {"string": "tpm_log_event_raw", "offset": "0x000da55d", "length": 17, "type": "ASCII"}, {"string": "print_ip4_addr", "offset": "0x000da737", "length": 14, "type": "ASCII"}, {"string": "print_ip6_addr", "offset": "0x000da754", "length": 14, "type": "ASCII"}, {"string": "allow_customize_debug", "offset": "0x000da930", "length": 21, "type": "ASCII"}, {"string": "malloc_debug_func", "offset": "0x000da946", "length": 17, "type": "ASCII"}, {"string": "realloc_debug_func", "offset": "0x000da958", "length": 18, "type": "ASCII"}, {"string": "free_debug_func", "offset": "0x000da96b", "length": 15, "type": "ASCII"}, {"string": "set_debug_options_func", "offset": "0x000da97b", "length": 22, "type": "ASCII"}, {"string": "get_debug_options_func", "offset": "0x000da992", "length": 22, "type": "ASCII"}, {"string": "print_qualifiers", "offset": "0x000db410", "length": 16, "type": "ASCII"}, {"string": "print_reasons", "offset": "0x000db4da", "length": 13, "type": "ASCII"}, {"string": "print_gens", "offset": "0x000db4f5", "length": 10, "type": "ASCII"}, {"string": "print_distpoint", "offset": "0x000db50c", "length": 15, "type": "ASCII"}, {"string": "print_leak_doall_arg", "offset": "0x000dbe40", "length": 20, "type": "ASCII"}], "function_names": [{"string": "holdInstructionCallIssuer", "offset": "0x000833d0", "length": 25, "type": "ASCII"}, {"string": "Hold Instruction Call Issuer", "offset": "0x000833ea", "length": 28, "type": "ASCII"}, {"string": "Unprocessed type %d", "offset": "0x00088256", "length": 19, "type": "ASCII"}, {"string": "null_callback", "offset": "0x000daffe", "length": 13, "type": "ASCII"}, {"string": "process_pci_value", "offset": "0x000db988", "length": 17, "type": "ASCII"}, {"string": "dynlock_create_callback", "offset": "0x000dbd6a", "length": 23, "type": "ASCII"}, {"string": "dynlock_destroy_callback", "offset": "0x000dbd8c", "length": 24, "type": "ASCII"}, {"string": "dyn<PERSON>_lock_callback", "offset": "0x000dbda5", "length": 21, "type": "ASCII"}, {"string": "locking_callback", "offset": "0x000dbdbb", "length": 16, "type": "ASCII"}, {"string": "add_lock_callback", "offset": "0x000dbdcc", "length": 17, "type": "ASCII"}, {"string": "threadid_callback", "offset": "0x000dbdde", "length": 17, "type": "ASCII"}, {"string": "buffer_callback_ctrl", "offset": "0x000dc0c5", "length": 20, "type": "ASCII"}, {"string": "md_callback_ctrl", "offset": "0x000dc1e0", "length": 16, "type": "ASCII"}, {"string": "enc_callback_ctrl", "offset": "0x000dc1fc", "length": 17, "type": "ASCII"}, {"string": "b64_callback_ctrl", "offset": "0x000dd433", "length": 17, "type": "ASCII"}, {"string": "asn1_bio_callback_ctrl", "offset": "0x000dd5c0", "length": 22, "type": "ASCII"}, {"string": "BIO_get_callback", "offset": "0x000dec09", "length": 16, "type": "ASCII"}, {"string": "CRYPTO_get_mem_ex_functions", "offset": "0x000df516", "length": 27, "type": "ASCII"}, {"string": "BIO_set_callback", "offset": "0x000dfc86", "length": 16, "type": "ASCII"}, {"string": "CRYPTO_get_dynlock_destroy_callback", "offset": "0x000e0760", "length": 35, "type": "ASCII"}, {"string": "CRYPTO_set_dynlock_destroy_callback", "offset": "0x000e0edb", "length": 35, "type": "ASCII"}, {"string": "CRYPTO_get_dynlock_create_callback", "offset": "0x000e1060", "length": 34, "type": "ASCII"}, {"string": "CRYPTO_set_add_lock_callback", "offset": "0x000e28cf", "length": 28, "type": "ASCII"}, {"string": "BIO_get_callback_arg", "offset": "0x000e2905", "length": 20, "type": "ASCII"}, {"string": "CRYPTO_get_dynlock_lock_callback", "offset": "0x000e3562", "length": 32, "type": "ASCII"}, {"string": "CRYPTO_set_mem_ex_functions", "offset": "0x000e3685", "length": 27, "type": "ASCII"}, {"string": "NopFunction", "offset": "0x000e4614", "length": 11, "type": "ASCII"}, {"string": "CRYPTO_get_mem_functions", "offset": "0x000e4819", "length": 24, "type": "ASCII"}, {"string": "BIO_callback_ctrl", "offset": "0x000e4bae", "length": 17, "type": "ASCII"}, {"string": "CRYPTO_set_locking_callback", "offset": "0x000e4c58", "length": 27, "type": "ASCII"}, {"string": "BN_GENCB_call", "offset": "0x000e4fc6", "length": 13, "type": "ASCII"}, {"string": "BIO_set_callback_arg", "offset": "0x000e5589", "length": 20, "type": "ASCII"}, {"string": "CRYPTO_get_locked_mem_ex_functions", "offset": "0x000e5879", "length": 34, "type": "ASCII"}, {"string": "CRYPTO_set_mem_functions", "offset": "0x000e5b1d", "length": 24, "type": "ASCII"}, {"string": "CRYPTO_THREADID_set_callback", "offset": "0x000e5d70", "length": 28, "type": "ASCII"}, {"string": "CRYPTO_get_add_lock_callback", "offset": "0x000e5e0e", "length": 28, "type": "ASCII"}, {"string": "CRYPTO_set_dynlock_lock_callback", "offset": "0x000e6315", "length": 32, "type": "ASCII"}, {"string": "BN_reciprocal", "offset": "0x000e638a", "length": 13, "type": "ASCII"}, {"string": "CRYPTO_get_locked_mem_functions", "offset": "0x000e65de", "length": 31, "type": "ASCII"}, {"string": "CRYPTO_set_locked_mem_functions", "offset": "0x000e6897", "length": 31, "type": "ASCII"}, {"string": "CRYPTO_set_dynlock_create_callback", "offset": "0x000e6d20", "length": 34, "type": "ASCII"}, {"string": "CRYPTO_get_locking_callback", "offset": "0x000e6e5e", "length": 27, "type": "ASCII"}, {"string": "BN_mod_mul_reciprocal", "offset": "0x000e6f30", "length": 21, "type": "ASCII"}, {"string": "CRYPTO_THREADID_get_callback", "offset": "0x000e7215", "length": 28, "type": "ASCII"}, {"string": "CRYPTO_set_locked_mem_ex_functions", "offset": "0x000e72e0", "length": 34, "type": "ASCII"}], "registry_keys": [{"string": "dhKeyAgreement", "offset": "0x000811d3", "length": 14, "type": "ASCII"}, {"string": "Microsoft Encrypted File System", "offset": "0x00081b9c", "length": 31, "type": "ASCII"}, {"string": "ipsecEndSystem", "offset": "0x000828d7", "length": 14, "type": "ASCII"}, {"string": "IPSec End System", "offset": "0x000828e6", "length": 16, "type": "ASCII"}, {"string": "value.shkeybag", "offset": "0x000894d0", "length": 14, "type": "ASCII"}, {"string": "system_load_image", "offset": "0x000da49c", "length": 17, "type": "ASCII"}, {"string": "system_start_image", "offset": "0x000da4ae", "length": 18, "type": "ASCII"}, {"string": "system_exit", "offset": "0x000da523", "length": 11, "type": "ASCII"}, {"string": "LibFileSystemInfo", "offset": "0x000ddd76", "length": 17, "type": "ASCII"}, {"string": "LibFileSystemVolumeLabelInfo", "offset": "0x000de39e", "length": 28, "type": "ASCII"}, {"string": "LibGetSystemConfigurationTable", "offset": "0x000de44a", "length": 30, "type": "ASCII"}, {"string": "PKCS12_MAKE_SHKEYBAG", "offset": "0x000e22c5", "length": 20, "type": "ASCII"}], "urls": [{"string": "httpboot.c", "offset": "0x00080e77", "length": 10, "type": "ASCII"}, {"string": "receive_http_response", "offset": "0x0008eec0", "length": 21, "type": "ASCII"}, {"string": "send_http_request", "offset": "0x0008eee0", "length": 17, "type": "ASCII"}, {"string": "http_fetch", "offset": "0x0008eef8", "length": 10, "type": "ASCII"}, {"string": "httpboot_fetch_buffer", "offset": "0x0008ef50", "length": 21, "type": "ASCII"}, {"string": "find_httpboot", "offset": "0x0008ef68", "length": 13, "type": "ASCII"}, {"string": "tftp_addr", "offset": "0x000da488", "length": 9, "type": "ASCII"}, {"string": "httpnotify", "offset": "0x000da72c", "length": 10, "type": "ASCII"}, {"string": "convert_http_status_code", "offset": "0x000da771", "length": 24, "type": "ASCII"}, {"string": "receive_http_response", "offset": "0x000da78a", "length": 21, "type": "ASCII"}, {"string": "http_hdr.13304", "offset": "0x000dbd51", "length": 14, "type": "ASCII"}, {"string": "find_httpboot", "offset": "0x000def20", "length": 13, "type": "ASCII"}, {"string": "X509_CRL_http_nbio", "offset": "0x000e157c", "length": 18, "type": "ASCII"}, {"string": "OCSP_REQ_CTX_http", "offset": "0x000e251a", "length": 17, "type": "ASCII"}, {"string": "X509_http_nbio", "offset": "0x000e2a11", "length": 14, "type": "ASCII"}, {"string": "httpboot_fetch_buffer", "offset": "0x000e553a", "length": 21, "type": "ASCII"}], "other_strings": [{"string": "!This program cannot be run in DOS mode.", "offset": "0x0000004d", "length": 40, "type": "ASCII"}, {"string": "@.text", "offset": "0x000001af", "length": 6, "type": "ASCII"}, {"string": "`.reloc", "offset": "0x000001d7", "length": 7, "type": "ASCII"}, {"string": "@.data", "offset": "0x0000024f", "length": 6, "type": "ASCII"}, {"string": "@.dynamic", "offset": "0x0000029f", "length": 9, "type": "ASCII"}, {"string": ".rela", "offset": "0x000002c8", "length": 5, "type": "ASCII"}, {"string": "@.sbat", "offset": "0x000002ef", "length": 6, "type": "ASCII"}, {"string": "YZQR", "offset": "0x0001e014", "length": 4, "type": "ASCII"}, {"string": "~CfB", "offset": "0x0001e07d", "length": 4, "type": "ASCII"}, {"string": "AWAVE1", "offset": "0x0001e0d2", "length": 6, "type": "ASCII"}, {"string": "AUATUSH", "offset": "0x0001e0d9", "length": 7, "type": "ASCII"}, {"string": "L$0L", "offset": "0x0001e0fa", "length": 4, "type": "ASCII"}, {"string": "D$ H", "offset": "0x0001e0ff", "length": 4, "type": "ASCII"}, {"string": "D$@H", "offset": "0x0001e104", "length": 4, "type": "ASCII"}, {"string": "D$HH", "offset": "0x0001e111", "length": 4, "type": "ASCII"}, {"string": "D$XH", "offset": "0x0001e116", "length": 4, "type": "ASCII"}, {"string": "D$8H", "offset": "0x0001e11b", "length": 4, "type": "ASCII"}, {"string": "H;D$", "offset": "0x0001e12d", "length": 4, "type": "ASCII"}, {"string": "t$ L", "offset": "0x0001e172", "length": 4, "type": "ASCII"}, {"string": "ATPL", "offset": "0x0001e234", "length": 4, "type": "ASCII"}, {"string": "t$XH", "offset": "0x0001e274", "length": 4, "type": "ASCII"}, {"string": "t$81", "offset": "0x0001e288", "length": 4, "type": "ASCII"}, {"string": "$tg1", "offset": "0x0001e2a0", "length": 4, "type": "ASCII"}, {"string": "|$(H", "offset": "0x0001e2e0", "length": 4, "type": "ASCII"}, {"string": "t$8H", "offset": "0x0001e328", "length": 4, "type": "ASCII"}, {"string": "T$HI", "offset": "0x0001e37e", "length": 4, "type": "ASCII"}, {"string": "t$@H", "offset": "0x0001e386", "length": 4, "type": "ASCII"}, {"string": "ATAVL", "offset": "0x0001e41f", "length": 5, "type": "ASCII"}, {"string": "H)D$", "offset": "0x0001e446", "length": 4, "type": "ASCII"}, {"string": "AWAVH", "offset": "0x0001e467", "length": 5, "type": "ASCII"}, {"string": "AUATI", "offset": "0x0001e46e", "length": 5, "type": "ASCII"}, {"string": "x*AVAUL", "offset": "0x0001e4aa", "length": 7, "type": "ASCII"}, {"string": "AWAVI", "offset": "0x0001e4e7", "length": 5, "type": "ASCII"}, {"string": "AUATUSH", "offset": "0x0001e4ee", "length": 7, "type": "ASCII"}, {"string": "D$0L", "offset": "0x0001e516", "length": 4, "type": "ASCII"}, {"string": "L$8H", "offset": "0x0001e51b", "length": 4, "type": "ASCII"}, {"string": "D$ H", "offset": "0x0001e520", "length": 4, "type": "ASCII"}, {"string": "D$0H", "offset": "0x0001e525", "length": 4, "type": "ASCII"}, {"string": "Hcl$", "offset": "0x0001e57f", "length": 4, "type": "ASCII"}, {"string": "D$(u", "offset": "0x0001e5ae", "length": 4, "type": "ASCII"}, {"string": "T$ H", "offset": "0x0001e5b5", "length": 4, "type": "ASCII"}, {"string": "AWAVH", "offset": "0x0001e5e8", "length": 5, "type": "ASCII"}, {"string": "AUATI", "offset": "0x0001e5ef", "length": 5, "type": "ASCII"}, {"string": "x5PAVD", "offset": "0x0001e62b", "length": 6, "type": "ASCII"}, {"string": "AUUL", "offset": "0x0001e633", "length": 4, "type": "ASCII"}, {"string": "D$pL", "offset": "0x0001e63b", "length": 4, "type": "ASCII"}, {"string": "L$xH", "offset": "0x0001e640", "length": 4, "type": "ASCII"}, {"string": "t$ H", "offset": "0x0001e645", "length": 4, "type": "ASCII"}, {"string": "AWAVL", "offset": "0x0001e673", "length": 5, "type": "ASCII"}, {"string": "AUATI", "offset": "0x0001e67e", "length": 5, "type": "ASCII"}]}, "efi_guids": [{"offset": "0x0000003c", "guid": "00000080-1F0E-0EBA-00B4-09CD21B8014C", "name": "Unknown GUID", "bytes": "800000000E1FBA0E00B409CD21B8014C"}, {"offset": "0x00000040", "guid": "0EBA1F0E-B400-CD09-21B8-014CCD215468", "name": "Unknown GUID", "bytes": "0E1FBA0E00B409CD21B8014CCD215468"}, {"offset": "0x00000044", "guid": "CD09B400-B821-4C01-CD21-************", "name": "Unknown GUID", "bytes": "00B409CD21B8014CCD21************"}, {"offset": "0x00000048", "guid": "4C01B821-21CD-6854-6973-2070726F6772", "name": "Unknown GUID", "bytes": "21B8014CCD21************726F6772"}, {"offset": "0x0000004c", "guid": "685421CD-7369-7020-726F-6772616D2063", "name": "Unknown GUID", "bytes": "CD21************726F6772616D2063"}, {"offset": "0x00000050", "guid": "70207369-6F72-7267-616D-2063616E6E6F", "name": "Unknown GUID", "bytes": "69732070726F6772616D2063616E6E6F"}, {"offset": "0x00000054", "guid": "72676F72-6D61-6320-616E-6E6F74206265", "name": "Unknown GUID", "bytes": "726F6772616D2063616E6E6F74206265"}, {"offset": "0x00000058", "guid": "63206D61-6E61-6F6E-7420-62652072756E", "name": "Unknown GUID", "bytes": "616D2063616E6E6F742062652072756E"}, {"offset": "0x0000005c", "guid": "6F6E6E61-**************-756E20696E20", "name": "Unknown GUID", "bytes": "616E6E6F742062652072756E20696E20"}, {"offset": "0x00000060", "guid": "*************-6E75-2069-6E20444F5320", "name": "Unknown GUID", "bytes": "742062652072756E20696E20444F5320"}, {"offset": "0x00000064", "guid": "6E757220-6920-206E-444F-53206D6F6465", "name": "Unknown GUID", "bytes": "2072756E20696E20444F53206D6F6465"}, {"offset": "0x00000068", "guid": "206E6920-4F44-2053-6D6F-64652E0D0D0A", "name": "Unknown GUID", "bytes": "20696E20444F53206D6F64652E0D0D0A"}, {"offset": "0x0000006c", "guid": "20534F44-6F6D-6564-2E0D-0D0A24000000", "name": "Unknown GUID", "bytes": "444F53206D6F64652E0D0D0A24000000"}, {"offset": "0x00000070", "guid": "65646F6D-0D2E-0A0D-2400-000000000000", "name": "Unknown GUID", "bytes": "6D6F64652E0D0D0A2400000000000000"}, {"offset": "0x00000080", "guid": "00004550-8664-000A-0000-0000009E0C00", "name": "Unknown GUID", "bytes": "5045000064860A0000000000009E0C00"}, {"offset": "0x00000090", "guid": "00000E62-00F0-0206-0B02-022900040600", "name": "Unknown GUID", "bytes": "620E0000F00006020B02022900040600"}, {"offset": "0x00000094", "guid": "020600F0-020B-2902-0004-060000960600", "name": "Unknown GUID", "bytes": "F00006020B0202290004060000960600"}, {"offset": "0x00000098", "guid": "2902020B-0400-0006-0096-060000000000", "name": "Unknown GUID", "bytes": "0B020229000406000096060000000000"}, {"offset": "0x0000009c", "guid": "00060400-**************-000000300200", "name": "Unknown GUID", "bytes": "00040600009606000000000000300200"}, {"offset": "0x000000a8", "guid": "00023000-**************-000000000000", "name": "Unknown GUID", "bytes": "00300200003002000000000000000000"}, {"offset": "0x000000d4", "guid": "00000400-5CD1-000E-0A00-000000000000", "name": "Unknown GUID", "bytes": "00040000D15C0E000A00000000000000"}, {"offset": "0x0000012c", "guid": "00002DA0-4000-0008-0A00-000000000000", "name": "Unknown GUID", "bytes": "A02D0000004008000A00000000000000"}, {"offset": "0x00000194", "guid": "00005000-DC00-0001-0004-000000000000", "name": "Unknown GUID", "bytes": "0050000000DC01000004000000000000"}, {"offset": "0x000001ac", "guid": "40000040-742E-7865-7400-000061030600", "name": "Unknown GUID", "bytes": "400000402E7465787400000061030600"}, {"offset": "0x000001b4", "guid": "00000074-0361-0006-0030-020000040600", "name": "Unknown GUID", "bytes": "74000000610306000030020000040600"}, {"offset": "0x000001b8", "guid": "00060361-**************-060000E00100", "name": "Unknown GUID", "bytes": "61030600003002000004060000E00100"}, {"offset": "0x000001bc", "guid": "00023000-0400-0006-00E0-010000000000", "name": "Unknown GUID", "bytes": "003002000004060000E0010000000000"}, {"offset": "0x000001c0", "guid": "00060400-E000-0001-0000-000000000000", "name": "Unknown GUID", "bytes": "0004060000E001000000000000000000"}, {"offset": "0x000001d4", "guid": "60000020-722E-6C65-6F63-00000A000000", "name": "Unknown GUID", "bytes": "200000602E72656C6F6300000A000000"}, {"offset": "0x000001e0", "guid": "0000000A-**************-000000E40700", "name": "Unknown GUID", "bytes": "0A000000004008000002000000E40700"}, {"offset": "0x000001e8", "guid": "00000200-E400-0007-0000-000000000000", "name": "Unknown GUID", "bytes": "0002000000E407000000000000000000"}, {"offset": "0x000001fc", "guid": "42000040-312F-0034-0000-00006B000000", "name": "Unknown GUID", "bytes": "400000422F313400000000006B000000"}, {"offset": "0x00000208", "guid": "0000006B-**************-000000E60700", "name": "Unknown GUID", "bytes": "6B000000005008000002000000E60700"}, {"offset": "0x00000210", "guid": "00000200-E600-0007-0000-000000000000", "name": "Unknown GUID", "bytes": "0002000000E607000000000000000000"}, {"offset": "0x00000224", "guid": "C0000040-322F-0036-0000-00006A000000", "name": "Unknown GUID", "bytes": "400000C02F323600000000006A000000"}, {"offset": "0x00000230", "guid": "0000006A-**************-000000E80700", "name": "Unknown GUID", "bytes": "6A000000006008000002000000E80700"}, {"offset": "0x00000238", "guid": "00000200-E800-0007-0000-000000000000", "name": "Unknown GUID", "bytes": "0002000000E807000000000000000000"}, {"offset": "0x0000024c", "guid": "40000040-642E-7461-6100-000018DB0200", "name": "Unknown GUID", "bytes": "400000402E6461746100000018DB0200"}, {"offset": "0x00000254", "guid": "00000061-DB18-0002-0070-080000DC0200", "name": "Unknown GUID", "bytes": "6100000018DB02000070080000DC0200"}, {"offset": "0x00000258", "guid": "0002DB18-7000-0008-00DC-020000EA0700", "name": "Unknown GUID", "bytes": "18DB02000070080000DC020000EA0700"}, {"offset": "0x0000025c", "guid": "00087000-DC00-0002-00EA-070000000000", "name": "Unknown GUID", "bytes": "0070080000DC020000EA070000000000"}, {"offset": "0x00000260", "guid": "0002DC00-EA00-0007-0000-000000000000", "name": "Unknown GUID", "bytes": "00DC020000EA07000000000000000000"}, {"offset": "0x00000274", "guid": "C0000040-332F-0037-0000-0000C21D0000", "name": "Unknown GUID", "bytes": "400000C02F33370000000000C21D0000"}, {"offset": "0x00000280", "guid": "00001DC2-5000-000B-001E-000000C60A00", "name": "Unknown GUID", "bytes": "C21D000000500B00001E000000C60A00"}, {"offset": "0x00000288", "guid": "00001E00-C600-000A-0000-000000000000", "name": "Unknown GUID", "bytes": "001E000000C60A000000000000000000"}, {"offset": "0x0000029c", "guid": "40000040-642E-6E79-616D-************", "name": "Unknown GUID", "bytes": "400000402E64796E616D************"}, {"offset": "0x000002a0", "guid": "6E79642E-6D61-6369-0001-000000700B00", "name": "Unknown GUID", "bytes": "2E64796E616D************00700B00"}, {"offset": "0x000002a8", "guid": "00000100-7000-000B-0002-000000E40A00", "name": "Unknown GUID", "bytes": "0001000000700B000002000000E40A00"}, {"offset": "0x000002b0", "guid": "00000200-E400-000A-0000-000000000000", "name": "Unknown GUID", "bytes": "0002000000E40A000000000000000000"}, {"offset": "0x000002c4", "guid": "C0000040-722E-6C65-6100-000038B40100", "name": "Unknown GUID", "bytes": "400000C02E72656C6100000038B40100"}, {"offset": "0x000002cc", "guid": "00000061-B438-0001-0080-0B0000B60100", "name": "Unknown GUID", "bytes": "6100000038B4010000800B0000B60100"}, {"offset": "0x000002d0", "guid": "0001B438-8000-000B-00B6-010000E60A00", "name": "Unknown GUID", "bytes": "38B4010000800B0000B6010000E60A00"}, {"offset": "0x000002d4", "guid": "000B8000-B600-0001-00E6-0A0000000000", "name": "Unknown GUID", "bytes": "00800B0000B6010000E60A0000000000"}, {"offset": "0x000002d8", "guid": "0001B600-E600-000A-0000-000000000000", "name": "Unknown GUID", "bytes": "00B6010000E60A000000000000000000"}, {"offset": "0x000002ec", "guid": "40000040-732E-6162-7400-0000C4000000", "name": "Unknown GUID", "bytes": "400000402E73626174000000C4000000"}, {"offset": "0x000002f8", "guid": "000000C4-4000-000D-0002-0000009C0C00", "name": "Unknown GUID", "bytes": "C400000000400D0000020000009C0C00"}, {"offset": "0x00000300", "guid": "00000200-9C00-000C-0000-000000000000", "name": "Unknown GUID", "bytes": "00020000009C0C000000000000000000"}, {"offset": "0x00000408", "guid": "00527A01-7801-0110-1B0C-070890010000", "name": "Unknown GUID", "bytes": "017A5200017810011B0C070890010000"}, {"offset": "0x0000040c", "guid": "01107801-0C1B-0807-9001-000010000000", "name": "Unknown GUID", "bytes": "017810011B0C07089001000010000000"}, {"offset": "0x0000041c", "guid": "0000001C-E010-0001-0300-000000000000", "name": "Unknown GUID", "bytes": "1C00000010E001000300000000000000"}, {"offset": "0x00000430", "guid": "00000030-DFFF-0001-4400-000000000000", "name": "Unknown GUID", "bytes": "30000000FFDF01004400000000000000"}, {"offset": "0x00000444", "guid": "00000044-E02F-0001-4E00-000000000000", "name": "Unknown GUID", "bytes": "440000002FE001004E00000000000000"}, {"offset": "0x00000458", "guid": "00000058-E069-0001-0D00-000000410E10", "name": "Unknown GUID", "bytes": "5800000069E001000D00000000410E10"}, {"offset": "0x00000460", "guid": "0000000D-4100-100E-4B0E-0800DC000000", "name": "Unknown GUID", "bytes": "0D00000000410E104B0E0800DC000000"}, {"offset": "0x00000464", "guid": "100E4100-0E4B-0008-DC00-000070000000", "name": "Unknown GUID", "bytes": "00410E104B0E0800DC00000070000000"}, {"offset": "0x00000470", "guid": "00000070-E05E-0001-9503-000000420E10", "name": "Unknown GUID", "bytes": "700000005EE001009503000000420E10"}, {"offset": "0x00000478", "guid": "00000395-4200-100E-8F02-420E188E0345", "name": "Unknown GUID", "bytes": "9503000000420E108F02420E188E0345"}, {"offset": "0x0000047c", "guid": "100E4200-028F-0E42-188E-03450E208D04", "name": "Unknown GUID", "bytes": "00420E108F02420E188E03450E208D04"}, {"offset": "0x00000480", "guid": "0E42028F-8E18-4503-0E20-8D04420E288C", "name": "Unknown GUID", "bytes": "8F02420E188E03450E208D04420E288C"}, {"offset": "0x00000484", "guid": "45038E18-200E-048D-420E-288C05410E30", "name": "Unknown GUID", "bytes": "188E03450E208D04420E288C05410E30"}, {"offset": "0x00000488", "guid": "048D200E-0E42-8C28-0541-0E308606410E", "name": "Unknown GUID", "bytes": "0E208D04420E288C05410E308606410E"}, {"offset": "0x0000048c", "guid": "8C280E42-4105-300E-8606-410E38830747", "name": "Unknown GUID", "bytes": "420E288C05410E308606410E38830747"}, {"offset": "0x00000490", "guid": "300E4105-0686-0E41-3883-07470EA00102", "name": "Unknown GUID", "bytes": "05410E308606410E388307470EA00102"}, {"offset": "0x00000494", "guid": "0E410686-8338-4707-0EA0-01028F0EA801", "name": "Unknown GUID", "bytes": "8606410E388307470EA001028F0EA801"}, {"offset": "0x00000498", "guid": "47078338-A00E-0201-8F0E-A801490EB001", "name": "Unknown GUID", "bytes": "388307470EA001028F0EA801490EB001"}, {"offset": "0x0000049c", "guid": "0201A00E-0E8F-01A8-490E-B0015C0ED001", "name": "Unknown GUID", "bytes": "0EA001028F0EA801490EB0015C0ED001"}, {"offset": "0x000004a0", "guid": "01A80E8F-0E49-01B0-5C0E-D001490EA001", "name": "Unknown GUID", "bytes": "8F0EA801490EB0015C0ED001490EA001"}, {"offset": "0x000004a4", "guid": "01B00E49-0E5C-01D0-490E-A0017C0EA801", "name": "Unknown GUID", "bytes": "490EB0015C0ED001490EA0017C0EA801"}, {"offset": "0x000004a8", "guid": "01D00E5C-0E49-01A0-7C0E-A801420EB001", "name": "Unknown GUID", "bytes": "5C0ED001490EA0017C0EA801420EB001"}, {"offset": "0x000004ac", "guid": "01A00E49-0E7C-01A8-420E-B0015C0EB801", "name": "Unknown GUID", "bytes": "490EA0017C0EA801420EB0015C0EB801"}, {"offset": "0x000004b0", "guid": "01A80E7C-0E42-01B0-5C0E-B801410EC001", "name": "Unknown GUID", "bytes": "7C0EA801420EB0015C0EB801410EC001"}, {"offset": "0x000004b4", "guid": "01B00E42-0E5C-01B8-410E-C0014B0EE001", "name": "Unknown GUID", "bytes": "420EB0015C0EB801410EC0014B0EE001"}, {"offset": "0x000004b8", "guid": "01B80E5C-0E41-01C0-4B0E-E001490EA001", "name": "Unknown GUID", "bytes": "5C0EB801410EC0014B0EE001490EA001"}, {"offset": "0x000004bc", "guid": "01C00E41-0E4B-01E0-490E-A001640EA801", "name": "Unknown GUID", "bytes": "410EC0014B0EE001490EA001640EA801"}, {"offset": "0x000004c0", "guid": "01E00E4B-0E49-01A0-640E-A801410EB001", "name": "Unknown GUID", "bytes": "4B0EE001490EA001640EA801410EB001"}, {"offset": "0x000004c4", "guid": "01A00E49-0E64-01A8-410E-B0015F0ED001", "name": "Unknown GUID", "bytes": "490EA001640EA801410EB0015F0ED001"}, {"offset": "0x000004c8", "guid": "01A80E64-0E41-01B0-5F0E-D001490EA001", "name": "Unknown GUID", "bytes": "640EA801410EB0015F0ED001490EA001"}, {"offset": "0x000004cc", "guid": "01B00E41-0E5F-01D0-490E-A00102C40EA8", "name": "Unknown GUID", "bytes": "410EB0015F0ED001490EA00102C40EA8"}, {"offset": "0x000004d0", "guid": "01D00E5F-0E49-01A0-02C4-0EA801480EB0", "name": "Unknown GUID", "bytes": "5F0ED001490EA00102C40EA801480EB0"}, {"offset": "0x000004d4", "guid": "01A00E49-C402-A80E-0148-0EB001440ED0", "name": "Unknown GUID", "bytes": "490EA00102C40EA801480EB001440ED0"}, {"offset": "0x000004d8", "guid": "A80EC402-4801-B00E-0144-0ED0014D0EA0", "name": "Unknown GUID", "bytes": "02C40EA801480EB001440ED0014D0EA0"}, {"offset": "0x000004dc", "guid": "B00E4801-4401-D00E-014D-0EA001540EA8", "name": "Unknown GUID", "bytes": "01480EB001440ED0014D0EA001540EA8"}, {"offset": "0x000004e0", "guid": "D00E4401-4D01-A00E-0154-0EA801480EB0", "name": "Unknown GUID", "bytes": "01440ED0014D0EA001540EA801480EB0"}, {"offset": "0x000004e4", "guid": "A00E4D01-5401-A80E-0148-0EB001580ED0", "name": "Unknown GUID", "bytes": "014D0EA001540EA801480EB001580ED0"}, {"offset": "0x000004e8", "guid": "A80E5401-4801-B00E-0158-0ED001490EA0", "name": "Unknown GUID", "bytes": "01540EA801480EB001580ED001490EA0"}, {"offset": "0x000004ec", "guid": "B00E4801-5801-D00E-0149-0EA001670EA8", "name": "Unknown GUID", "bytes": "01480EB001580ED001490EA001670EA8"}, {"offset": "0x000004f0", "guid": "D00E5801-4901-A00E-0167-0EA801420EB0", "name": "Unknown GUID", "bytes": "01580ED001490EA001670EA801420EB0"}, {"offset": "0x000004f4", "guid": "A00E4901-6701-A80E-0142-0EB0015E0ED0", "name": "Unknown GUID", "bytes": "01490EA001670EA801420EB0015E0ED0"}, {"offset": "0x000004f8", "guid": "A80E6701-4201-B00E-015E-0ED001490EA0", "name": "Unknown GUID", "bytes": "01670EA801420EB0015E0ED001490EA0"}, {"offset": "0x000004fc", "guid": "B00E4201-5E01-D00E-0149-0EA001540EC0", "name": "Unknown GUID", "bytes": "01420EB0015E0ED001490EA001540EC0"}], "imports": [], "function_patterns": [{"offset": "0x00072984", "pattern": "5C7835355C7834385C7838395C786535", "description": "push rbp; mov rbp, rsp", "type": "function_prologue"}, {"offset": "0x0001e000", "pattern": "5C7834385C7838335C786563", "description": "sub rsp, imm8", "type": "function_prologue"}, {"offset": "0x0001e0e2", "pattern": "5C7834385C7838335C786563", "description": "sub rsp, imm8", "type": "function_prologue"}, {"offset": "0x0001e196", "pattern": "5C7834385C7838335C786563", "description": "sub rsp, imm8", "type": "function_prologue"}, {"offset": "0x0001e205", "pattern": "5C7834385C7838335C786563", "description": "sub rsp, imm8", "type": "function_prologue"}, {"offset": "0x0001e252", "pattern": "5C7834385C7838335C786563", "description": "sub rsp, imm8", "type": "function_prologue"}, {"offset": "0x0001e32b", "pattern": "5C7834385C7838335C786563", "description": "sub rsp, imm8", "type": "function_prologue"}, {"offset": "0x0001e36c", "pattern": "5C7834385C7838335C786563", "description": "sub rsp, imm8", "type": "function_prologue"}, {"offset": "0x0001e3bc", "pattern": "5C7834385C7838335C786563", "description": "sub rsp, imm8", "type": "function_prologue"}, {"offset": "0x0001e3d9", "pattern": "5C7834385C7838335C786563", "description": "sub rsp, imm8", "type": "function_prologue"}, {"offset": "0x0001e402", "pattern": "5C7834385C7838335C786563", "description": "sub rsp, imm8", "type": "function_prologue"}, {"offset": "0x0001e436", "pattern": "5C7834385C7838335C786563", "description": "sub rsp, imm8", "type": "function_prologue"}, {"offset": "0x0001e483", "pattern": "5C7834385C7838335C786563", "description": "sub rsp, imm8", "type": "function_prologue"}, {"offset": "0x0001e4f7", "pattern": "5C7834385C7838335C786563", "description": "sub rsp, imm8", "type": "function_prologue"}, {"offset": "0x0001e604", "pattern": "5C7834385C7838335C786563", "description": "sub rsp, imm8", "type": "function_prologue"}, {"offset": "0x0001e68f", "pattern": "5C7834385C7838335C786563", "description": "sub rsp, imm8", "type": "function_prologue"}, {"offset": "0x0001e6f0", "pattern": "5C7834385C7838335C786563", "description": "sub rsp, imm8", "type": "function_prologue"}, {"offset": "0x0001e759", "pattern": "5C7834385C7838335C786563", "description": "sub rsp, imm8", "type": "function_prologue"}, {"offset": "0x0001e7b5", "pattern": "5C7834385C7838335C786563", "description": "sub rsp, imm8", "type": "function_prologue"}, {"offset": "0x0001e81b", "pattern": "5C7834385C7838335C786563", "description": "sub rsp, imm8", "type": "function_prologue"}, {"offset": "0x0001e881", "pattern": "5C7834385C7838335C786563", "description": "sub rsp, imm8", "type": "function_prologue"}, {"offset": "0x0001e8d9", "pattern": "5C7834385C7838335C786563", "description": "sub rsp, imm8", "type": "function_prologue"}, {"offset": "0x0001e944", "pattern": "5C7834385C7838335C786563", "description": "sub rsp, imm8", "type": "function_prologue"}, {"offset": "0x0001e999", "pattern": "5C7834385C7838335C786563", "description": "sub rsp, imm8", "type": "function_prologue"}, {"offset": "0x0001ea4c", "pattern": "5C7834385C7838335C786563", "description": "sub rsp, imm8", "type": "function_prologue"}, {"offset": "0x0001eabf", "pattern": "5C7834385C7838335C786563", "description": "sub rsp, imm8", "type": "function_prologue"}, {"offset": "0x0001eb26", "pattern": "5C7834385C7838335C786563", "description": "sub rsp, imm8", "type": "function_prologue"}, {"offset": "0x0001eb82", "pattern": "5C7834385C7838335C786563", "description": "sub rsp, imm8", "type": "function_prologue"}, {"offset": "0x0001ebcf", "pattern": "5C7834385C7838335C786563", "description": "sub rsp, imm8", "type": "function_prologue"}, {"offset": "0x0001ecc7", "pattern": "5C7834385C7838335C786563", "description": "sub rsp, imm8", "type": "function_prologue"}, {"offset": "0x0001ed33", "pattern": "5C7834385C7838335C786563", "description": "sub rsp, imm8", "type": "function_prologue"}, {"offset": "0x0001eded", "pattern": "5C7834385C7838335C786563", "description": "sub rsp, imm8", "type": "function_prologue"}, {"offset": "0x0001ee2a", "pattern": "5C7834385C7838335C786563", "description": "sub rsp, imm8", "type": "function_prologue"}, {"offset": "0x0001ee6a", "pattern": "5C7834385C7838335C786563", "description": "sub rsp, imm8", "type": "function_prologue"}, {"offset": "0x0001eee5", "pattern": "5C7834385C7838335C786563", "description": "sub rsp, imm8", "type": "function_prologue"}, {"offset": "0x0001ef24", "pattern": "5C7834385C7838335C786563", "description": "sub rsp, imm8", "type": "function_prologue"}, {"offset": "0x0001ef46", "pattern": "5C7834385C7838335C786563", "description": "sub rsp, imm8", "type": "function_prologue"}, {"offset": "0x0001efc6", "pattern": "5C7834385C7838335C786563", "description": "sub rsp, imm8", "type": "function_prologue"}, {"offset": "0x0001efe8", "pattern": "5C7834385C7838335C786563", "description": "sub rsp, imm8", "type": "function_prologue"}, {"offset": "0x0001f02c", "pattern": "5C7834385C7838335C786563", "description": "sub rsp, imm8", "type": "function_prologue"}, {"offset": "0x0001f04e", "pattern": "5C7834385C7838335C786563", "description": "sub rsp, imm8", "type": "function_prologue"}, {"offset": "0x0001f093", "pattern": "5C7834385C7838335C786563", "description": "sub rsp, imm8", "type": "function_prologue"}, {"offset": "0x0001f0b5", "pattern": "5C7834385C7838335C786563", "description": "sub rsp, imm8", "type": "function_prologue"}, {"offset": "0x0001f0f1", "pattern": "5C7834385C7838335C786563", "description": "sub rsp, imm8", "type": "function_prologue"}, {"offset": "0x0001f113", "pattern": "5C7834385C7838335C786563", "description": "sub rsp, imm8", "type": "function_prologue"}, {"offset": "0x0001f178", "pattern": "5C7834385C7838335C786563", "description": "sub rsp, imm8", "type": "function_prologue"}, {"offset": "0x0001f1b9", "pattern": "5C7834385C7838335C786563", "description": "sub rsp, imm8", "type": "function_prologue"}, {"offset": "0x0001f1e5", "pattern": "5C7834385C7838335C786563", "description": "sub rsp, imm8", "type": "function_prologue"}, {"offset": "0x0001f271", "pattern": "5C7834385C7838335C786563", "description": "sub rsp, imm8", "type": "function_prologue"}, {"offset": "0x0001f2e2", "pattern": "5C7834385C7838335C786563", "description": "sub rsp, imm8", "type": "function_prologue"}, {"offset": "0x0001f322", "pattern": "5C7834385C7838335C786563", "description": "sub rsp, imm8", "type": "function_prologue"}, {"offset": "0x0001f352", "pattern": "5C7834385C7838335C786563", "description": "sub rsp, imm8", "type": "function_prologue"}, {"offset": "0x0001f3c0", "pattern": "5C7834385C7838335C786563", "description": "sub rsp, imm8", "type": "function_prologue"}, {"offset": "0x0001f425", "pattern": "5C7834385C7838335C786563", "description": "sub rsp, imm8", "type": "function_prologue"}, {"offset": "0x0001f457", "pattern": "5C7834385C7838335C786563", "description": "sub rsp, imm8", "type": "function_prologue"}, {"offset": "0x0001f4af", "pattern": "5C7834385C7838335C786563", "description": "sub rsp, imm8", "type": "function_prologue"}, {"offset": "0x0001f52a", "pattern": "5C7834385C7838335C786563", "description": "sub rsp, imm8", "type": "function_prologue"}, {"offset": "0x0001f56d", "pattern": "5C7834385C7838335C786563", "description": "sub rsp, imm8", "type": "function_prologue"}, {"offset": "0x0001f58f", "pattern": "5C7834385C7838335C786563", "description": "sub rsp, imm8", "type": "function_prologue"}, {"offset": "0x0001f5eb", "pattern": "5C7834385C7838335C786563", "description": "sub rsp, imm8", "type": "function_prologue"}, {"offset": "0x0001f60d", "pattern": "5C7834385C7838335C786563", "description": "sub rsp, imm8", "type": "function_prologue"}, {"offset": "0x0001f64e", "pattern": "5C7834385C7838335C786563", "description": "sub rsp, imm8", "type": "function_prologue"}, {"offset": "0x0001f670", "pattern": "5C7834385C7838335C786563", "description": "sub rsp, imm8", "type": "function_prologue"}, {"offset": "0x0001f6e5", "pattern": "5C7834385C7838335C786563", "description": "sub rsp, imm8", "type": "function_prologue"}, {"offset": "0x0001f71b", "pattern": "5C7834385C7838335C786563", "description": "sub rsp, imm8", "type": "function_prologue"}, {"offset": "0x0001f73d", "pattern": "5C7834385C7838335C786563", "description": "sub rsp, imm8", "type": "function_prologue"}, {"offset": "0x0001f8db", "pattern": "5C7834385C7838335C786563", "description": "sub rsp, imm8", "type": "function_prologue"}, {"offset": "0x0001f951", "pattern": "5C7834385C7838335C786563", "description": "sub rsp, imm8", "type": "function_prologue"}, {"offset": "0x0001f9a4", "pattern": "5C7834385C7838335C786563", "description": "sub rsp, imm8", "type": "function_prologue"}, {"offset": "0x0001f9c6", "pattern": "5C7834385C7838335C786563", "description": "sub rsp, imm8", "type": "function_prologue"}, {"offset": "0x0001fa0a", "pattern": "5C7834385C7838335C786563", "description": "sub rsp, imm8", "type": "function_prologue"}, {"offset": "0x0001fa3a", "pattern": "5C7834385C7838335C786563", "description": "sub rsp, imm8", "type": "function_prologue"}, {"offset": "0x0001fa99", "pattern": "5C7834385C7838335C786563", "description": "sub rsp, imm8", "type": "function_prologue"}, {"offset": "0x0001fac6", "pattern": "5C7834385C7838335C786563", "description": "sub rsp, imm8", "type": "function_prologue"}, {"offset": "0x0001fb1b", "pattern": "5C7834385C7838335C786563", "description": "sub rsp, imm8", "type": "function_prologue"}, {"offset": "0x0001fb48", "pattern": "5C7834385C7838335C786563", "description": "sub rsp, imm8", "type": "function_prologue"}, {"offset": "0x0001fba8", "pattern": "5C7834385C7838335C786563", "description": "sub rsp, imm8", "type": "function_prologue"}, {"offset": "0x0001fbe3", "pattern": "5C7834385C7838335C786563", "description": "sub rsp, imm8", "type": "function_prologue"}, {"offset": "0x0001fc4d", "pattern": "5C7834385C7838335C786563", "description": "sub rsp, imm8", "type": "function_prologue"}, {"offset": "0x0001fc61", "pattern": "5C7834385C7838335C786563", "description": "sub rsp, imm8", "type": "function_prologue"}, {"offset": "0x0001fc9c", "pattern": "5C7834385C7838335C786563", "description": "sub rsp, imm8", "type": "function_prologue"}, {"offset": "0x0001fcc8", "pattern": "5C7834385C7838335C786563", "description": "sub rsp, imm8", "type": "function_prologue"}, {"offset": "0x0001fce7", "pattern": "5C7834385C7838335C786563", "description": "sub rsp, imm8", "type": "function_prologue"}, {"offset": "0x0001fd10", "pattern": "5C7834385C7838335C786563", "description": "sub rsp, imm8", "type": "function_prologue"}, {"offset": "0x0001fd3c", "pattern": "5C7834385C7838335C786563", "description": "sub rsp, imm8", "type": "function_prologue"}, {"offset": "0x0001fd67", "pattern": "5C7834385C7838335C786563", "description": "sub rsp, imm8", "type": "function_prologue"}, {"offset": "0x0001fd90", "pattern": "5C7834385C7838335C786563", "description": "sub rsp, imm8", "type": "function_prologue"}, {"offset": "0x0001fdc0", "pattern": "5C7834385C7838335C786563", "description": "sub rsp, imm8", "type": "function_prologue"}, {"offset": "0x0001fdfa", "pattern": "5C7834385C7838335C786563", "description": "sub rsp, imm8", "type": "function_prologue"}, {"offset": "0x0001fe1c", "pattern": "5C7834385C7838335C786563", "description": "sub rsp, imm8", "type": "function_prologue"}, {"offset": "0x0001fe4a", "pattern": "5C7834385C7838335C786563", "description": "sub rsp, imm8", "type": "function_prologue"}, {"offset": "0x0001fea5", "pattern": "5C7834385C7838335C786563", "description": "sub rsp, imm8", "type": "function_prologue"}, {"offset": "0x0001fec7", "pattern": "5C7834385C7838335C786563", "description": "sub rsp, imm8", "type": "function_prologue"}, {"offset": "0x0001feed", "pattern": "5C7834385C7838335C786563", "description": "sub rsp, imm8", "type": "function_prologue"}, {"offset": "0x0001ff23", "pattern": "5C7834385C7838335C786563", "description": "sub rsp, imm8", "type": "function_prologue"}, {"offset": "0x0001ff4f", "pattern": "5C7834385C7838335C786563", "description": "sub rsp, imm8", "type": "function_prologue"}, {"offset": "0x0001ff8f", "pattern": "5C7834385C7838335C786563", "description": "sub rsp, imm8", "type": "function_prologue"}, {"offset": "0x0001ffb1", "pattern": "5C7834385C7838335C786563", "description": "sub rsp, imm8", "type": "function_prologue"}, {"offset": "0x0001ffda", "pattern": "5C7834385C7838335C786563", "description": "sub rsp, imm8", "type": "function_prologue"}, {"offset": "0x00020012", "pattern": "5C7834385C7838335C786563", "description": "sub rsp, imm8", "type": "function_prologue"}, {"offset": "0x0002004a", "pattern": "5C7834385C7838335C786563", "description": "sub rsp, imm8", "type": "function_prologue"}, {"offset": "0x00020079", "pattern": "5C7834385C7838335C786563", "description": "sub rsp, imm8", "type": "function_prologue"}, {"offset": "0x000200e3", "pattern": "5C7834385C7838335C786563", "description": "sub rsp, imm8", "type": "function_prologue"}, {"offset": "0x00020113", "pattern": "5C7834385C7838335C786563", "description": "sub rsp, imm8", "type": "function_prologue"}, {"offset": "0x00020141", "pattern": "5C7834385C7838335C786563", "description": "sub rsp, imm8", "type": "function_prologue"}, {"offset": "0x0002018d", "pattern": "5C7834385C7838335C786563", "description": "sub rsp, imm8", "type": "function_prologue"}, {"offset": "0x000201d8", "pattern": "5C7834385C7838335C786563", "description": "sub rsp, imm8", "type": "function_prologue"}, {"offset": "0x000201fa", "pattern": "5C7834385C7838335C786563", "description": "sub rsp, imm8", "type": "function_prologue"}, {"offset": "0x00020253", "pattern": "5C7834385C7838335C786563", "description": "sub rsp, imm8", "type": "function_prologue"}, {"offset": "0x0002027f", "pattern": "5C7834385C7838335C786563", "description": "sub rsp, imm8", "type": "function_prologue"}, {"offset": "0x0002029f", "pattern": "5C7834385C7838335C786563", "description": "sub rsp, imm8", "type": "function_prologue"}, {"offset": "0x000202f7", "pattern": "5C7834385C7838335C786563", "description": "sub rsp, imm8", "type": "function_prologue"}, {"offset": "0x00020352", "pattern": "5C7834385C7838335C786563", "description": "sub rsp, imm8", "type": "function_prologue"}, {"offset": "0x0002037f", "pattern": "5C7834385C7838335C786563", "description": "sub rsp, imm8", "type": "function_prologue"}, {"offset": "0x0002045c", "pattern": "5C7834385C7838335C786563", "description": "sub rsp, imm8", "type": "function_prologue"}, {"offset": "0x00020474", "pattern": "5C7834385C7838335C786563", "description": "sub rsp, imm8", "type": "function_prologue"}, {"offset": "0x0002051d", "pattern": "5C7834385C7838335C786563", "description": "sub rsp, imm8", "type": "function_prologue"}, {"offset": "0x00020571", "pattern": "5C7834385C7838335C786563", "description": "sub rsp, imm8", "type": "function_prologue"}, {"offset": "0x00020594", "pattern": "5C7834385C7838335C786563", "description": "sub rsp, imm8", "type": "function_prologue"}, {"offset": "0x000205d1", "pattern": "5C7834385C7838335C786563", "description": "sub rsp, imm8", "type": "function_prologue"}, {"offset": "0x000205fd", "pattern": "5C7834385C7838335C786563", "description": "sub rsp, imm8", "type": "function_prologue"}, {"offset": "0x00020619", "pattern": "5C7834385C7838335C786563", "description": "sub rsp, imm8", "type": "function_prologue"}, {"offset": "0x00020642", "pattern": "5C7834385C7838335C786563", "description": "sub rsp, imm8", "type": "function_prologue"}, {"offset": "0x0002066e", "pattern": "5C7834385C7838335C786563", "description": "sub rsp, imm8", "type": "function_prologue"}, {"offset": "0x0002069c", "pattern": "5C7834385C7838335C786563", "description": "sub rsp, imm8", "type": "function_prologue"}, {"offset": "0x000206e3", "pattern": "5C7834385C7838335C786563", "description": "sub rsp, imm8", "type": "function_prologue"}, {"offset": "0x00020750", "pattern": "5C7834385C7838335C786563", "description": "sub rsp, imm8", "type": "function_prologue"}, {"offset": "0x0002077c", "pattern": "5C7834385C7838335C786563", "description": "sub rsp, imm8", "type": "function_prologue"}, {"offset": "0x000207b0", "pattern": "5C7834385C7838335C786563", "description": "sub rsp, imm8", "type": "function_prologue"}, {"offset": "0x000207dc", "pattern": "5C7834385C7838335C786563", "description": "sub rsp, imm8", "type": "function_prologue"}, {"offset": "0x000207f6", "pattern": "5C7834385C7838335C786563", "description": "sub rsp, imm8", "type": "function_prologue"}, {"offset": "0x0002081c", "pattern": "5C7834385C7838335C786563", "description": "sub rsp, imm8", "type": "function_prologue"}, {"offset": "0x00020871", "pattern": "5C7834385C7838335C786563", "description": "sub rsp, imm8", "type": "function_prologue"}, {"offset": "0x0002096e", "pattern": "5C7834385C7838335C786563", "description": "sub rsp, imm8", "type": "function_prologue"}, {"offset": "0x000209a2", "pattern": "5C7834385C7838335C786563", "description": "sub rsp, imm8", "type": "function_prologue"}, {"offset": "0x000209f5", "pattern": "5C7834385C7838335C786563", "description": "sub rsp, imm8", "type": "function_prologue"}, {"offset": "0x00020b0d", "pattern": "5C7834385C7838335C786563", "description": "sub rsp, imm8", "type": "function_prologue"}, {"offset": "0x00020b4d", "pattern": "5C7834385C7838335C786563", "description": "sub rsp, imm8", "type": "function_prologue"}, {"offset": "0x00020b9c", "pattern": "5C7834385C7838335C786563", "description": "sub rsp, imm8", "type": "function_prologue"}, {"offset": "0x00020be9", "pattern": "5C7834385C7838335C786563", "description": "sub rsp, imm8", "type": "function_prologue"}, {"offset": "0x00020c23", "pattern": "5C7834385C7838335C786563", "description": "sub rsp, imm8", "type": "function_prologue"}, {"offset": "0x00020cff", "pattern": "5C7834385C7838335C786563", "description": "sub rsp, imm8", "type": "function_prologue"}, {"offset": "0x00020d48", "pattern": "5C7834385C7838335C786563", "description": "sub rsp, imm8", "type": "function_prologue"}, {"offset": "0x00020da3", "pattern": "5C7834385C7838335C786563", "description": "sub rsp, imm8", "type": "function_prologue"}, {"offset": "0x00020dc5", "pattern": "5C7834385C7838335C786563", "description": "sub rsp, imm8", "type": "function_prologue"}, {"offset": "0x00020e03", "pattern": "5C7834385C7838335C786563", "description": "sub rsp, imm8", "type": "function_prologue"}, {"offset": "0x00020e57", "pattern": "5C7834385C7838335C786563", "description": "sub rsp, imm8", "type": "function_prologue"}, {"offset": "0x00020e89", "pattern": "5C7834385C7838335C786563", "description": "sub rsp, imm8", "type": "function_prologue"}, {"offset": "0x00020eb2", "pattern": "5C7834385C7838335C786563", "description": "sub rsp, imm8", "type": "function_prologue"}, {"offset": "0x00020edf", "pattern": "5C7834385C7838335C786563", "description": "sub rsp, imm8", "type": "function_prologue"}, {"offset": "0x00020f0a", "pattern": "5C7834385C7838335C786563", "description": "sub rsp, imm8", "type": "function_prologue"}, {"offset": "0x00020f31", "pattern": "5C7834385C7838335C786563", "description": "sub rsp, imm8", "type": "function_prologue"}, {"offset": "0x00020f6c", "pattern": "5C7834385C7838335C786563", "description": "sub rsp, imm8", "type": "function_prologue"}, {"offset": "0x00020fa4", "pattern": "5C7834385C7838335C786563", "description": "sub rsp, imm8", "type": "function_prologue"}, {"offset": "0x00020fdb", "pattern": "5C7834385C7838335C786563", "description": "sub rsp, imm8", "type": "function_prologue"}, {"offset": "0x00021013", "pattern": "5C7834385C7838335C786563", "description": "sub rsp, imm8", "type": "function_prologue"}, {"offset": "0x00021072", "pattern": "5C7834385C7838335C786563", "description": "sub rsp, imm8", "type": "function_prologue"}, {"offset": "0x000210be", "pattern": "5C7834385C7838335C786563", "description": "sub rsp, imm8", "type": "function_prologue"}, {"offset": "0x000210e1", "pattern": "5C7834385C7838335C786563", "description": "sub rsp, imm8", "type": "function_prologue"}, {"offset": "0x0002111c", "pattern": "5C7834385C7838335C786563", "description": "sub rsp, imm8", "type": "function_prologue"}, {"offset": "0x00021159", "pattern": "5C7834385C7838335C786563", "description": "sub rsp, imm8", "type": "function_prologue"}, {"offset": "0x00021188", "pattern": "5C7834385C7838335C786563", "description": "sub rsp, imm8", "type": "function_prologue"}, {"offset": "0x000211c3", "pattern": "5C7834385C7838335C786563", "description": "sub rsp, imm8", "type": "function_prologue"}, {"offset": "0x00021218", "pattern": "5C7834385C7838335C786563", "description": "sub rsp, imm8", "type": "function_prologue"}, {"offset": "0x0002123f", "pattern": "5C7834385C7838335C786563", "description": "sub rsp, imm8", "type": "function_prologue"}, {"offset": "0x0002127a", "pattern": "5C7834385C7838335C786563", "description": "sub rsp, imm8", "type": "function_prologue"}, {"offset": "0x0002130c", "pattern": "5C7834385C7838335C786563", "description": "sub rsp, imm8", "type": "function_prologue"}, {"offset": "0x00021340", "pattern": "5C7834385C7838335C786563", "description": "sub rsp, imm8", "type": "function_prologue"}, {"offset": "0x0002136c", "pattern": "5C7834385C7838335C786563", "description": "sub rsp, imm8", "type": "function_prologue"}, {"offset": "0x000213e4", "pattern": "5C7834385C7838335C786563", "description": "sub rsp, imm8", "type": "function_prologue"}, {"offset": "0x00021410", "pattern": "5C7834385C7838335C786563", "description": "sub rsp, imm8", "type": "function_prologue"}, {"offset": "0x00021460", "pattern": "5C7834385C7838335C786563", "description": "sub rsp, imm8", "type": "function_prologue"}, {"offset": "0x00021586", "pattern": "5C7834385C7838335C786563", "description": "sub rsp, imm8", "type": "function_prologue"}, {"offset": "0x00021654", "pattern": "5C7834385C7838335C786563", "description": "sub rsp, imm8", "type": "function_prologue"}, {"offset": "0x00021689", "pattern": "5C7834385C7838335C786563", "description": "sub rsp, imm8", "type": "function_prologue"}, {"offset": "0x000216cf", "pattern": "5C7834385C7838335C786563", "description": "sub rsp, imm8", "type": "function_prologue"}, {"offset": "0x000216ff", "pattern": "5C7834385C7838335C786563", "description": "sub rsp, imm8", "type": "function_prologue"}, {"offset": "0x00021740", "pattern": "5C7834385C7838335C786563", "description": "sub rsp, imm8", "type": "function_prologue"}, {"offset": "0x00021781", "pattern": "5C7834385C7838335C786563", "description": "sub rsp, imm8", "type": "function_prologue"}, {"offset": "0x000217b8", "pattern": "5C7834385C7838335C786563", "description": "sub rsp, imm8", "type": "function_prologue"}, {"offset": "0x00021809", "pattern": "5C7834385C7838335C786563", "description": "sub rsp, imm8", "type": "function_prologue"}, {"offset": "0x0002183c", "pattern": "5C7834385C7838335C786563", "description": "sub rsp, imm8", "type": "function_prologue"}, {"offset": "0x00021887", "pattern": "5C7834385C7838335C786563", "description": "sub rsp, imm8", "type": "function_prologue"}, {"offset": "0x000218b3", "pattern": "5C7834385C7838335C786563", "description": "sub rsp, imm8", "type": "function_prologue"}, {"offset": "0x000218e5", "pattern": "5C7834385C7838335C786563", "description": "sub rsp, imm8", "type": "function_prologue"}, {"offset": "0x00021987", "pattern": "5C7834385C7838335C786563", "description": "sub rsp, imm8", "type": "function_prologue"}, {"offset": "0x000219d3", "pattern": "5C7834385C7838335C786563", "description": "sub rsp, imm8", "type": "function_prologue"}, {"offset": "0x00021a08", "pattern": "5C7834385C7838335C786563", "description": "sub rsp, imm8", "type": "function_prologue"}, {"offset": "0x00021a26", "pattern": "5C7834385C7838335C786563", "description": "sub rsp, imm8", "type": "function_prologue"}, {"offset": "0x00021a88", "pattern": "5C7834385C7838335C786563", "description": "sub rsp, imm8", "type": "function_prologue"}, {"offset": "0x00021af1", "pattern": "5C7834385C7838335C786563", "description": "sub rsp, imm8", "type": "function_prologue"}, {"offset": "0x00021b5a", "pattern": "5C7834385C7838335C786563", "description": "sub rsp, imm8", "type": "function_prologue"}, {"offset": "0x00021b8c", "pattern": "5C7834385C7838335C786563", "description": "sub rsp, imm8", "type": "function_prologue"}, {"offset": "0x00021bc0", "pattern": "5C7834385C7838335C786563", "description": "sub rsp, imm8", "type": "function_prologue"}, {"offset": "0x00021bfe", "pattern": "5C7834385C7838335C786563", "description": "sub rsp, imm8", "type": "function_prologue"}, {"offset": "0x00021f66", "pattern": "5C7834385C7838335C786563", "description": "sub rsp, imm8", "type": "function_prologue"}, {"offset": "0x00021f8d", "pattern": "5C7834385C7838335C786563", "description": "sub rsp, imm8", "type": "function_prologue"}, {"offset": "0x00022037", "pattern": "5C7834385C7838335C786563", "description": "sub rsp, imm8", "type": "function_prologue"}, {"offset": "0x000220a1", "pattern": "5C7834385C7838335C786563", "description": "sub rsp, imm8", "type": "function_prologue"}, {"offset": "0x000220f8", "pattern": "5C7834385C7838335C786563", "description": "sub rsp, imm8", "type": "function_prologue"}, {"offset": "0x00021dd4", "pattern": "5C7834385C7838395C7835635C783234", "description": "mov [rsp+offset], rbx", "type": "function_prologue"}, {"offset": "0x000021d8", "pattern": "5C7834305C783533", "description": "push rbx (with REX)", "type": "function_prologue"}, {"offset": "0x0001e0db", "pattern": "5C7834315C783534", "description": "push r12", "type": "function_prologue"}, {"offset": "0x0001e0d9", "pattern": "5C7834315C783535", "description": "push r13", "type": "function_prologue"}, {"offset": "0x0001e0d4", "pattern": "5C7834315C783536", "description": "push r14", "type": "function_prologue"}, {"offset": "0x0001e0d2", "pattern": "5C7834315C783537", "description": "push r15", "type": "function_prologue"}], "statistics": {"total_strings": 323, "efi_protocols": 50, "error_messages": 50, "total_guids": 100, "known_guids": 0, "function_patterns": 206}}