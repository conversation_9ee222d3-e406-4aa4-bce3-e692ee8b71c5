# Ghidra EFI Analysis Script
# Generated by EFI Analysis Helper

# Import Ghidra modules
from ghidra.program.model.symbol import SourceType
from ghidra.program.model.data import DataType
from ghidra.program.model.listing import CodeUnit

def main():
    print("Starting EFI Analysis...")
    
    # Get current program
    program = getCurrentProgram()
    if program is None:
        print("No program loaded")
        return
    
    # Create symbol table reference
    symbolTable = program.getSymbolTable()
    listing = program.getListing()
    
    # Add known EFI GUIDs as labels
    print("Adding EFI GUID labels...")
    guids = [{'offset': '0x0000003c', 'guid': '00000080-1F0E-0EBA-00B4-09CD21B8014C', 'name': 'Unknown GUID', 'bytes': '800000000E1FBA0E00B409CD21B8014C'}, {'offset': '0x00000040', 'guid': '0EBA1F0E-B400-CD09-21B8-014CCD215468', 'name': 'Unknown GUID', 'bytes': '0E1FBA0E00B409CD21B8014CCD215468'}, {'offset': '0x00000044', 'guid': 'CD09B400-B821-4C01-CD21-************', 'name': 'Unknown GUID', 'bytes': '00B409CD21B8014CCD21************'}, {'offset': '0x00000048', 'guid': '4C01B821-21CD-6854-6973-2070726F6772', 'name': 'Unknown GUID', 'bytes': '21B8014CCD21************726F6772'}, {'offset': '0x0000004c', 'guid': '685421CD-7369-7020-726F-6772616D2063', 'name': 'Unknown GUID', 'bytes': 'CD21************726F6772616D2063'}, {'offset': '0x00000050', 'guid': '70207369-6F72-7267-616D-2063616E6E6F', 'name': 'Unknown GUID', 'bytes': '69732070726F6772616D2063616E6E6F'}, {'offset': '0x00000054', 'guid': '72676F72-6D61-6320-616E-6E6F74206265', 'name': 'Unknown GUID', 'bytes': '726F6772616D2063616E6E6F74206265'}, {'offset': '0x00000058', 'guid': '63206D61-6E61-6F6E-7420-62652072756E', 'name': 'Unknown GUID', 'bytes': '616D2063616E6E6F742062652072756E'}, {'offset': '0x0000005c', 'guid': '6F6E6E61-**************-756E20696E20', 'name': 'Unknown GUID', 'bytes': '616E6E6F742062652072756E20696E20'}, {'offset': '0x00000060', 'guid': '*************-6E75-2069-6E20444F5320', 'name': 'Unknown GUID', 'bytes': '742062652072756E20696E20444F5320'}, {'offset': '0x00000064', 'guid': '6E757220-6920-206E-444F-53206D6F6465', 'name': 'Unknown GUID', 'bytes': '2072756E20696E20444F53206D6F6465'}, {'offset': '0x00000068', 'guid': '206E6920-4F44-2053-6D6F-64652E0D0D0A', 'name': 'Unknown GUID', 'bytes': '20696E20444F53206D6F64652E0D0D0A'}, {'offset': '0x0000006c', 'guid': '20534F44-6F6D-6564-2E0D-0D0A24000000', 'name': 'Unknown GUID', 'bytes': '444F53206D6F64652E0D0D0A24000000'}, {'offset': '0x00000070', 'guid': '65646F6D-0D2E-0A0D-2400-000000000000', 'name': 'Unknown GUID', 'bytes': '6D6F64652E0D0D0A2400000000000000'}, {'offset': '0x00000080', 'guid': '00004550-8664-000A-0000-0000009E0C00', 'name': 'Unknown GUID', 'bytes': '5045000064860A0000000000009E0C00'}, {'offset': '0x00000090', 'guid': '00000E62-00F0-0206-0B02-022900040600', 'name': 'Unknown GUID', 'bytes': '620E0000F00006020B02022900040600'}, {'offset': '0x00000094', 'guid': '020600F0-020B-2902-0004-060000960600', 'name': 'Unknown GUID', 'bytes': 'F00006020B0202290004060000960600'}, {'offset': '0x00000098', 'guid': '2902020B-0400-0006-0096-060000000000', 'name': 'Unknown GUID', 'bytes': '0B020229000406000096060000000000'}, {'offset': '0x0000009c', 'guid': '00060400-**************-000000300200', 'name': 'Unknown GUID', 'bytes': '00040600009606000000000000300200'}, {'offset': '0x000000a8', 'guid': '00023000-**************-000000000000', 'name': 'Unknown GUID', 'bytes': '00300200003002000000000000000000'}]  # Limit to first 20 GUIDs
    
    for guid_info in guids:
        try:
            offset = int(guid_info["offset"], 16)
            address = program.getAddressFactory().getDefaultAddressSpace().getAddress(offset)
            if address is not None:
                symbolTable.createLabel(address, guid_info["name"], SourceType.ANALYSIS)
                print(f"Added GUID label: {guid_info['name']} at {guid_info['offset']}")
        except:
            continue
    
    # Add string labels
    print("Adding string labels...")
    efi_protocols = [{'string': 'directory services (X.500)', 'offset': '0x000810cb', 'length': 26, 'type': 'ASCII'}, {'string': 'id-mod-timestamp-protocol', 'offset': '0x000827fd', 'length': 25, 'type': 'ASCII'}, {'string': 'id-regCtrl-protocolEncrKey', 'offset': '0x00082b03', 'length': 26, 'type': 'ASCII'}, {'string': 'serviceLocator', 'offset': '0x00082f19', 'length': 14, 'type': 'ASCII'}, {'string': 'OCSP Service Locator', 'offset': '0x00082f28', 'length': 20, 'type': 'ASCII'}, {'string': 'directory services - algorithms', 'offset': '0x00082f97', 'length': 31, 'type': 'ASCII'}, {'string': 'searchGuide', 'offset': '0x00085717', 'length': 11, 'type': 'ASCII'}, {'string': 'enhancedSearchGuide', 'offset': '0x000858e1', 'length': 19, 'type': 'ASCII'}, {'string': 'protocolInformation', 'offset': '0x000858f5', 'length': 19, 'type': 'ASCII'}, {'string': 'OCSP_SERVICELOC', 'offset': '0x00087365', 'length': 15, 'type': 'ASCII'}]
    
    for string_info in efi_protocols:
        try:
            offset = int(string_info["offset"], 16)
            address = program.getAddressFactory().getDefaultAddressSpace().getAddress(offset)
            if address is not None:
                label_name = "STR_" + string_info["string"][:20].replace(" ", "_").replace("-", "_")
                symbolTable.createLabel(address, label_name, SourceType.ANALYSIS)
                print(f"Added string label: {label_name} at {string_info['offset']}")
        except:
            continue
    
    # Add comments for important strings
    print("Adding comments...")
    error_messages = [{'string': 'error:%08lX:%s:%s:%s', 'offset': '0x00080f97', 'length': 20, 'type': 'ASCII'}, {'string': 'invalidityDate', 'offset': '0x00081c24', 'length': 14, 'type': 'ASCII'}, {'string': 'Invalidity Date', 'offset': '0x00081c33', 'length': 15, 'type': 'ASCII'}, {'string': 'setct-ErrorTBS', 'offset': '0x00083c9e', 'length': 14, 'type': 'ASCII'}, {'string': '<INVALID>', 'offset': '0x0008613b', 'length': 9, 'type': 'ASCII'}, {'string': 'IP Address:<invalid>', 'offset': '0x000867f0', 'length': 20, 'type': 'ASCII'}, {'string': 'Verify error:', 'offset': '0x0008730e', 'length': 13, 'type': 'ASCII'}, {'string': 'ERROR', 'offset': '0x00087651', 'length': 5, 'type': 'ASCII'}, {'string': '%s(%d): OpenSSL internal error, assertion failed: %s', 'offset': '0x00087657', 'length': 52, 'type': 'ASCII'}, {'string': '<<ERROR>>', 'offset': '0x0008769d', 'length': 9, 'type': 'ASCII'}]
    
    for error_info in error_messages:
        try:
            offset = int(error_info["offset"], 16)
            address = program.getAddressFactory().getDefaultAddressSpace().getAddress(offset)
            if address is not None:
                codeUnit = listing.getCodeUnitAt(address)
                if codeUnit is not None:
                    codeUnit.setComment(CodeUnit.EOL_COMMENT, f"Error message: {error_info['string']}")
        except:
            continue
    
    print("EFI Analysis complete!")

if __name__ == "__main__":
    main()
