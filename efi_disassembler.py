#!/usr/bin/env python3
"""
EFI文件反编译分析工具
EFI File Disassembly Analysis Tool
"""

import os
import sys
import struct
import hashlib
import json
import re
from pathlib import Path
from typing import Dict, List, Any, Optional, Tuple

class EFIDisassembler:
    """EFI文件反编译器"""
    
    def __init__(self, file_path: str):
        self.file_path = file_path
        self.file_data = None
        self.pe_header = {}
        self.sections = []
        self.disassembly = {}
        self.functions = []
        self.imports = []
        self.exports = []
        
    def load_file(self) -> bool:
        """加载EFI文件"""
        try:
            with open(self.file_path, 'rb') as f:
                self.file_data = f.read()
            return True
        except Exception as e:
            print(f"Error loading file {self.file_path}: {e}")
            return False
    
    def parse_pe_header(self) -> Dict[str, Any]:
        """解析PE头"""
        if not self.file_data or len(self.file_data) < 64:
            return {"error": "File too small"}
        
        # DOS头
        dos_sig = struct.unpack('<H', self.file_data[:2])[0]
        if dos_sig != 0x5A4D:
            return {"error": "Invalid DOS signature"}
        
        pe_offset = struct.unpack('<L', self.file_data[60:64])[0]
        
        # PE头
        if pe_offset + 4 > len(self.file_data):
            return {"error": "Invalid PE offset"}
        
        pe_sig = struct.unpack('<L', self.file_data[pe_offset:pe_offset+4])[0]
        if pe_sig != 0x00004550:
            return {"error": "Invalid PE signature"}
        
        # COFF头
        coff_offset = pe_offset + 4
        if coff_offset + 20 > len(self.file_data):
            return {"error": "COFF header too small"}
        
        try:
            machine, num_sections, timestamp, ptr_symtab, num_symbols, opt_hdr_size = \
                struct.unpack('<HHLLHH', self.file_data[coff_offset:coff_offset+18])
            
            characteristics = struct.unpack('<H', self.file_data[coff_offset+18:coff_offset+20])[0]
            
            self.pe_header = {
                "pe_offset": pe_offset,
                "machine": machine,
                "num_sections": num_sections,
                "timestamp": timestamp,
                "opt_header_size": opt_hdr_size,
                "characteristics": characteristics
            }
            
            return self.pe_header
            
        except struct.error as e:
            return {"error": f"COFF parsing error: {e}"}
    
    def parse_sections(self) -> List[Dict[str, Any]]:
        """解析节表"""
        if not self.pe_header:
            self.parse_pe_header()
        
        if "error" in self.pe_header:
            return []
        
        pe_offset = self.pe_header["pe_offset"]
        opt_hdr_size = self.pe_header["opt_header_size"]
        num_sections = self.pe_header["num_sections"]
        
        section_table_offset = pe_offset + 24 + opt_hdr_size
        sections = []
        
        for i in range(min(num_sections, 20)):
            section_offset = section_table_offset + i * 40
            
            if section_offset + 40 > len(self.file_data):
                break
            
            try:
                section_data = self.file_data[section_offset:section_offset+40]
                name = section_data[:8].rstrip(b'\x00').decode('ascii', errors='ignore')
                
                virtual_size, virtual_addr, raw_size, raw_addr = \
                    struct.unpack('<LLLL', section_data[8:24])
                
                characteristics = struct.unpack('<L', section_data[36:40])[0]
                
                section_info = {
                    "name": name,
                    "virtual_size": virtual_size,
                    "virtual_address": virtual_addr,
                    "raw_size": raw_size,
                    "raw_address": raw_addr,
                    "characteristics": characteristics,
                    "executable": bool(characteristics & 0x20000000),
                    "readable": bool(characteristics & 0x40000000),
                    "writable": bool(characteristics & 0x80000000)
                }
                
                sections.append(section_info)
                
            except (struct.error, UnicodeDecodeError) as e:
                print(f"Section {i} parsing error: {e}")
                continue
        
        self.sections = sections
        return sections
    
    def extract_code_sections(self) -> Dict[str, bytes]:
        """提取代码节"""
        if not self.sections:
            self.parse_sections()
        
        code_sections = {}
        
        for section in self.sections:
            if section["executable"] and section["name"] in [".text", "CODE"]:
                raw_addr = section["raw_address"]
                raw_size = section["raw_size"]
                
                if raw_addr + raw_size <= len(self.file_data):
                    code_data = self.file_data[raw_addr:raw_addr + raw_size]
                    code_sections[section["name"]] = code_data
        
        return code_sections
    
    def simple_disassemble_x64(self, code_data: bytes, base_addr: int = 0) -> List[Dict[str, Any]]:
        """简单的x64反汇编（基础版本）"""
        instructions = []
        offset = 0
        
        while offset < len(code_data) - 1:
            try:
                # 获取当前字节
                byte = code_data[offset]
                
                # 简单的指令识别
                instruction = self._identify_x64_instruction(code_data, offset)
                
                if instruction:
                    instructions.append({
                        "offset": f"0x{base_addr + offset:08X}",
                        "bytes": code_data[offset:offset + instruction["length"]].hex().upper(),
                        "mnemonic": instruction["mnemonic"],
                        "operands": instruction.get("operands", ""),
                        "comment": instruction.get("comment", "")
                    })
                    offset += instruction["length"]
                else:
                    # 未识别的字节，跳过
                    instructions.append({
                        "offset": f"0x{base_addr + offset:08X}",
                        "bytes": f"{byte:02X}",
                        "mnemonic": "db",
                        "operands": f"0x{byte:02X}",
                        "comment": "Unknown byte"
                    })
                    offset += 1
                    
            except Exception as e:
                offset += 1
                continue
        
        return instructions
    
    def _identify_x64_instruction(self, data: bytes, offset: int) -> Optional[Dict[str, Any]]:
        """识别x64指令（简化版本）"""
        if offset >= len(data):
            return None
        
        byte = data[offset]
        
        # 常见的x64指令模式
        patterns = {
            0x48: {"mnemonic": "REX.W", "length": 1, "comment": "64-bit operand prefix"},
            0x50: {"mnemonic": "push", "operands": "rax", "length": 1},
            0x51: {"mnemonic": "push", "operands": "rcx", "length": 1},
            0x52: {"mnemonic": "push", "operands": "rdx", "length": 1},
            0x53: {"mnemonic": "push", "operands": "rbx", "length": 1},
            0x54: {"mnemonic": "push", "operands": "rsp", "length": 1},
            0x55: {"mnemonic": "push", "operands": "rbp", "length": 1},
            0x56: {"mnemonic": "push", "operands": "rsi", "length": 1},
            0x57: {"mnemonic": "push", "operands": "rdi", "length": 1},
            0x58: {"mnemonic": "pop", "operands": "rax", "length": 1},
            0x59: {"mnemonic": "pop", "operands": "rcx", "length": 1},
            0x5A: {"mnemonic": "pop", "operands": "rdx", "length": 1},
            0x5B: {"mnemonic": "pop", "operands": "rbx", "length": 1},
            0x5C: {"mnemonic": "pop", "operands": "rsp", "length": 1},
            0x5D: {"mnemonic": "pop", "operands": "rbp", "length": 1},
            0x5E: {"mnemonic": "pop", "operands": "rsi", "length": 1},
            0x5F: {"mnemonic": "pop", "operands": "rdi", "length": 1},
            0x90: {"mnemonic": "nop", "length": 1, "comment": "No operation"},
            0xC3: {"mnemonic": "ret", "length": 1, "comment": "Return"},
            0xCC: {"mnemonic": "int3", "length": 1, "comment": "Breakpoint"},
            0xE8: {"mnemonic": "call", "length": 5, "comment": "Call relative"},
            0xE9: {"mnemonic": "jmp", "length": 5, "comment": "Jump relative"},
            0xEB: {"mnemonic": "jmp", "length": 2, "comment": "Jump short"},
        }
        
        if byte in patterns:
            instruction = patterns[byte].copy()
            
            # 处理需要额外操作数的指令
            if byte == 0xE8 and offset + 5 <= len(data):  # call rel32
                rel_addr = struct.unpack('<l', data[offset+1:offset+5])[0]
                instruction["operands"] = f"0x{rel_addr:08X}"
            elif byte == 0xE9 and offset + 5 <= len(data):  # jmp rel32
                rel_addr = struct.unpack('<l', data[offset+1:offset+5])[0]
                instruction["operands"] = f"0x{rel_addr:08X}"
            elif byte == 0xEB and offset + 2 <= len(data):  # jmp rel8
                rel_addr = struct.unpack('<b', data[offset+1:offset+2])[0]
                instruction["operands"] = f"0x{rel_addr:02X}"
            
            return instruction
        
        # 检查多字节指令
        if offset + 1 < len(data):
            two_byte = struct.unpack('<H', data[offset:offset+2])[0]
            
            two_byte_patterns = {
                0x8B48: {"mnemonic": "mov", "length": 3, "comment": "Move with REX.W"},
                0x8948: {"mnemonic": "mov", "length": 3, "comment": "Move with REX.W"},
            }
            
            if two_byte in two_byte_patterns:
                return two_byte_patterns[two_byte]
        
        return None
    
    def extract_strings_from_code(self, code_data: bytes) -> List[Dict[str, Any]]:
        """从代码中提取字符串引用"""
        strings = []
        
        # 查找ASCII字符串
        ascii_pattern = rb'[\x20-\x7E]{4,}'
        for match in re.finditer(ascii_pattern, code_data):
            string = match.group().decode('ascii', errors='ignore')
            strings.append({
                "offset": f"0x{match.start():08X}",
                "type": "ASCII",
                "value": string,
                "length": len(string)
            })
        
        # 查找Unicode字符串
        unicode_pattern = rb'(?:[\x20-\x7E]\x00){4,}'
        for match in re.finditer(unicode_pattern, code_data):
            try:
                string = match.group().decode('utf-16le', errors='ignore').rstrip('\x00')
                strings.append({
                    "offset": f"0x{match.start():08X}",
                    "type": "Unicode",
                    "value": string,
                    "length": len(string)
                })
            except:
                continue
        
        return strings[:50]  # 限制数量
    
    def analyze_function_patterns(self, instructions: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """分析函数模式"""
        functions = []
        current_function = None
        
        for i, instr in enumerate(instructions):
            # 函数开始模式：push rbp; mov rbp, rsp
            if (instr["mnemonic"] == "push" and instr["operands"] == "rbp" and
                i + 1 < len(instructions) and 
                instructions[i + 1]["mnemonic"] == "mov"):
                
                current_function = {
                    "start_offset": instr["offset"],
                    "instructions": [],
                    "calls": [],
                    "jumps": []
                }
            
            if current_function:
                current_function["instructions"].append(instr)
                
                # 记录调用和跳转
                if instr["mnemonic"] == "call":
                    current_function["calls"].append(instr["operands"])
                elif instr["mnemonic"] in ["jmp", "je", "jne", "jz", "jnz"]:
                    current_function["jumps"].append(instr["operands"])
                
                # 函数结束模式：ret
                if instr["mnemonic"] == "ret":
                    current_function["end_offset"] = instr["offset"]
                    current_function["size"] = len(current_function["instructions"])
                    functions.append(current_function)
                    current_function = None
        
        return functions
    
    def generate_c_pseudocode(self, functions: List[Dict[str, Any]]) -> str:
        """生成C语言伪代码"""
        c_code = []
        c_code.append("// EFI Boot File Decompiled Pseudocode")
        c_code.append("// Generated by EFI Disassembler")
        c_code.append("")
        c_code.append("#include <efi.h>")
        c_code.append("#include <efilib.h>")
        c_code.append("")
        
        for i, func in enumerate(functions):
            func_name = f"sub_{func['start_offset'].replace('0x', '')}"
            c_code.append(f"// Function at {func['start_offset']}")
            c_code.append(f"EFI_STATUS {func_name}() {{")
            
            # 分析指令并生成伪代码
            for instr in func["instructions"][:20]:  # 限制指令数量
                comment = f"    // {instr['offset']}: {instr['bytes']} {instr['mnemonic']} {instr['operands']}"
                c_code.append(comment)
                
                # 简单的指令转换
                if instr["mnemonic"] == "push":
                    c_code.append(f"    // Push {instr['operands']} to stack")
                elif instr["mnemonic"] == "call":
                    c_code.append(f"    // Call function at {instr['operands']}")
                elif instr["mnemonic"] == "mov":
                    c_code.append(f"    // Move operation")
                elif instr["mnemonic"] == "ret":
                    c_code.append("    return EFI_SUCCESS;")
            
            c_code.append("}")
            c_code.append("")
        
        return "\n".join(c_code)
    
    def full_analysis(self) -> Dict[str, Any]:
        """完整分析"""
        if not self.load_file():
            return {"error": "Failed to load file"}
        
        print(f"Analyzing {self.file_path}...")
        
        # 解析PE结构
        pe_info = self.parse_pe_header()
        if "error" in pe_info:
            return pe_info
        
        # 解析节
        sections = self.parse_sections()
        
        # 提取代码节
        code_sections = self.extract_code_sections()
        
        analysis_result = {
            "file_path": self.file_path,
            "pe_header": pe_info,
            "sections": sections,
            "disassembly": {},
            "functions": [],
            "strings": [],
            "c_pseudocode": ""
        }
        
        # 反汇编代码节
        for section_name, code_data in code_sections.items():
            print(f"Disassembling section {section_name}...")
            
            # 获取节的虚拟地址
            section_info = next((s for s in sections if s["name"] == section_name), None)
            base_addr = section_info["virtual_address"] if section_info else 0
            
            # 反汇编
            instructions = self.simple_disassemble_x64(code_data, base_addr)
            analysis_result["disassembly"][section_name] = instructions[:500]  # 限制指令数量
            
            # 提取字符串
            strings = self.extract_strings_from_code(code_data)
            analysis_result["strings"].extend(strings)
            
            # 分析函数
            functions = self.analyze_function_patterns(instructions)
            analysis_result["functions"].extend(functions)
        
        # 生成C伪代码
        if analysis_result["functions"]:
            analysis_result["c_pseudocode"] = self.generate_c_pseudocode(analysis_result["functions"][:10])
        
        return analysis_result

def main():
    """主函数"""
    print("=== EFI文件反编译分析工具 ===")
    print("=== EFI File Disassembly Analysis Tool ===\n")
    
    efi_files = [
        "EFI/BOOT/BOOTX64.EFI",
        "EFI/BOOT/grub.efi"
    ]
    
    for efi_file in efi_files:
        if os.path.exists(efi_file):
            print(f"\n{'='*60}")
            print(f"反编译分析: {efi_file}")
            print(f"{'='*60}")
            
            disassembler = EFIDisassembler(efi_file)
            result = disassembler.full_analysis()
            
            if "error" not in result:
                # 保存反汇编结果
                output_file = f"{efi_file.replace('/', '_').replace('.', '_')}_disassembly.json"
                with open(output_file, 'w', encoding='utf-8') as f:
                    json.dump(result, f, indent=2, ensure_ascii=False)
                
                # 保存C伪代码
                if result.get("c_pseudocode"):
                    c_file = f"{efi_file.replace('/', '_').replace('.', '_')}_pseudocode.c"
                    with open(c_file, 'w', encoding='utf-8') as f:
                        f.write(result["c_pseudocode"])
                    print(f"C伪代码已保存到: {c_file}")
                
                print(f"反汇编结果已保存到: {output_file}")
                print(f"发现 {len(result.get('functions', []))} 个函数")
                print(f"发现 {len(result.get('strings', []))} 个字符串")
            else:
                print(f"分析失败: {result['error']}")

if __name__ == "__main__":
    main()
