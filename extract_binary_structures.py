#!/usr/bin/env python3

def find_binary_patterns(data):
    """查找二进制中的潜在ACPI或DMAR结构"""
    
    # 常见的ACPI表签名
    acpi_signatures = [
        b'DMAR', b'DSDT', b'FADT', b'MADT', b'SSDT', 
        b'HPET', b'MCFG', b'SRAT', b'SLIT', b'BERT'
    ]
    
    print("=== Searching for ACPI signatures ===")
    for sig in acpi_signatures:
        pos = 0
        while True:
            pos = data.find(sig, pos)
            if pos == -1:
                break
            print(f"Found '{sig.decode()}' at offset 0x{pos:x}")
            
            # 显示周围的数据
            start = max(0, pos - 16)
            end = min(len(data), pos + 48)
            context = data[start:end]
            hex_repr = ' '.join(f'{b:02x}' for b in context)
            print(f"  Context: {hex_repr}")
            
            pos += 1
    
    print()
    
    # 搜索可能的DMAR表特定结构
    print("=== Searching for DMAR-specific patterns ===")
    
    # DMAR表中的常见字段值
    # Host Address Width (通常是39-48)
    # Flags字段
    # DRHD (DMA Remapping Hardware Unit Definition)
    
    # 搜索DRHD结构 (type=0, length>=16)
    drhd_pattern = b'\x00\x00\x10\x00'  # type=0, flags=0, length=16 (little endian)
    pos = 0
    while True:
        pos = data.find(drhd_pattern, pos)
        if pos == -1:
            break
        print(f"Potential DRHD structure at offset 0x{pos:x}")
        
        # 显示结构数据
        if pos + 16 <= len(data):
            struct_data = data[pos:pos+16]
            hex_repr = ' '.join(f'{b:02x}' for b in struct_data)
            print(f"  Structure: {hex_repr}")
        
        pos += 1
    
    print()
    
    # 搜索完整的DMAR表模式
    print("=== Searching for complete DMAR table patterns ===")
    
    # 标准DMAR表头 + Host Address Width
    # DMAR(4) + Length(4) + Revision(1) + Checksum(1) + OEMID(6) + ...
    for i in range(0, len(data) - 48, 4):  # 4字节对齐
        chunk = data[i:i+48]
        
        # 检查是否可能是DMAR表头
        if len(chunk) >= 48:
            # 检查前4字节是否为DMAR
            if chunk[0:4] == b'DMAR':
                length = int.from_bytes(chunk[4:8], byteorder='little')
                if 48 <= length <= 1024:  # 合理的表长度
                    print(f"Potential complete DMAR table at offset 0x{i:x}")
                    print(f"  Length: {length}")
                    
                    # 显示完整表头
                    if i + length <= len(data):
                        table_data = data[i:i+min(length, 128)]
                        for j in range(0, len(table_data), 16):
                            hex_bytes = ' '.join(f'{b:02x}' for b in table_data[j:j+16])
                            ascii_repr = ''.join(chr(b) if 32 <= b <= 126 else '.' for b in table_data[j:j+16])
                            print(f'  {i+j:08x}: {hex_bytes:<48} {ascii_repr}')
                        print()

def analyze_dmar_structure(data, offset):
    """分析潜在的DMAR表结构"""
    if offset + 48 > len(data):
        return False
        
    signature = data[offset:offset+4]
    if signature != b'DMAR':
        return False
    
    length = int.from_bytes(data[offset+4:offset+8], byteorder='little')
    revision = data[offset+8]
    checksum = data[offset+9]
    oemid = data[offset+10:offset+16]
    oemtableid = data[offset+16:offset+24]
    oemrevision = int.from_bytes(data[offset+24:offset+28], byteorder='little')
    creatorid = data[offset+28:offset+32]
    creatorrevision = int.from_bytes(data[offset+32:offset+36], byteorder='little')
    
    # DMAR表特定字段
    if offset + 40 <= len(data):
        host_addr_width = data[offset+36]
        flags = data[offset+37]
        
        print(f"DMAR Table Analysis at offset 0x{offset:x}:")
        print(f"  Signature: {signature}")
        print(f"  Length: {length}")
        print(f"  Revision: {revision}")
        print(f"  Checksum: 0x{checksum:02x}")
        print(f"  OEM ID: {oemid}")
        print(f"  OEM Table ID: {oemtableid}")
        print(f"  OEM Revision: 0x{oemrevision:08x}")
        print(f"  Creator ID: {creatorid}")
        print(f"  Creator Revision: 0x{creatorrevision:08x}")
        print(f"  Host Address Width: {host_addr_width}")
        print(f"  Flags: 0x{flags:02x}")
        
        return True
    return False

# 主分析
with open('EFI/BOOT/BOOT.EFI', 'rb') as f:
    data = f.read()

print(f"Analyzing EFI/BOOT/BOOT.EFI ({len(data)} bytes)")
print("="*60)

find_binary_patterns(data)

# 尝试分析每个DMAR位置
print("=== Detailed DMAR Analysis ===")
pos = 0
while True:
    pos = data.find(b'DMAR', pos)
    if pos == -1:
        break
    
    if analyze_dmar_structure(data, pos):
        print()
    
    pos += 1

print("Analysis complete.")