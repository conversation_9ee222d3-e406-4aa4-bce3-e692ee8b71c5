#!/usr/bin/env python3

def find_verification_function(filename):
    """精确定位grubx64_real.efi中的验证函数"""
    
    try:
        with open(filename, 'rb') as f:
            data = f.read()
            
            print("=== 定位验证函数 ===\n")
            
            # 找到错误消息位置
            error_msg = b"This is NOT a standard Ventoy device and is NOT supported"
            error_pos = data.find(error_msg)
            
            if error_pos == -1:
                print("未找到错误消息")
                return
                
            print(f"错误消息位置: 0x{error_pos:04X}")
            
            # 向前搜索可能的验证函数
            # 在错误消息前1000字节范围内搜索可能的函数开始
            search_start = max(0, error_pos - 1000)
            
            print(f"\n在 0x{search_start:04X} - 0x{error_pos:04X} 范围内搜索验证函数:")
            
            # 搜索可能引用错误消息的指令
            # lea指令加载字符串地址的模式
            for i in range(search_start, error_pos - 10):
                # 检查是否有指向错误消息的引用
                chunk = data[i:i+10]
                
                # 寻找可能的字符串地址引用
                # 计算相对偏移
                for j in range(len(chunk) - 4):
                    # 检查是否是相对地址引用
                    potential_offset = int.from_bytes(chunk[j:j+4], 'little', signed=True)
                    calculated_addr = i + j + 4 + potential_offset
                    
                    # 如果计算出的地址接近错误消息位置
                    if abs(calculated_addr - error_pos) < 50:
                        print(f"  可能的字符串引用 @ 0x{i+j:04X}, 偏移: {potential_offset:08X}")
                        
                        # 显示这个位置前后的代码
                        context_start = max(0, i - 50)
                        context_end = min(len(data), i + 100)
                        context = data[context_start:context_end]
                        
                        print(f"    代码上下文 (0x{context_start:04X}):")
                        for k in range(0, len(context), 16):
                            offset = context_start + k
                            line = context[k:k+16]
                            hex_str = ' '.join(f'{b:02x}' for b in line)
                            print(f"      {offset:08x}: {hex_str}")
                        print()
            
            # 搜索VTOY_CHKDEV相关的代码
            chkdev_pattern = b"VTOY_CHKDEV_RESULT_STRING"
            chkdev_pos = data.find(chkdev_pattern)
            
            if chkdev_pos != -1:
                print(f"\nVTOY_CHKDEV_RESULT_STRING 位置: 0x{chkdev_pos:04X}")
                
                # 搜索可能引用这个字符串的代码
                for i in range(max(0, chkdev_pos - 2000), chkdev_pos):
                    chunk = data[i:i+8]
                    for j in range(len(chunk) - 4):
                        potential_offset = int.from_bytes(chunk[j:j+4], 'little', signed=True)
                        calculated_addr = i + j + 4 + potential_offset
                        
                        if abs(calculated_addr - chkdev_pos) < 10:
                            print(f"  VTOY_CHKDEV引用 @ 0x{i+j:04X}")
                            
                            # 分析这个函数
                            func_start = max(0, i - 100)
                            func_end = min(len(data), i + 200)
                            func_code = data[func_start:func_end]
                            
                            print(f"    函数代码片段:")
                            for k in range(0, len(func_code), 16):
                                offset = func_start + k
                                line = func_code[k:k+16]
                                hex_str = ' '.join(f'{b:02x}' for b in line)
                                print(f"      {offset:08x}: {hex_str}")
                            print()
            
            print("=== 分析完成 ===")
            
    except FileNotFoundError:
        print(f"文件未找到: {filename}")
    except Exception as e:
        print(f"分析过程中出错: {e}")

if __name__ == "__main__":
    filename = r"D:\新建文件夹 (2)\新建文件夹\EFI\BOOT\grubx64_real.efi"
    find_verification_function(filename)