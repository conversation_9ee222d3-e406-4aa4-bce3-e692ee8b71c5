#!/usr/bin/env python3
"""
使用Ghidra对BOOT.EFI进行完整反编译分析
"""

import os
import subprocess
import sys
import time

def run_ghidra_analysis():
    """运行Ghidra进行BOOT.EFI的完整分析"""
    
    ghidra_path = r"D:\ghidra_11.4.1_PUBLIC"
    boot_efi_path = r"D:\新建文件夹 (2)\新建文件夹\EFI\BOOT\BOOT.EFI"
    project_dir = r"D:\新建文件夹 (2)\新建文件夹\ghidra_project"
    
    # 检查文件是否存在
    if not os.path.exists(boot_efi_path):
        print(f"错误: BOOT.EFI文件不存在: {boot_efi_path}")
        return False
    
    if not os.path.exists(ghidra_path):
        print(f"错误: Ghidra路径不存在: {ghidra_path}")
        return False
    
    # 创建项目目录
    os.makedirs(project_dir, exist_ok=True)
    
    print("=== Ghidra BOOT.EFI 完整反编译分析 ===")
    print(f"Ghidra路径: {ghidra_path}")
    print(f"分析文件: {boot_efi_path}")
    print(f"项目目录: {project_dir}")
    
    # 创建Ghidra脚本进行自动化分析
    ghidra_script = f"""
import ghidra.app.script.GhidraScript
import ghidra.program.model.listing.*
import ghidra.program.model.symbol.*
import ghidra.program.model.mem.*
import ghidra.program.model.data.*
import ghidra.app.decompiler.*
import ghidra.app.plugin.core.analysis.*
import ghidra.program.model.address.*
import java.io.*

# 分析脚本
def analyze_boot_efi():
    program = getCurrentProgram()
    listing = program.getListing()
    symbolTable = program.getSymbolTable()
    memory = program.getMemory()
    
    print("=== BOOT.EFI 自动分析开始 ===")
    
    # 1. 基本信息
    print("1. 程序基本信息:")
    print(f"  程序名: {{program.getName()}}")
    print(f"  入口点: {{program.getImageBase()}}")
    print(f"  最小地址: {{program.getMinAddress()}}")
    print(f"  最大地址: {{program.getMaxAddress()}}")
    
    # 2. 搜索所有字符串
    print("\\n2. 字符串分析:")
    strings = []
    for data in listing.getDefinedData(True):
        if data.hasStringValue():
            addr = data.getAddress()
            value = data.getValue()
            if value and len(str(value)) > 3:
                strings.append((addr, str(value)))
                print(f"  {{addr}}: {{value}}")
    
    # 3. 搜索函数
    print("\\n3. 函数分析:")
    functions = program.getFunctionManager().getFunctions(True)
    func_count = 0
    for func in functions:
        if func_count < 50:  # 限制输出
            print(f"  {{func.getEntryPoint()}}: {{func.getName()}} (大小: {{func.getBody().getNumAddresses()}})")
        func_count += 1
    print(f"  总计: {{func_count}} 个函数")
    
    # 4. 搜索特定的验证相关字符串
    print("\\n4. 验证相关分析:")
    verification_keywords = ["check", "verify", "MBR", "error", "fail", "device", "General_UDisk", "DmarInsert"]
    for keyword in verification_keywords:
        addresses = findBytes(None, keyword.encode(), 100)
        if addresses:
            print(f"  '{keyword}' 找到 {{len(addresses)}} 次:")
            for addr in addresses[:5]:
                print(f"    {{addr}}")
    
    # 5. 反编译主要函数
    print("\\n5. 主要函数反编译:")
    decompiler = DecompInterface()
    decompiler.openProgram(program)
    
    main_functions = []
    for func in functions:
        if func.getName().lower() in ["main", "entry", "start"] or func.getEntryPoint().equals(program.getImageBase()):
            main_functions.append(func)
    
    for func in main_functions[:5]:  # 反编译前5个主要函数
        print(f"\\n反编译函数: {{func.getName()}} @ {{func.getEntryPoint()}}")
        try:
            result = decompiler.decompileFunction(func, 30, None)
            if result.decompileCompleted():
                code = result.getDecompiledFunction().getC()
                print(code[:2000])  # 只显示前2000字符
            else:
                print("  反编译失败")
        except:
            print("  反编译出错")
    
    decompiler.dispose()
    print("\\n=== BOOT.EFI 自动分析完成 ===")

# 运行分析
analyze_boot_efi()
"""
    
    # 保存Ghidra脚本
    script_path = os.path.join(project_dir, "analyze_boot_efi.py")
    with open(script_path, 'w', encoding='utf-8') as f:
        f.write(ghidra_script)
    
    print(f"已创建Ghidra分析脚本: {script_path}")
    
    # 构建Ghidra命令
    analyzeHeadless_path = os.path.join(ghidra_path, "support", "analyzeHeadless.bat")
    if not os.path.exists(analyzeHeadless_path):
        analyzeHeadless_path = os.path.join(ghidra_path, "support", "analyzeHeadless")
    
    project_name = "BOOT_EFI_Analysis"
    
    # Ghidra命令参数
    cmd = [
        analyzeHeadless_path,
        project_dir,
        project_name,
        "-import", boot_efi_path,
        "-postScript", script_path,
        "-deleteProject",  # 分析后删除项目
        "-overwrite"
    ]
    
    print(f"\\n执行Ghidra命令:")
    print(" ".join(cmd))
    
    try:
        # 运行Ghidra分析
        process = subprocess.Popen(
            cmd,
            stdout=subprocess.PIPE,
            stderr=subprocess.PIPE,
            text=True,
            cwd=ghidra_path
        )
        
        print("\\nGhidra分析中，请稍候...")
        
        # 实时输出
        while True:
            output = process.stdout.readline()
            if output == '' and process.poll() is not None:
                break
            if output:
                print(output.strip())
        
        # 获取错误输出
        stderr = process.stderr.read()
        if stderr:
            print(f"\\n错误输出:\\n{stderr}")
        
        return_code = process.poll()
        if return_code == 0:
            print("\\nGhidra分析成功完成!")
            return True
        else:
            print(f"\\nGhidra分析失败，返回码: {return_code}")
            return False
            
    except Exception as e:
        print(f"运行Ghidra时出错: {e}")
        return False

if __name__ == "__main__":
    success = run_ghidra_analysis()
    if success:
        print("\\n=== 分析完成 ===")
    else:
        print("\\n=== 分析失败 ===")