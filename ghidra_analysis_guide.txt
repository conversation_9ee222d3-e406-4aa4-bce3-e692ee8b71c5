
=== BOOT.EFI 手动分析指南 ===

1. 启动Ghidra:
   - 双击运行: D:\ghidra_11.4.1_PUBLIC\ghidraRun.bat
   - 或者运行: D:\ghidra_11.4.1_PUBLIC\ghidra.exe

2. 创建新项目:
   - File -> New Project
   - 选择 Non-Shared Project
   - 项目目录: D:\新建文件夹 (2)\新建文件夹\ghidra_project
   - 项目名称: BOOT_EFI_Analysis

3. 导入BOOT.EFI:
   - File -> Import File
   - 选择: D:\新建文件夹 (2)\新建文件夹\EFI\BOOT\BOOT.EFI
   - Format: Portable Executable (PE)
   - 点击 OK

4. 分析文件:
   - 双击导入的BOOT.EFI文件
   - 在弹出的对话框中选择 "Yes" 进行自动分析
   - 等待分析完成

5. 关键分析步骤:

   a) 字符串搜索:
      - Search -> For Strings
      - 搜索关键词: "MBR", "check", "verify", "error", "DmarInsert", "General_UDisk"

   b) 函数列表:
      - Window -> Functions
      - 查看所有识别的函数

   c) 入口点分析:
      - 在Symbol Tree中找到 "entry" 函数
      - 双击查看反编译代码

   d) 内存映射:
      - Window -> Memory Map
      - 查看内存布局

   e) 数据类型:
      - Window -> Data Type Manager
      - 查看结构体定义

6. 重点分析目标:

   a) 硬件验证函数:
      - 搜索包含 "4C35A0E2" 或类似硬件ID的函数
      - 查找错误代码12的设置位置

   b) VTD模拟功能:
      - 搜索 "DmarInsert", "DMAR", "ACPI" 相关代码
      - 分析ACPI表操作函数

   c) 启动流程:
      - 从entry函数开始，追踪主要的执行路径
      - 识别初始化、验证、VTD设置的顺序

7. 导出分析结果:
   - File -> Export Program
   - 选择导出格式和位置

请按照以上步骤手动进行分析，这将比自动化脚本提供更详细的控制和结果。
