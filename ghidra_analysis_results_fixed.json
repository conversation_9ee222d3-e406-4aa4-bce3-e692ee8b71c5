{"EFI/BOOT/BOOTX64.EFI": {"success": true, "efi_file": "EFI\\BOOT\\BOOTX64.EFI", "output_directory": "D:\\新建文件夹 (2)\\新建文件夹\\ghidra_output\\BOOTX64", "main_file": null, "summary_file": null, "temp_directory": "C:\\Users\\<USER>\\AppData\\Local\\Temp\\ghidra_efi_oy11hb0p", "stdout": "nalyzer.java:457)\n\tat ghidra.app.util.headless.AnalyzeHeadless.launch(AnalyzeHeadless.java:198)\n\tat ghidra.GhidraLauncher.launch(GhidraLauncher.java:81)\n\tat ghidra.Ghidra.main(Ghidra.java:54)\nCaused by: java.lang.ClassNotFoundException: Failed to find source bundle containing script: C:\\Users\\<USER>\\AppData\\Local\\Temp\\ghidra_efi_oy11hb0p\\SimpleEFIAnalysis.java\n\tat ghidra.app.script.JavaScriptProvider.loadClass(JavaScriptProvider.java:151)\n\tat ghidra.app.script.JavaScriptProvider.getScriptInstance(JavaScriptProvider.java:96)\n\t... 10 more\n \nINFO  ANALYZING changes made by post scripts: file:///C:/Users/<USER>/AppData/Local/Temp/ghidra_efi_oy11hb0p/BOOTX64.EFI (HeadlessAnalyzer)  \nINFO  REPORT: Post-analysis succeeded for file: file:///C:/Users/<USER>/AppData/Local/Temp/ghidra_efi_oy11hb0p/BOOTX64.EFI (HeadlessAnalyzer)  \nINFO  REPORT: Save succeeded for: /BOOTX64.EFI (EFI_Analysis:/BOOTX64.EFI) (HeadlessAnalyzer)  \nINFO  REPORT: Import succeeded (HeadlessAnalyzer)  \n", "stderr": "Picked up JAV<PERSON>_TOOL_OPTIONS: -Dfile.encoding=UTF-8\nPicked up JAVA_TOOL_OPTIONS: -Dfile.encoding=UTF-8\nPicked up JAVA_TOOL_OPTIONS: -Dfile.encoding=UTF-8\n"}, "EFI/BOOT/grub.efi": {"success": true, "efi_file": "EFI\\BOOT\\grub.efi", "output_directory": "D:\\新建文件夹 (2)\\新建文件夹\\ghidra_output\\grub", "main_file": null, "summary_file": null, "temp_directory": "C:\\Users\\<USER>\\AppData\\Local\\Temp\\ghidra_efi_oy11hb0p", "stdout": "al(HeadlessAnalyzer.java:457)\n\tat ghidra.app.util.headless.AnalyzeHeadless.launch(AnalyzeHeadless.java:198)\n\tat ghidra.GhidraLauncher.launch(GhidraLauncher.java:81)\n\tat ghidra.Ghidra.main(Ghidra.java:54)\nCaused by: java.lang.ClassNotFoundException: Failed to find source bundle containing script: C:\\Users\\<USER>\\AppData\\Local\\Temp\\ghidra_efi_oy11hb0p\\SimpleEFIAnalysis.java\n\tat ghidra.app.script.JavaScriptProvider.loadClass(JavaScriptProvider.java:151)\n\tat ghidra.app.script.JavaScriptProvider.getScriptInstance(JavaScriptProvider.java:96)\n\t... 10 more\n \nINFO  ANALYZING changes made by post scripts: file:///C:/Users/<USER>/AppData/Local/Temp/ghidra_efi_oy11hb0p/grub.efi (HeadlessAnalyzer)  \nINFO  REPORT: Post-analysis succeeded for file: file:///C:/Users/<USER>/AppData/Local/Temp/ghidra_efi_oy11hb0p/grub.efi (HeadlessAnalyzer)  \nINFO  REPORT: Save succeeded for: /grub.efi (EFI_Analysis:/grub.efi) (HeadlessAnalyzer)  \nINFO  REPORT: Import succeeded (HeadlessAnalyzer)  \n", "stderr": "Picked up JAV<PERSON>_TOOL_OPTIONS: -Dfile.encoding=UTF-8\nPicked up JAVA_TOOL_OPTIONS: -Dfile.encoding=UTF-8\nPicked up JAVA_TOOL_OPTIONS: -Dfile.encoding=UTF-8\n"}}