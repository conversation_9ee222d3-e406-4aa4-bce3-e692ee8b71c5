#!/usr/bin/env python3
"""
Ghidra EFI文件自动反编译工具
Ghidra EFI File Automatic Decompiler
"""

import os
import sys
import subprocess
import json
import time
from pathlib import Path
from typing import Dict, List, Any, Optional

class GhidraEFIDecompiler:
    """Ghidra EFI文件反编译器"""
    
    def __init__(self, ghidra_path: str, workspace_path: str = None):
        self.ghidra_path = Path(ghidra_path)
        self.workspace_path = Path(workspace_path) if workspace_path else Path.cwd() / "ghidra_workspace"
        self.project_name = "EFI_Analysis"
        
        # 验证Ghidra路径
        if not self.ghidra_path.exists():
            raise FileNotFoundError(f"Ghidra路径不存在: {ghidra_path}")
        
        # 查找analyzeHeadless脚本
        self.analyze_script = self._find_analyze_script()
        if not self.analyze_script:
            raise FileNotFoundError("未找到Ghidra analyzeHeadless脚本")
    
    def _find_analyze_script(self) -> Optional[Path]:
        """查找analyzeHeadless脚本"""
        possible_paths = [
            self.ghidra_path / "support" / "analyzeHeadless.bat",
            self.ghidra_path / "support" / "analyzeHeadless",
            self.ghidra_path / "analyzeHeadless.bat",
            self.ghidra_path / "analyzeHeadless"
        ]
        
        for path in possible_paths:
            if path.exists():
                return path
        
        return None
    
    def create_ghidra_script(self, output_dir: str) -> str:
        """创建Ghidra反编译脚本"""
        script_content = '''
//Ghidra EFI反编译脚本
//<AUTHOR>
//@category Analysis
//@keybinding
//@menupath
//@toolbar

import ghidra.app.script.GhidraScript;
import ghidra.program.model.listing.*;
import ghidra.program.model.address.*;
import ghidra.program.model.symbol.*;
import ghidra.app.decompiler.*;
import ghidra.app.decompiler.component.*;
import java.io.*;
import java.util.*;

public class EFIDecompileScript extends GhidraScript {
    
    @Override
    public void run() throws Exception {
        
        // 获取当前程序
        Program program = getCurrentProgram();
        if (program == null) {
            println("No program loaded");
            return;
        }
        
        println("开始分析EFI文件: " + program.getName());
        
        // 创建输出目录
        String outputDir = "''' + output_dir.replace('\\', '/') + '''";
        File outDir = new File(outputDir);
        if (!outDir.exists()) {
            outDir.mkdirs();
        }
        
        // 初始化反编译器
        DecompInterface decompiler = new DecompInterface();
        decompiler.openProgram(program);
        
        // 获取所有函数
        FunctionManager functionManager = program.getFunctionManager();
        FunctionIterator functions = functionManager.getFunctions(true);
        
        // 创建主输出文件
        File mainOutput = new File(outputDir, program.getName() + "_decompiled.c");
        PrintWriter mainWriter = new PrintWriter(new FileWriter(mainOutput));
        
        // 写入文件头
        mainWriter.println("/*");
        mainWriter.println(" * Decompiled EFI Boot File: " + program.getName());
        mainWriter.println(" * Generated by Ghidra Decompiler");
        mainWriter.println(" * Date: " + new Date());
        mainWriter.println(" */");
        mainWriter.println();
        mainWriter.println("#include <Uefi.h>");
        mainWriter.println("#include <Library/UefiLib.h>");
        mainWriter.println("#include <Library/UefiBootServicesTableLib.h>");
        mainWriter.println("#include <Library/UefiRuntimeServicesTableLib.h>");
        mainWriter.println();
        
        // 分析和反编译每个函数
        int functionCount = 0;
        while (functions.hasNext() && !monitor.isCancelled()) {
            Function function = functions.next();
            
            println("反编译函数: " + function.getName() + " at " + function.getEntryPoint());
            
            try {
                // 反编译函数
                DecompileResults results = decompiler.decompileFunction(function, 30, monitor);
                
                if (results != null && results.decompileCompleted()) {
                    DecompiledFunction decompiledFunction = results.getDecompiledFunction();
                    String cCode = decompiledFunction.getC();
                    
                    // 写入主文件
                    mainWriter.println("// Function: " + function.getName());
                    mainWriter.println("// Address: " + function.getEntryPoint());
                    mainWriter.println("// Size: " + function.getBody().getNumAddresses() + " bytes");
                    mainWriter.println(cCode);
                    mainWriter.println();
                    
                    // 创建单独的函数文件
                    File funcFile = new File(outputDir, "func_" + function.getName() + ".c");
                    PrintWriter funcWriter = new PrintWriter(new FileWriter(funcFile));
                    funcWriter.println("// Function: " + function.getName());
                    funcWriter.println("// Address: " + function.getEntryPoint());
                    funcWriter.println("// Original Binary: " + program.getName());
                    funcWriter.println();
                    funcWriter.println("#include <Uefi.h>");
                    funcWriter.println();
                    funcWriter.println(cCode);
                    funcWriter.close();
                    
                    functionCount++;
                } else {
                    mainWriter.println("// Failed to decompile function: " + function.getName());
                    mainWriter.println("// Address: " + function.getEntryPoint());
                    mainWriter.println();
                }
                
            } catch (Exception e) {
                println("Error decompiling function " + function.getName() + ": " + e.getMessage());
                mainWriter.println("// Error decompiling function: " + function.getName());
                mainWriter.println("// Error: " + e.getMessage());
                mainWriter.println();
            }
        }
        
        mainWriter.close();
        decompiler.dispose();
        
        println("反编译完成!");
        println("总函数数: " + functionCount);
        println("输出目录: " + outputDir);
        
        // 生成分析报告
        generateAnalysisReport(program, outputDir, functionCount);
    }
    
    private void generateAnalysisReport(Program program, String outputDir, int functionCount) throws Exception {
        File reportFile = new File(outputDir, "analysis_report.txt");
        PrintWriter reportWriter = new PrintWriter(new FileWriter(reportFile));
        
        reportWriter.println("EFI文件分析报告");
        reportWriter.println("================");
        reportWriter.println();
        reportWriter.println("文件名: " + program.getName());
        reportWriter.println("分析时间: " + new Date());
        reportWriter.println("总函数数: " + functionCount);
        reportWriter.println();
        
        // 内存布局
        reportWriter.println("内存布局:");
        reportWriter.println("---------");
        MemoryBlock[] blocks = program.getMemory().getBlocks();
        for (MemoryBlock block : blocks) {
            reportWriter.println("Block: " + block.getName());
            reportWriter.println("  Start: " + block.getStart());
            reportWriter.println("  End: " + block.getEnd());
            reportWriter.println("  Size: " + block.getSize());
            reportWriter.println("  Permissions: " + 
                (block.isRead() ? "R" : "-") +
                (block.isWrite() ? "W" : "-") +
                (block.isExecute() ? "X" : "-"));
            reportWriter.println();
        }
        
        // 符号表
        reportWriter.println("符号表:");
        reportWriter.println("-------");
        SymbolTable symbolTable = program.getSymbolTable();
        SymbolIterator symbols = symbolTable.getAllSymbols(true);
        int symbolCount = 0;
        while (symbols.hasNext() && symbolCount < 100) {
            Symbol symbol = symbols.next();
            reportWriter.println(symbol.getName() + " @ " + symbol.getAddress());
            symbolCount++;
        }
        
        reportWriter.close();
        println("分析报告已生成: " + reportFile.getAbsolutePath());
    }
}
'''
        
        # 保存脚本文件
        script_file = Path.cwd() / "EFIDecompileScript.java"
        with open(script_file, 'w', encoding='utf-8') as f:
            f.write(script_content)
        
        return str(script_file)
    
    def analyze_efi_file(self, efi_file_path: str, output_dir: str = None) -> Dict[str, Any]:
        """使用Ghidra分析EFI文件"""
        efi_file = Path(efi_file_path)
        if not efi_file.exists():
            raise FileNotFoundError(f"EFI文件不存在: {efi_file_path}")
        
        if not output_dir:
            output_dir = str(Path.cwd() / "ghidra_output" / efi_file.stem)
        
        output_path = Path(output_dir)
        output_path.mkdir(parents=True, exist_ok=True)
        
        print(f"开始Ghidra分析: {efi_file_path}")
        print(f"输出目录: {output_dir}")
        
        # 创建Ghidra脚本
        script_file = self.create_ghidra_script(output_dir)
        
        try:
            # 构建Ghidra命令
            cmd = [
                str(self.analyze_script),
                str(self.workspace_path),
                self.project_name,
                "-import", str(efi_file),
                "-postScript", script_file,
                "-deleteProject"  # 分析完成后删除项目
            ]
            
            print("执行Ghidra命令:")
            print(" ".join(cmd))
            
            # 执行Ghidra分析
            process = subprocess.Popen(
                cmd,
                stdout=subprocess.PIPE,
                stderr=subprocess.PIPE,
                text=True,
                cwd=str(self.ghidra_path)
            )
            
            stdout, stderr = process.communicate(timeout=600)  # 10分钟超时
            
            if process.returncode == 0:
                print("Ghidra分析完成!")
                
                # 检查输出文件
                main_output = output_path / f"{efi_file.name}_decompiled.c"
                report_file = output_path / "analysis_report.txt"
                
                result = {
                    "success": True,
                    "efi_file": str(efi_file),
                    "output_directory": output_dir,
                    "main_decompiled_file": str(main_output) if main_output.exists() else None,
                    "analysis_report": str(report_file) if report_file.exists() else None,
                    "stdout": stdout,
                    "stderr": stderr
                }
                
                # 统计生成的文件
                c_files = list(output_path.glob("*.c"))
                result["generated_files"] = [str(f) for f in c_files]
                result["function_count"] = len([f for f in c_files if f.name.startswith("func_")])
                
                return result
            else:
                return {
                    "success": False,
                    "error": f"Ghidra分析失败，返回码: {process.returncode}",
                    "stdout": stdout,
                    "stderr": stderr
                }
        
        except subprocess.TimeoutExpired:
            process.kill()
            return {
                "success": False,
                "error": "Ghidra分析超时（10分钟）"
            }
        
        except Exception as e:
            return {
                "success": False,
                "error": f"执行错误: {str(e)}"
            }
        
        finally:
            # 清理脚本文件
            if Path(script_file).exists():
                Path(script_file).unlink()
    
    def batch_analyze_efi_files(self, efi_files: List[str]) -> Dict[str, Any]:
        """批量分析EFI文件"""
        results = {}
        
        for efi_file in efi_files:
            if Path(efi_file).exists():
                print(f"\n{'='*60}")
                print(f"分析文件: {efi_file}")
                print(f"{'='*60}")
                
                result = self.analyze_efi_file(efi_file)
                results[efi_file] = result
                
                if result["success"]:
                    print(f"✅ 成功: {result.get('function_count', 0)} 个函数已反编译")
                else:
                    print(f"❌ 失败: {result.get('error', 'Unknown error')}")
            else:
                results[efi_file] = {
                    "success": False,
                    "error": f"文件不存在: {efi_file}"
                }
        
        return results

def main():
    """主函数"""
    print("=== Ghidra EFI文件自动反编译工具 ===")
    print("=== Ghidra EFI File Automatic Decompiler ===\n")
    
    # Ghidra路径
    ghidra_path = r"D:\ghidra_11.4.1_PUBLIC"
    
    try:
        # 创建反编译器实例
        decompiler = GhidraEFIDecompiler(ghidra_path)
        
        # 要分析的EFI文件
        efi_files = [
            "EFI/BOOT/BOOTX64.EFI",
            "EFI/BOOT/grub.efi"
        ]
        
        # 过滤存在的文件
        existing_files = [f for f in efi_files if Path(f).exists()]
        
        if not existing_files:
            print("❌ 未找到要分析的EFI文件")
            return
        
        print(f"发现 {len(existing_files)} 个EFI文件:")
        for f in existing_files:
            print(f"  - {f}")
        print()
        
        # 批量分析
        results = decompiler.batch_analyze_efi_files(existing_files)
        
        # 生成总结报告
        print(f"\n{'='*60}")
        print("分析总结")
        print(f"{'='*60}")
        
        total_files = len(results)
        successful_files = len([r for r in results.values() if r["success"]])
        total_functions = sum([r.get("function_count", 0) for r in results.values() if r["success"]])
        
        print(f"总文件数: {total_files}")
        print(f"成功分析: {successful_files}")
        print(f"总函数数: {total_functions}")
        
        # 保存结果
        results_file = "ghidra_analysis_results.json"
        with open(results_file, 'w', encoding='utf-8') as f:
            json.dump(results, f, indent=2, ensure_ascii=False)
        
        print(f"\n详细结果已保存到: {results_file}")
        
        # 显示输出文件位置
        print(f"\n反编译文件位置:")
        for file_path, result in results.items():
            if result["success"]:
                print(f"  {Path(file_path).name}:")
                print(f"    - 主文件: {result.get('main_decompiled_file', 'N/A')}")
                print(f"    - 输出目录: {result.get('output_directory', 'N/A')}")
                print(f"    - 函数数: {result.get('function_count', 0)}")
    
    except Exception as e:
        print(f"❌ 错误: {e}")
        print("\n请确保:")
        print("1. Ghidra路径正确")
        print("2. Ghidra已正确安装")
        print("3. Java环境已配置")

if __name__ == "__main__":
    main()
