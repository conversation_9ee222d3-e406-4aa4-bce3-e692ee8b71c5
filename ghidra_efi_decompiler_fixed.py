#!/usr/bin/env python3
"""
Ghidra EFI文件自动反编译工具 (修复版)
Ghidra EFI File Automatic Decompiler (Fixed Version)
"""

import os
import sys
import subprocess
import json
import time
import shutil
import tempfile
from pathlib import Path
from typing import Dict, List, Any, Optional

class GhidraEFIDecompilerFixed:
    """Ghidra EFI文件反编译器 (修复版)"""
    
    def __init__(self, ghidra_path: str):
        self.ghidra_path = Path(ghidra_path)
        
        # 使用临时目录避免中文路径问题
        self.temp_dir = Path(tempfile.mkdtemp(prefix="ghidra_efi_"))
        self.workspace_path = self.temp_dir / "workspace"
        self.workspace_path.mkdir(exist_ok=True)
        
        self.project_name = "EFI_Analysis"
        
        # 验证Ghidra路径
        if not self.ghidra_path.exists():
            raise FileNotFoundError(f"Ghidra路径不存在: {ghidra_path}")
        
        # 查找analyzeHeadless脚本
        self.analyze_script = self._find_analyze_script()
        if not self.analyze_script:
            raise FileNotFoundError("未找到Ghidra analyzeHeadless脚本")
        
        print(f"临时工作目录: {self.temp_dir}")
    
    def _find_analyze_script(self) -> Optional[Path]:
        """查找analyzeHeadless脚本"""
        possible_paths = [
            self.ghidra_path / "support" / "analyzeHeadless.bat",
            self.ghidra_path / "support" / "analyzeHeadless",
            self.ghidra_path / "analyzeHeadless.bat",
            self.ghidra_path / "analyzeHeadless"
        ]
        
        for path in possible_paths:
            if path.exists():
                return path
        
        return None
    
    def create_simple_analysis_script(self, output_dir: str) -> str:
        """创建简单的Ghidra分析脚本"""
        script_content = '''
//Simple EFI Analysis Script
//<AUTHOR>
//@category Analysis

import ghidra.app.script.GhidraScript;
import ghidra.program.model.listing.*;
import ghidra.program.model.address.*;
import ghidra.program.model.symbol.*;
import ghidra.app.decompiler.*;
import java.io.*;
import java.util.*;

public class SimpleEFIAnalysis extends GhidraScript {
    
    @Override
    public void run() throws Exception {
        
        Program program = getCurrentProgram();
        if (program == null) {
            println("No program loaded");
            return;
        }
        
        println("Analyzing EFI file: " + program.getName());
        
        // Create output directory
        String outputDir = "''' + output_dir.replace('\\', '/') + '''";
        File outDir = new File(outputDir);
        if (!outDir.exists()) {
            outDir.mkdirs();
        }
        
        // Initialize decompiler
        DecompInterface decompiler = new DecompInterface();
        decompiler.openProgram(program);
        
        // Get all functions
        FunctionManager functionManager = program.getFunctionManager();
        FunctionIterator functions = functionManager.getFunctions(true);
        
        // Create main output file
        File mainOutput = new File(outputDir, program.getName() + "_decompiled.c");
        PrintWriter mainWriter = new PrintWriter(new FileWriter(mainOutput));
        
        // Write header
        mainWriter.println("/*");
        mainWriter.println(" * Decompiled EFI File: " + program.getName());
        mainWriter.println(" * Generated by Ghidra");
        mainWriter.println(" */");
        mainWriter.println();
        mainWriter.println("#include <Uefi.h>");
        mainWriter.println("#include <Library/UefiLib.h>");
        mainWriter.println();
        
        int functionCount = 0;
        int maxFunctions = 50; // Limit functions to avoid timeout
        
        while (functions.hasNext() && functionCount < maxFunctions && !monitor.isCancelled()) {
            Function function = functions.next();
            
            println("Decompiling function: " + function.getName());
            
            try {
                DecompileResults results = decompiler.decompileFunction(function, 10, monitor);
                
                if (results != null && results.decompileCompleted()) {
                    DecompiledFunction decompiledFunction = results.getDecompiledFunction();
                    String cCode = decompiledFunction.getC();
                    
                    mainWriter.println("// Function: " + function.getName());
                    mainWriter.println("// Address: " + function.getEntryPoint());
                    mainWriter.println(cCode);
                    mainWriter.println();
                    
                    functionCount++;
                } else {
                    mainWriter.println("// Failed to decompile: " + function.getName());
                    mainWriter.println();
                }
                
            } catch (Exception e) {
                println("Error: " + e.getMessage());
                mainWriter.println("// Error decompiling: " + function.getName());
                mainWriter.println();
            }
        }
        
        mainWriter.close();
        decompiler.dispose();
        
        println("Analysis complete!");
        println("Functions decompiled: " + functionCount);
        
        // Create summary file
        File summaryFile = new File(outputDir, "summary.txt");
        PrintWriter summaryWriter = new PrintWriter(new FileWriter(summaryFile));
        summaryWriter.println("EFI Analysis Summary");
        summaryWriter.println("===================");
        summaryWriter.println("File: " + program.getName());
        summaryWriter.println("Functions: " + functionCount);
        summaryWriter.println("Output: " + mainOutput.getName());
        summaryWriter.close();
    }
}
'''
        
        # 保存脚本到临时目录
        script_file = self.temp_dir / "SimpleEFIAnalysis.java"
        with open(script_file, 'w', encoding='utf-8') as f:
            f.write(script_content)
        
        return str(script_file)
    
    def analyze_efi_file_simple(self, efi_file_path: str) -> Dict[str, Any]:
        """简化的EFI文件分析"""
        efi_file = Path(efi_file_path)
        if not efi_file.exists():
            raise FileNotFoundError(f"EFI文件不存在: {efi_file_path}")
        
        # 复制EFI文件到临时目录
        temp_efi = self.temp_dir / efi_file.name
        shutil.copy2(efi_file, temp_efi)
        
        # 创建输出目录
        output_dir = self.temp_dir / "output" / efi_file.stem
        output_dir.mkdir(parents=True, exist_ok=True)
        
        print(f"分析文件: {efi_file.name}")
        print(f"临时文件: {temp_efi}")
        print(f"输出目录: {output_dir}")
        
        # 创建分析脚本
        script_file = self.create_simple_analysis_script(str(output_dir))
        
        try:
            # 构建Ghidra命令 (使用短路径)
            cmd = [
                str(self.analyze_script),
                str(self.workspace_path),
                self.project_name,
                "-import", str(temp_efi),
                "-postScript", script_file,
                "-deleteProject"
            ]
            
            print("执行Ghidra命令...")
            
            # 设置环境变量
            env = os.environ.copy()
            env['JAVA_TOOL_OPTIONS'] = '-Dfile.encoding=UTF-8'
            
            # 执行Ghidra分析
            process = subprocess.Popen(
                cmd,
                stdout=subprocess.PIPE,
                stderr=subprocess.PIPE,
                text=True,
                cwd=str(self.ghidra_path),
                env=env
            )
            
            stdout, stderr = process.communicate(timeout=300)  # 5分钟超时
            
            if process.returncode == 0:
                print("✅ Ghidra分析完成!")
                
                # 检查输出文件
                main_output = output_dir / f"{efi_file.name}_decompiled.c"
                summary_file = output_dir / "summary.txt"
                
                # 复制结果到原始目录
                original_output_dir = Path.cwd() / "ghidra_output" / efi_file.stem
                original_output_dir.mkdir(parents=True, exist_ok=True)
                
                if main_output.exists():
                    shutil.copy2(main_output, original_output_dir / main_output.name)
                if summary_file.exists():
                    shutil.copy2(summary_file, original_output_dir / summary_file.name)
                
                return {
                    "success": True,
                    "efi_file": str(efi_file),
                    "output_directory": str(original_output_dir),
                    "main_file": str(original_output_dir / main_output.name) if main_output.exists() else None,
                    "summary_file": str(original_output_dir / summary_file.name) if summary_file.exists() else None,
                    "temp_directory": str(self.temp_dir),
                    "stdout": stdout[-1000:],  # 只保留最后1000字符
                    "stderr": stderr[-1000:] if stderr else ""
                }
            else:
                return {
                    "success": False,
                    "error": f"Ghidra分析失败，返回码: {process.returncode}",
                    "stdout": stdout[-1000:],
                    "stderr": stderr[-1000:] if stderr else ""
                }
        
        except subprocess.TimeoutExpired:
            process.kill()
            return {
                "success": False,
                "error": "Ghidra分析超时（5分钟）"
            }
        
        except Exception as e:
            return {
                "success": False,
                "error": f"执行错误: {str(e)}"
            }
    
    def cleanup(self):
        """清理临时文件"""
        try:
            if self.temp_dir.exists():
                shutil.rmtree(self.temp_dir)
                print(f"已清理临时目录: {self.temp_dir}")
        except Exception as e:
            print(f"清理临时目录失败: {e}")

def create_manual_decompile_script():
    """创建手动反编译脚本"""
    manual_script = '''
# Ghidra手动反编译指南
# =====================

## 步骤1: 启动Ghidra
1. 打开 D:\\ghidra_11.4.1_PUBLIC\\ghidraRun.bat
2. 创建新项目或打开现有项目

## 步骤2: 导入EFI文件
1. File -> Import File
2. 选择 BOOTX64.EFI 文件
3. 格式选择 "Portable Executable (PE)"
4. 点击 OK

## 步骤3: 分析文件
1. 双击导入的文件打开CodeBrowser
2. 点击 "Yes" 开始自动分析
3. 等待分析完成

## 步骤4: 查看反编译代码
1. 在左侧Symbol Tree中找到Functions
2. 双击任意函数查看汇编代码
3. 在右侧Decompile窗口查看C代码

## 步骤5: 导出反编译结果
1. 选择要导出的函数
2. 右键 -> Export -> C/C++
3. 选择保存位置

## 主要函数位置
- 入口点通常在 entry 函数
- 主要逻辑在 FUN_* 函数中
- 字符串引用可以帮助理解功能

## 分析技巧
1. 查看字符串引用了解功能
2. 分析函数调用关系
3. 注意EFI协议的使用
4. 关注错误处理逻辑
'''
    
    with open("ghidra_manual_guide.txt", 'w', encoding='utf-8') as f:
        f.write(manual_script)
    
    print("已创建手动反编译指南: ghidra_manual_guide.txt")

def main():
    """主函数"""
    print("=== Ghidra EFI文件反编译工具 (修复版) ===")
    print("=== Ghidra EFI File Decompiler (Fixed) ===\n")
    
    # Ghidra路径
    ghidra_path = r"D:\ghidra_11.4.1_PUBLIC"
    
    # 创建手动指南
    create_manual_decompile_script()
    
    try:
        # 创建反编译器实例
        decompiler = GhidraEFIDecompilerFixed(ghidra_path)
        
        # 要分析的EFI文件
        efi_files = [
            "EFI/BOOT/BOOTX64.EFI",
            "EFI/BOOT/grub.efi"
        ]
        
        # 过滤存在的文件
        existing_files = [f for f in efi_files if Path(f).exists()]
        
        if not existing_files:
            print("❌ 未找到要分析的EFI文件")
            return
        
        print(f"发现 {len(existing_files)} 个EFI文件:")
        for f in existing_files:
            print(f"  - {f}")
        print()
        
        # 分析每个文件
        results = {}
        for efi_file in existing_files:
            print(f"\n{'='*60}")
            result = decompiler.analyze_efi_file_simple(efi_file)
            results[efi_file] = result
            
            if result["success"]:
                print(f"✅ 成功分析: {efi_file}")
                print(f"   输出目录: {result['output_directory']}")
            else:
                print(f"❌ 分析失败: {efi_file}")
                print(f"   错误: {result.get('error', 'Unknown')}")
        
        # 清理临时文件
        decompiler.cleanup()
        
        # 保存结果
        results_file = "ghidra_analysis_results_fixed.json"
        with open(results_file, 'w', encoding='utf-8') as f:
            json.dump(results, f, indent=2, ensure_ascii=False)
        
        print(f"\n{'='*60}")
        print("分析完成!")
        print(f"结果已保存到: {results_file}")
        print(f"手动指南: ghidra_manual_guide.txt")
        
        # 显示成功的结果
        successful_results = [r for r in results.values() if r["success"]]
        if successful_results:
            print(f"\n反编译文件位置:")
            for result in successful_results:
                if result["main_file"]:
                    print(f"  - {result['main_file']}")
    
    except Exception as e:
        print(f"❌ 错误: {e}")
        print("\n建议:")
        print("1. 检查Ghidra路径是否正确")
        print("2. 确保Java环境已配置")
        print("3. 尝试手动使用Ghidra (参考 ghidra_manual_guide.txt)")

if __name__ == "__main__":
    main()
