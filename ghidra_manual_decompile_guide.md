# Ghidra EFI文件手动反编译完整指南

## 🎯 目标
使用Ghidra将boot.efi (BOOTX64.EFI) 完全反编译为C语言代码

## 📋 准备工作

### 1. 启动Ghidra
```bash
# 进入Ghidra目录
cd D:\ghidra_11.4.1_PUBLIC

# 启动Ghidra
ghidraRun.bat
```

### 2. 创建新项目
1. 启动后选择 `File` → `New Project`
2. 选择 `Non-Shared Project`
3. 项目目录：`D:\ghidra_projects`
4. 项目名称：`EFI_Boot_Analysis`
5. 点击 `Finish`

## 🔍 导入和分析EFI文件

### 3. 导入BOOTX64.EFI
1. 在项目窗口中，点击 `File` → `Import File`
2. 选择文件：`D:\新建文件夹 (2)\新建文件夹\EFI\BOOT\BOOTX64.EFI`
3. 格式选择：`Portable Executable (PE)`
4. 语言选择：`x86:LE:64:default`
5. 点击 `OK`

### 4. 开始自动分析
1. 双击导入的 `BOOTX64.EFI` 文件
2. 弹出分析选项对话框，点击 `Yes`
3. 在 `Analysis Options` 中确保以下选项已勾选：
   - ✅ ASCII Strings
   - ✅ Data Reference
   - ✅ Function Start Search
   - ✅ Subroutine References
   - ✅ Windows x86 PE RTTI Analyzer
   - ✅ Create Address Tables
   - ✅ Decompiler Parameter ID
4. 点击 `Analyze` 开始分析
5. 等待分析完成（可能需要几分钟）

## 🔧 反编译操作

### 5. 查看程序结构
分析完成后，在左侧 `Program Tree` 中可以看到：
- **Memory Map**: 内存布局
- **Symbol Tree**: 符号表
  - **Functions**: 所有识别的函数
  - **Labels**: 标签
  - **Classes**: 类（如果有）

### 6. 找到入口点
1. 在 `Symbol Tree` → `Functions` 中查找：
   - `entry` 函数（主入口点）
   - `FUN_*` 函数（其他函数）
2. 或者在 `Memory Map` 中查看 `.text` 段

### 7. 反编译主要函数

#### 方法1：逐个函数反编译
1. 在 `Functions` 列表中双击 `entry` 函数
2. 在右侧 `Decompile` 窗口查看C代码
3. 右键点击反编译窗口 → `Export` → `Export to C File`
4. 保存为 `entry_function.c`

#### 方法2：批量导出
1. 选择 `File` → `Export Program`
2. 格式选择：`C/C++`
3. 选择导出选项：
   - ✅ Create Header File (.h)
   - ✅ Use C++ Style Comments
   - ✅ Use Decompiler
4. 输出目录：`D:\efi_decompiled`
5. 点击 `OK`

### 8. 分析关键函数

#### 查找重要函数的方法：
1. **字符串引用法**：
   - 在 `Symbol Tree` → `Defined Strings` 中查找关键字符串
   - 双击字符串查看引用位置
   - 跟踪到使用该字符串的函数

2. **交叉引用法**：
   - 右键点击函数名 → `References` → `Show References to`
   - 查看哪些函数调用了当前函数

3. **调用图法**：
   - 选择函数后，点击 `Window` → `Function Call Graph`
   - 查看函数调用关系

## 📊 重要分析点

### 9. EFI特有结构识别

#### EFI Boot Services
查找以下模式的函数调用：
```c
// EFI_BOOT_SERVICES 结构体调用
gBS->AllocatePool(...)
gBS->FreePool(...)
gBS->LoadImage(...)
gBS->StartImage(...)
```

#### EFI Runtime Services
```c
// EFI_RUNTIME_SERVICES 结构体调用
gRT->GetVariable(...)
gRT->SetVariable(...)
gRT->GetTime(...)
```

#### EFI Protocols
查找GUID模式：
```c
// 协议GUID通常是16字节的结构
EFI_LOADED_IMAGE_PROTOCOL_GUID
EFI_SIMPLE_FILE_SYSTEM_PROTOCOL_GUID
```

### 10. 关键函数识别

#### 主入口函数
- 通常名为 `UefiMain` 或 `_ModuleEntryPoint`
- 参数：`(EFI_HANDLE ImageHandle, EFI_SYSTEM_TABLE *SystemTable)`

#### 启动逻辑函数
- 查找包含 "boot", "load", "start" 字符串的函数
- 分析文件操作相关函数

#### 安全验证函数
- 查找包含 "verify", "check", "validate" 的函数
- 分析证书和签名验证逻辑

## 💾 导出完整反编译结果

### 11. 创建完整的C项目结构

#### 步骤1：导出所有函数
```bash
# 在Ghidra中执行
File → Export Program → C/C++
```

#### 步骤2：整理文件结构
```
efi_decompiled/
├── include/
│   ├── efi_types.h      # EFI类型定义
│   ├── protocols.h      # 协议定义
│   └── bootx64.h        # 主头文件
├── src/
│   ├── main.c           # 主入口函数
│   ├── boot_logic.c     # 启动逻辑
│   ├── security.c       # 安全验证
│   └── file_ops.c       # 文件操作
└── Makefile             # 编译脚本
```

### 12. 手动优化反编译代码

#### 常见需要修复的问题：
1. **变量类型**：将 `undefined*` 替换为正确的EFI类型
2. **函数签名**：添加正确的EFI函数签名
3. **结构体**：识别和定义EFI结构体
4. **常量**：将魔数替换为EFI常量

#### 示例修复：
```c
// Ghidra原始输出
undefined8 FUN_00401000(undefined8 param_1, undefined8 param_2)

// 修复后
EFI_STATUS UefiMain(EFI_HANDLE ImageHandle, EFI_SYSTEM_TABLE *SystemTable)
```

## 🔍 高级分析技巧

### 13. 使用Ghidra脚本自动化

#### 创建自定义脚本：
1. `Window` → `Script Manager`
2. 点击 `Create New Script`
3. 选择 `Java` 或 `Python`
4. 编写自动化分析脚本

#### 示例脚本功能：
- 自动识别EFI函数签名
- 批量重命名函数
- 提取字符串和常量
- 生成函数调用图

### 14. 交叉验证

#### 对比其他工具：
1. **IDA Pro**：对比反编译结果
2. **x64dbg**：动态调试验证
3. **PE工具**：验证PE结构解析

#### 验证方法：
1. 检查函数调用关系是否合理
2. 验证字符串引用是否正确
3. 确认内存访问模式

## 📝 输出文档

### 15. 生成分析报告

#### 创建以下文档：
1. **函数列表**：所有函数的名称、地址、功能描述
2. **调用图**：函数间的调用关系
3. **数据结构**：识别的结构体和类型
4. **字符串表**：所有字符串及其用途
5. **安全分析**：安全相关功能的详细分析

#### 报告模板：
```markdown
# BOOTX64.EFI 反编译分析报告

## 1. 文件概述
- 文件大小：943KB
- 架构：x86-64
- 编译器：Microsoft Visual C++
- 入口点：0x401000

## 2. 函数分析
### 2.1 主入口函数
- 函数名：UefiMain
- 地址：0x401000
- 功能：EFI应用程序入口点

### 2.2 核心函数
[详细列表...]

## 3. 安全特性
[安全分析...]

## 4. 启动流程
[启动逻辑分析...]
```

## ⚡ 快速操作清单

### 最小化操作步骤：
1. ✅ 启动Ghidra
2. ✅ 创建项目
3. ✅ 导入BOOTX64.EFI
4. ✅ 运行自动分析
5. ✅ 导出为C/C++
6. ✅ 手动优化代码
7. ✅ 生成分析报告

### 预期输出：
- 完整的C源代码文件
- 函数调用关系图
- 详细的分析报告
- 可编译的项目结构

---

**注意事项**：
- 反编译过程可能需要1-2小时
- 生成的代码需要手动优化才能编译
- 建议保存Ghidra项目文件以便后续分析
- 某些加密或混淆的代码可能无法完全反编译
