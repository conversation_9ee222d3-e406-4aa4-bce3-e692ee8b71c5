
# Ghidra手动反编译指南
# =====================

## 步骤1: 启动Ghidra
1. 打开 D:\ghidra_11.4.1_PUBLIC\ghidraRun.bat
2. 创建新项目或打开现有项目

## 步骤2: 导入EFI文件
1. File -> Import File
2. 选择 BOOTX64.EFI 文件
3. 格式选择 "Portable Executable (PE)"
4. 点击 OK

## 步骤3: 分析文件
1. 双击导入的文件打开CodeBrowser
2. 点击 "Yes" 开始自动分析
3. 等待分析完成

## 步骤4: 查看反编译代码
1. 在左侧Symbol Tree中找到Functions
2. 双击任意函数查看汇编代码
3. 在右侧Decompile窗口查看C代码

## 步骤5: 导出反编译结果
1. 选择要导出的函数
2. 右键 -> Export -> C/C++
3. 选择保存位置

## 主要函数位置
- 入口点通常在 entry 函数
- 主要逻辑在 FUN_* 函数中
- 字符串引用可以帮助理解功能

## 分析技巧
1. 查看字符串引用了解功能
2. 分析函数调用关系
3. 注意EFI协议的使用
4. 关注错误处理逻辑
