
unset vtchkdef
vt_vtoychksum_exist "${VTOY_CHKSUM_FILE_PATH}" 

if [ "$VT_EXIST_MD5" = "1" ]; then
    if [ -z "$vtchkdef" ]; then
        set default=0
        set vtchkdef=1
    fi
    menuentry "$VTLANG_CHKSUM_MD5_CALC_CHK" --class=checksum_md5 {
        md5sum "${vtoy_iso_part}${VTOY_CHKSUM_FILE_PATH}"       
        vt_cmp_checksum 0 "${VTOY_CHKSUM_FILE_PATH}"

        echo -en "\n\n$VTLANG_ENTER_EXIT ..."
        read vtInputKey
    }
else
    menuentry "$VTLANG_CHKSUM_MD5_CALC" --class=checksum_md5 {
        md5sum "${vtoy_iso_part}${VTOY_CHKSUM_FILE_PATH}"
        
        echo -en "\n\n$VTLANG_ENTER_EXIT ..."
        read vtInputKey
    }
fi

if [ "$VT_EXIST_SHA1" = "1" ]; then
    if [ -z "$vtchkdef" ]; then
        set default=1
        set vtchkdef=1
    fi
    menuentry "$VTLANG_CHKSUM_SHA1_CALC_CHK" --class=checksum_sha1 {
        sha1sum "${vtoy_iso_part}${VTOY_CHKSUM_FILE_PATH}"
        vt_cmp_checksum 1 "${VTOY_CHKSUM_FILE_PATH}" 
        
        echo -en "\n\n$VTLANG_ENTER_EXIT ..."
        read vtInputKey
    }
else
    menuentry "$VTLANG_CHKSUM_SHA1_CALC" --class=checksum_sha1 {
        sha1sum "${vtoy_iso_part}${VTOY_CHKSUM_FILE_PATH}"
        
        echo -en "\n\n$VTLANG_ENTER_EXIT ..."
        read vtInputKey
    }
fi



if [ "$VT_EXIST_SHA256" = "1" ]; then
    if [ -z "$vtchkdef" ]; then
        set default=2
        set vtchkdef=1
    fi
    menuentry "$VTLANG_CHKSUM_SHA256_CALC_CHK" --class=checksum_sha256 {
        sha256sum "${vtoy_iso_part}${VTOY_CHKSUM_FILE_PATH}"
        vt_cmp_checksum 2 "${VTOY_CHKSUM_FILE_PATH}"         
        
        echo -en "\n\n$VTLANG_ENTER_EXIT ..."
        read vtInputKey
    }
else
    menuentry "$VTLANG_CHKSUM_SHA256_CALC" --class=checksum_sha256 {
        sha256sum "${vtoy_iso_part}${VTOY_CHKSUM_FILE_PATH}"
        
        echo -en "\n\n$VTLANG_ENTER_EXIT ..."
        read vtInputKey
    }
fi



if [ "$VT_EXIST_SHA512" = "1" ]; then
    if [ -z "$vtchkdef" ]; then
        set default=3
        set vtchkdef=1
    fi
    menuentry "$VTLANG_CHKSUM_SHA512_CALC_CHK" --class=checksum_sha512{
        sha512sum "${vtoy_iso_part}${VTOY_CHKSUM_FILE_PATH}"
        vt_cmp_checksum 3 "${VTOY_CHKSUM_FILE_PATH}"        
        
        echo -en "\n\n$VTLANG_ENTER_EXIT ..."
        read vtInputKey
    }
else
    menuentry "$VTLANG_CHKSUM_SHA512_CALC" --class=checksum_sha512{
        sha512sum "${vtoy_iso_part}${VTOY_CHKSUM_FILE_PATH}"
        
        echo -en "\n\n$VTLANG_ENTER_EXIT ..."
        read vtInputKey
    }
fi


menuentry "$VTLANG_RETURN_PREVIOUS" --class=vtoyret VTOY_RET {
    echo 'Return ...'
}
