set label="writable"

menuentry 'Firmware Test Suite' --class ubuntu --class gnu-linux --class gnu --class os {
    set root='vtimghd,4'
    linux   /boot/vmlinuz root=LABEL=$label ro console=tty1 console=ttyS0 quiet
    initrd  /boot/initrd.img
}

menuentry 'Firmware Test Suite (recovery mode)' --class ubuntu --class gnu-linux --class gnu --class os {    
    set root='vtimghd,4'    
    echo    'Loading Linux ...'
    linux   /boot/vmlinuz root=LABEL=$label ro recovery nomodeset
    echo    'Loading initial ramdisk ...'
    initrd  /boot/initrd.img
}
