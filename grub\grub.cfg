#************************************************************************************
# <AUTHOR> <EMAIL>
# 
# This program is free software; you can redistribute it and/or
# modify it under the terms of the GNU General Public License as
# published by the Free Software Foundation; either version 3 of the
# License, or (at your option) any later version.
# 
# This program is distributed in the hope that it will be useful, but
# WITHOUT ANY WARRANTY; without even the implied warranty of
# MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the GNU
# General Public License for more details.
# 
# You should have received a copy of the GNU General Public License
# along with this program; if not, see <http://www.gnu.org/licenses/>.
# 
#************************************************************************************

if [ "$grub_platform" = "pc" ]; then
    insmod setkey
    insmod regexp
fi

function ventoy_pause {
    echo "press Enter to continue ......"
    read vtTmpPause
}

function ventoy_debug_pause {
    if [ -n "${vtdebug_flag}" ]; then
        echo "press Enter to continue ......"
        read vtTmpPause
    fi
}


function ventoy_max_resolution {
    vt_enum_video_mode
    vt_get_video_mode 0 vtCurMode
    terminal_output console
    set gfxmode=$vtCurMode
    terminal_output gfxterm
}

function ventoy_cli_console {
    if [ -z "$vtoy_display_mode" ]; then
        terminal_output  console
    elif [ "$vtoy_display_mode" = "GUI" ]; then
        terminal_output  console
    fi
}

function ventoy_gui_console {  
    if [ -z "$vtoy_display_mode" ]; then
        terminal_output  gfxterm
    elif [ "$vtoy_display_mode" = "GUI" ]; then
        terminal_output  gfxterm
    fi    
}

function ventoy_acpi_param {  
    if [ "$VTOY_PARAM_NO_ACPI" != "1" ]; then
        vt_acpi_param "$1" "$2"
    fi
}

function ventoy_vcfg_proc {
    if vt_check_custom_boot "${1}" vt_vcfg; then
        set vtoy_chosen_path="${1}"
        vt_file_basefile "${vtoy_chosen_path}" vtoy_chosen_file
        
        export vtoy_chosen_path
        export vtoy_chosen_file
        ventoy_debug_pause
        configfile "${vtoy_iso_part}${vt_vcfg}"
        true
    else
        false
    fi
}

function ventoy_language {
    configfile $prefix/menulang.cfg
}

function ventoy_diagnosis {
    vt_enum_video_mode    
    configfile $prefix/debug.cfg
}

function ventoy_localboot {
    configfile $prefix/localboot.cfg
}

# 定义Ventoy扩展菜单函数
function ventoy_ext_menu {    
    # 检查Ventoy的grub配置文件是否存在
    if [ -e $prefix/ventoy_grub.cfg ]; then
        # 设置Ventoy新上下文标记
        set ventoy_new_context=1
        # 加载Ventoy的grub配置文件
        configfile $prefix/ventoy_grub.cfg
        # 取消Ventoy新上下文标记
        unset ventoy_new_context
    else
        # 若配置文件不存在，输出提示信息
        echo "ventoy_grub.cfg 不存在。"
        # 提示用户按回车键退出
        echo -en "\n$VTLANG_ENTER_EXIT ..."
        # 等待用户输入（按回车继续）
        read vtInputKey
    fi
}

function ventoy_checksum {
    if [ -f "${vtoy_iso_part}${VTOY_CHKSUM_FILE_PATH}" ]; then
        configfile $prefix/checksum.cfg
    fi
}

function ventoy_show_help {
    if [ -f $prefix/help.tar.gz ]; then
        if [ -z "$vtoy_help_txt_mem_addr" ]; then
            vt_load_file_to_mem "auto" $prefix/help.tar.gz vtoy_help_txt_mem
        fi

        loopback vt_help_tarfs mem:${vtoy_help_txt_mem_addr}:size:${vtoy_help_txt_mem_size}
        vt_cur_menu_lang vtCurLang
        if [ -f "(vt_help_tarfs)/help/${vtCurLang}.txt" ]; then
            cat "(vt_help_tarfs)/help/${vtCurLang}.txt"
        else
            cat "(vt_help_tarfs)/help/en_US.txt"
        fi        
        loopback -d vt_help_tarfs
    fi
}

function ventoy_load_menu_lang_file {
    vt_load_file_to_mem "auto" $prefix/menu.tar.gz vtoy_menu_lang_mem
    loopback vt_menu_tarfs mem:${vtoy_menu_lang_mem_addr}:size:${vtoy_menu_lang_mem_size}    
}

function get_os_type {
    set vtoy_os=Linux
    export vtoy_os

    if vt_str_begin "$vt_volume_id" "DLC Boot"; then
        if [ -f (loop)/DLCBoot.exe ]; then
            set vtoy_os=Windows
        fi
    else
        for file in "efi/microsoft/boot/bcd" "sources/boot.wim" "boot/bcd" "bootmgr.efi" "boot/etfsboot.com" ; do        
            if vt_file_exist_nocase (loop)/$file; then        
                set vtoy_os=Windows            
                break
            fi
        done
    fi

    if [ "$vtoy_os" = "Linux" ]; then
        if vt_strstr "$vt_system_id" "FreeBSD"; then
            set vtoy_os=Unix
            set vt_unix_type=FreeBSD
        elif [ -e (loop)/bin/freebsd-version ]; then
            set vtoy_os=Unix
            set vt_unix_type=FreeBSD
        elif [ -e (loop)/boot/kernel/geom_ventoy.ko ]; then
            set vtoy_os=Unix
            set vt_unix_type=FreeBSD
        elif vt_str_begin "$vt_system_id" "DragonFly"; then
            set vtoy_os=Unix
            set vt_unix_type=DragonFly
            
            
        elif [ -e (loop)/boot/kernel/kernel ]; then            
            if file --is-x86-kfreebsd (loop)/boot/kernel/kernel; then
                set vtoy_os=Unix
                set vt_unix_type=FreeBSD
            elif file --is-x86-knetbsd (loop)/boot/kernel/kernel; then
                set vtoy_os=Unix
                set vt_unix_type=NetBSD
            fi
        fi
    fi

    if [ -n "${vtdebug_flag}" ]; then
        echo ISO is "$vtoy_os"
    fi
}

function vt_check_compatible_pe {
    #Check for PE without external tools
    #set compatible if ISO file is less than 80MB
    if [ $vt_chosen_size -GT 33554432 -a $vt_chosen_size -LE 83886080 ]; then
        set ventoy_compatible=YES    
    fi

    return
}

function vt_check_compatible_linux {
    if vt_str_begin "$vt_volume_id" "embootkit"; then
        set ventoy_compatible=YES
    elif [ -e "$1/casper/tinycore.gz" ]; then
        set ventoy_compatible=YES
    fi

    return
}

function locate_initrd {
    vt_linux_locate_initrd 

    if [ -n "${vtdebug_flag}" ]; then        
        vt_linux_dump_initrd
        ventoy_debug_pause
    fi
}

function locate_wim {
    vt_windows_locate_wim_patch (loop) "$1"
    
    if [ -n "${vtdebug_flag}" ]; then
        echo '###############################################'
        vt_dump_wim_patch
        echo '###############################################'
        ventoy_debug_pause
    fi
}

function distro_specify_wim_patch {
    if [ -d (loop)/h3pe ]; then
        vt_windows_collect_wim_patch wim /BOOT/H3_10PE.WIM
        vt_windows_collect_wim_patch wim /BOOT/H3_7PE.WIM
        vt_windows_collect_wim_patch wim /BOOT/H3_8PE.WIM
        vt_windows_collect_wim_patch wim /BOOT/H3_81PE.WIM
    elif [ -d (loop)/2k10/winpe ]; then
        vt_windows_collect_wim_patch wim /2k10/winpe/w1086pe.wim
        vt_windows_collect_wim_patch wim /2k10/winpe/w8x86pe.wim
        vt_windows_collect_wim_patch wim /2k10/winpe/w7x86pe.wim
    fi  
}

function distro_specify_wim_patch_phase2 {
    if [ -f (loop)/boot/boot.wim ]; then
        vt_windows_collect_wim_patch wim /boot/boot.wim
    elif [ -f (loop)/sources/boot.wim ]; then
        vt_windows_collect_wim_patch wim /sources/boot.wim
    fi

    if vt_str_begin "$vt_volume_id" "DLC Boot"; then
        for vwfile in "/DLC1/WinPE/W11x64.wim" "/DLC1/WinPE/W10x64.wim" "/DLC1/WinPE/W10x86.wim"; do
            if [ -f (loop)/$vwfile ]; then
                vt_windows_collect_wim_patch wim $vwfile
            fi
        done
    fi
    
}


function distro_specify_initrd_file {
    if [ -e (loop)/boot/all.rdz ]; then
        vt_linux_specify_initrd_file /boot/all.rdz
    elif [ -e (loop)/boot/xen.gz ]; then 
        if [ -e (loop)/install.img ]; then
            vt_linux_specify_initrd_file /install.img
        fi
    elif [ -d (loop)/casper ]; then 
        if [ -e (loop)/casper/initrd ]; then
            vt_linux_specify_initrd_file /casper/initrd
        fi
        if [ -e (loop)/casper/initrd.gz ]; then
            vt_linux_specify_initrd_file /casper/initrd.gz
        fi
        if [ -e (loop)/casper/initrd-oem ]; then
            vt_linux_specify_initrd_file /casper/initrd-oem
        fi
    elif [ -e (loop)/boot/grub/initrd.xz ]; then
        vt_linux_specify_initrd_file /boot/grub/initrd.xz
    elif [ -e (loop)/initrd.gz ]; then
        vt_linux_specify_initrd_file /initrd.gz
    elif [ -e (loop)/slax/boot/initrfs.img ]; then
        vt_linux_specify_initrd_file /slax/boot/initrfs.img
    elif [ -e (loop)/minios/boot/initrfs.img ]; then
        vt_linux_specify_initrd_file /minios/boot/initrfs.img
    elif [ -e (loop)/pmagic/initrd.img ]; then
        vt_linux_specify_initrd_file /pmagic/initrd.img
    elif [ -e (loop)/boot/initrd.xz ]; then
        vt_linux_specify_initrd_file /boot/initrd.xz
    elif [ -e (loop)/boot/initrd.gz ]; then
        vt_linux_specify_initrd_file /boot/initrd.gz
    elif [ -f (loop)/boot/initrd ]; then
        vt_linux_specify_initrd_file /boot/initrd
    elif [ -f (loop)/boot/x86_64/loader/initrd ]; then
        vt_linux_specify_initrd_file /boot/x86_64/loader/initrd
    elif [ -f (loop)/boot/initramfs-x86_64.img ]; then
        vt_linux_specify_initrd_file /boot/initramfs-x86_64.img
    elif [ -f (loop)/boot/isolinux/initramfs_data64.cpio.gz ]; then 
        vt_linux_specify_initrd_file /boot/isolinux/initramfs_data64.cpio.gz
    elif [ -f (loop)/boot/initrd.img ]; then 
        vt_linux_specify_initrd_file /boot/initrd.img
        
    fi
    
    if [ -f (loop)/isolinux/initrd.gz ]; then 
        vt_linux_specify_initrd_file /isolinux/initrd.gz
    fi
    
    if vt_str_begin "$vt_volume_id" "QUBES"; then 
        vt_linux_specify_initrd_file /images/pxeboot/initrd.img
    fi
    
    if [ "$vt_chosen_size" = "1133375488" ]; then
        if [ -d (loop)/boot/grub/x86_64-efi ]; then
            vt_cpio_busybox64 "64h"
        fi
    fi
}


function distro_specify_initrd_file_phase2 {
    if [ -f (loop)/boot/initrd.img ]; then
        vt_linux_specify_initrd_file /boot/initrd.img
    elif [ -f (loop)/Setup/initrd.gz ]; then
        vt_linux_specify_initrd_file /Setup/initrd.gz
    elif [ -f (loop)/isolinux/initramfs ]; then
        vt_linux_specify_initrd_file /isolinux/initramfs
    elif [ -f (loop)/boot/iniramfs.igz ]; then
        vt_linux_specify_initrd_file /boot/iniramfs.igz
    elif [ -f (loop)/initrd-x86_64 ]; then
        vt_linux_specify_initrd_file /initrd-x86_64
    elif [ -f (loop)/live/initrd.img ]; then 
        vt_linux_specify_initrd_file /live/initrd.img
    elif [ -f (loop)/initrd.img ]; then 
        vt_linux_specify_initrd_file /initrd.img
    elif [ -f (loop)/sysresccd/boot/x86_64/sysresccd.img ]; then 
        vt_linux_specify_initrd_file /sysresccd/boot/x86_64/sysresccd.img
    elif [ -f (loop)/CDlinux/initrd ]; then 
        vt_linux_specify_initrd_file /CDlinux/initrd
    elif [ -f (loop)/parabola/boot/x86_64/parabolaiso.img ]; then 
        vt_linux_specify_initrd_file /parabola/boot/x86_64/parabolaiso.img
        if [ -f (loop)/parabola/boot/i686/parabolaiso.img ]; then 
            vt_linux_specify_initrd_file /parabola/boot/i686/parabolaiso.img
        fi
    elif [ -f (loop)/parabola/boot/x86_64/initramfs-linux-libre.img ]; then
        vt_linux_specify_initrd_file /parabola/boot/x86_64/initramfs-linux-libre.img
        if [ -f (loop)/parabola/boot/i686/initramfs-linux-libre.img ]; then
            vt_linux_specify_initrd_file /parabola/boot/i686/initramfs-linux-libre.img
        fi
    elif [ -f (loop)/hyperbola/boot/x86_64/hyperiso.img ]; then 
        vt_linux_specify_initrd_file /hyperbola/boot/x86_64/hyperiso.img
        if [ -f (loop)/hyperbola/boot/i686/hyperiso.img ]; then 
            vt_linux_specify_initrd_file /hyperbola/boot/i686/hyperiso.img
        fi
    elif [ -f (loop)/EFI/BOOT/initrd.img ]; then 
        #Qubes
        vt_linux_specify_initrd_file /EFI/BOOT/initrd.img
        if [ "$grub_platform" != "pc" ]; then
            vt_add_replace_file 0 "initrd.img"    
        fi
    elif [ -f (loop)/initrd ]; then 
        vt_linux_specify_initrd_file /initrd
    elif [ -f (loop)/live/initrd1 ]; then 
        vt_linux_specify_initrd_file /live/initrd1
    elif [ -f (loop)/isolinux/initrd.img ]; then 
        vt_linux_specify_initrd_file /isolinux/initrd.img
    elif [ -f (loop)/isolinux/initrd.gz ]; then 
        vt_linux_specify_initrd_file /isolinux/initrd.gz
    elif [ -f (loop)/syslinux/kernel/initramfs.gz ]; then 
        vt_linux_specify_initrd_file /syslinux/kernel/initramfs.gz    
    elif vt_strstr "$vt_volume_id" "Daphile"; then
        vt_linux_parse_initrd_isolinux   (loop)/isolinux/
    elif [ -f (loop)/boot/rootfs.xz ]; then 
        vt_linux_specify_initrd_file /boot/rootfs.xz
        if [ "$grub_platform" != "pc" ]; then
            vt_add_replace_file 0 "minimal\\x86_64\\rootfs.xz"
        fi
    elif [ -f (loop)/arch/boot/x86_64/archiso.img ]; then 
        vt_linux_specify_initrd_file /arch/boot/x86_64/archiso.img
        if [ "$grub_platform" != "pc" ]; then
            vt_add_replace_file 0 "EFI\\archiso\\archiso.img"
        fi
    elif [ -f (loop)/blackarch/boot/x86_64/archiso.img ]; then 
        vt_linux_specify_initrd_file /blackarch/boot/x86_64/archiso.img
    elif [ -f (loop)/blackarch/boot/x86_64/initramfs-linux.img ]; then 
        vt_linux_specify_initrd_file /blackarch/boot/x86_64/initramfs-linux.img
        
    elif [ -f (loop)/install.amd/initrd.gz ]; then
        vt_linux_specify_initrd_file /live/initrd2.img
        vt_linux_specify_initrd_file /install.amd/initrd.gz
        vt_linux_specify_initrd_file /install.amd/gtk/initrd.gz
    elif [ -f (loop)/boot/grub/kernels.cfg ]; then
        vt_linux_parse_initrd_grub  file  (loop)/boot/grub/kernels.cfg
    elif [ -f (loop)/austrumi/initrd.gz ]; then
        vt_linux_specify_initrd_file /austrumi/initrd.gz
        if [ -f (loop)/EFI/BOOT/bootx64.efi ]; then
            vt_cpio_busybox64 "64h"
        fi
    elif [ -f (loop)/boot/initfs.x86_64-efi ]; then
        vt_linux_specify_initrd_file /boot/initfs.x86_64-efi
        if [ -f (loop)/boot/initfs.i386-pc ]; then
            vt_linux_specify_initrd_file /boot/initfs.i386-pc
        fi
    elif [ -f (loop)/antiX/initrd.gz ]; then
        vt_linux_specify_initrd_file /antiX/initrd.gz
    elif [ -f (loop)/360Disk/initrd.gz ]; then
        vt_linux_specify_initrd_file /360Disk/initrd.gz
    elif [ -f (loop)/porteus/initrd.xz ]; then
        vt_linux_specify_initrd_file /porteus/initrd.xz
    elif [ -f (loop)/pyabr/boot/initrfs.img ]; then
        vt_linux_specify_initrd_file /pyabr/boot/initrfs.img
    elif [ -f (loop)/initrd0.img ]; then
        vt_linux_specify_initrd_file /initrd0.img
    elif [ -f (loop)/sysresccd/boot/i686/sysresccd.img ]; then
        vt_linux_specify_initrd_file /sysresccd/boot/i686/sysresccd.img
    elif [ -f (loop)/boot/full.cz ]; then
        vt_linux_specify_initrd_file /boot/full.cz
    elif [ -f (loop)/images/pxeboot/initrd.img ]; then
        vt_linux_specify_initrd_file /images/pxeboot/initrd.img
    elif [ -f (loop)/live/initrd ]; then
        vt_linux_specify_initrd_file /live/initrd
    elif [ -f (loop)/initramfs-linux.img ]; then
        vt_linux_specify_initrd_file /initramfs-linux.img
    elif [ -f (loop)/boot/isolinux/initrd.gz ]; then
        vt_linux_specify_initrd_file /boot/isolinux/initrd.gz        
    fi
}


function ventoy_get_ghostbsd_ver {
    # fallback to parse version from elf /boot/kernel/kernel
    set vt_freebsd_ver=xx
}

function ventoy_get_furybsd_ver {
    set vt_freebsd_ver=12.x
    if regexp --set 1:vtFuryVer "(14|13)\.[0-9]" "$2"; then
        set vt_freebsd_ver=${vtFuryVer}.x
    fi
}

function ventoy_get_freenas_ver {   
    set vt_freebsd_ver=11.x

    if [ -e (loop)/FreeNAS-MANIFEST ]; then
        vt_parse_freenas_ver (loop)/FreeNAS-MANIFEST vt_freenas_ver
        if regexp --set 1:vtNasVer "^(14|13|12|11)\.[0-9]" "$vt_freenas_ver"; then
            set vt_freebsd_ver=${vtNasVer}.x        
        fi
    fi
}

function ventoy_get_truenas_ver {   
    set vt_freebsd_ver=12.x

    if [ -e (loop)/TrueNAS-MANIFEST ]; then
        vt_parse_freenas_ver (loop)/TrueNAS-MANIFEST vt_truenas_ver
        if regexp --set 1:vtTNasVer "^(14|13|12|11)\.[0-9]" "$vt_truenas_ver"; then
            set vt_freebsd_ver=${vtTNasVer}.x        
        fi
    fi
}

function ventoy_get_midnightbsd_ver {   
    if vt_str_begin "$vt_volume_id" "1_"; then
        set vt_freebsd_ver=11.x
    elif vt_str_begin "$vt_volume_id" "2_"; then
        set vt_freebsd_ver=2.x
    elif vt_str_begin "$vt_volume_id" "3_"; then
        set vt_freebsd_ver=3.x
    fi
}

function ventoy_freebsd_proc {
    set vtFreeBsdDistro=FreeBSD
    set vt_freebsd_ver=xx

    if [ -e (loop)/boot/kernel/geom_ventoy.ko ]; then
        vt_unix_ko_fillmap /boot/kernel/geom_ventoy.ko
        return
    fi

    if vt_strstr "$vt_volume_id" "GHOSTBSD"; then
        ventoy_get_ghostbsd_ver "$1" "${chosen_path}"
    elif vt_strstr "$vt_volume_id" "FREENAS"; then
        ventoy_get_freenas_ver "$1" "${chosen_path}"
    elif vt_strstr "$vt_volume_id" "TRUENAS"; then
        ventoy_get_truenas_ver "$1" "${chosen_path}"
    elif vt_strstr "$vt_volume_id" "FURYBSD"; then
        ventoy_get_furybsd_ver "$1" "${chosen_path}"  
    elif regexp --set 1:vtBsdVerNum "^(14|13|12|11|10|9)_[0-9]" "$vt_volume_id"; then
        set vt_freebsd_ver=${vtBsdVerNum}.x    
    elif [ -d (loop)/usr/midnightbsd-dist ]; then
        ventoy_get_midnightbsd_ver "$1" "${chosen_path}"
        set vtFreeBsdDistro=MidnightBSD
    elif [ -e (loop)/bin/freebsd-version ]; then
        vt_unix_parse_freebsd_ver (loop)/bin/freebsd-version vt_userland_ver        
        if regexp --set 1:vtBsdVerNum "\"(14|13|12|11|10|9)\.[0-9]-" "$vt_userland_ver"; then
            set vt_freebsd_ver=${vtBsdVerNum}.x
        fi        
    elif [ -e (loop)/README.TXT ]; then
        vt_1st_line (loop)/README.TXT vt_freebsd_line1
        if regexp --set 1:vtBsdVerNum "FreeBSD (14|13|12|11|10|9)\.[0-9]-" "$vt_freebsd_line1"; then
            set vt_freebsd_ver=${vtBsdVerNum}.x        
        fi
    elif vt_strstr "${chosen_path}" "MidnightBSD"; then
        set vt_freebsd_ver=9.x
    fi

    
    if [ -e (loop)/usr/freebsd-dist/cloninst.sh ]; then
        set vtFreeBsdDistro=ClonOS
    fi
    
    set vt_freebsd_bit=64
    for file in "/boot/kernel/kernel" "/boot/kernel/kernel.gz"; do
        if [ -e (loop)/$file ]; then    
            if file --is-i386-kfreebsd (loop)/$file; then
                set vt_freebsd_bit=32
            fi
            break
        fi
    done

    if [ "$vt_freebsd_ver" = "xx" ]; then
        if [ -e (loop)/boot/kernel/kernel ]; then
            vt_unix_parse_freebsd_ver_elf (loop)/boot/kernel/kernel $vt_freebsd_bit vt_freebsd_ver
        elif [ -e (loop)/boot/kernel/kernel.gz ]; then
            vt_unix_parse_freebsd_ver_elf (loop)/boot/kernel/kernel.gz $vt_freebsd_bit vt_freebsd_ver
        fi
        
        if [ "$vt_freebsd_ver" = "xx" ]; then
            set vt_freebsd_ver=14.x
        fi
    fi

    if [ "$vt_freebsd_ver" = "14.x" ]; then
        if [ -e (loop)/boot/lua/brand-pfSense.lua ]; then
            set vtFreeBsdDistro=pfSense
        fi
    fi


    if [ -n "${vtdebug_flag}" ]; then
        echo "This is $vtFreeBsdDistro $vt_freebsd_ver ${vt_freebsd_bit}bit"
    fi
    
    unset vt_unix_mod_path
    for file in "/COPYRIGHT" "/FreeNAS-MANIFEST" "/TrueNAS-MANIFEST" "/version" "/etc/fstab"; do   
        if [ -e (loop)${file} ]; then                    
            set vt_unix_mod_path=${file}
            break
        fi
    done
    
    if [ -n "$vt_unix_mod_path" ]; then
        vt_unix_replace_ko $vt_unix_mod_path (vtunix)/ventoy_unix/$vtFreeBsdDistro/geom_ventoy_ko/$vt_freebsd_ver/$vt_freebsd_bit/geom_ventoy.ko.xz
        vt_unix_replace_conf FreeBSD "${1}${chosen_path}"
    elif [ -e (loop)/easyre.ufs.uzip ]; then
        vt_unix_replace_ko "/boot/grub/i386-pc/linux.mod" (vtunix)/ventoy_unix/$vtFreeBsdDistro/geom_ventoy_ko/$vt_freebsd_ver/$vt_freebsd_bit/geom_ventoy.ko.xz
        if [ "$grub_platform" = "pc" ]; then
            vt_unix_replace_grub_conf "/boot/grub/i386-pc/linux.mod" "cd9"
        else
            vt_unix_replace_conf FreeBSD "${1}${chosen_path}" "cd9"
        fi
    fi
}

function ventoy_dragonfly_proc {

    unset vt_unix_mod_path
    for file in "/boot/kernel/initrd.img.gz"; do
        if [ -e (loop)${file} ]; then                    
            set vt_unix_mod_path=${file}
            break
        fi
    done

    vt_unix_replace_ko $vt_unix_mod_path ${vtoy_path}/dragonfly.mfs.xz
    vt_unix_fill_image_desc
    vt_unix_gzip_new_ko
    vt_unix_replace_conf DragonFly "${1}${chosen_path}"
}

function ventoy_unix_comm_proc {
    vt_unix_reset
    
    vt_unix_check_vlnk "${1}${chosen_path}"
    
    if [ "$ventoy_compatible" = "NO" ]; then
        loopback vtunix $vtoy_efi_part/ventoy/ventoy_unix.cpio
        
        if [ "$vt_unix_type" = "FreeBSD" ]; then
            ventoy_freebsd_proc "$1" "${chosen_path}"
        elif [ "$vt_unix_type" = "DragonFly" ]; then
            ventoy_dragonfly_proc "$1" "${chosen_path}"        
        elif [ "$vt_unix_type" = "NetBSD" ]; then
            echo "NetBSD not supported"
            
            
        else
            if [ -n "${vtdebug_flag}" ]; then
                echo "Unknown unix type"
            fi
        fi
    fi
    
    vt_unix_chain_data "${1}${chosen_path}"
    ventoy_debug_pause
}


function uefi_windows_menu_func {
    vt_windows_reset

    unset vt_cur_wimboot_mode
    if vt_check_mode 4 "$vt_chosen_name"; then
        set vt_cur_wimboot_mode=1
    fi

    if [ "$ventoy_compatible" = "NO" -o "$vt_cur_wimboot_mode" = "1" ]; then
        if [ "$ventoy_fs_probe" = "iso9660" ]; then
            loopback -d loop
            vt_iso9660_nojoliet 1            
            loopback loop "$1$2"
        fi
        
        for file in "efi/microsoft/boot/bcd"; do
            vt_windows_collect_wim_patch bcd (loop)/$file                
        done

        vt_windows_count_wim_patch vt_wim_cnt
        if [ $vt_wim_cnt -eq 0 ]; then
            distro_specify_wim_patch_phase2
        fi
        
        ventoy_debug_pause        
        locate_wim "${chosen_path}"
    fi
    
    vt_windows_chain_data "${1}${chosen_path}"
    ventoy_debug_pause

    if [ "$vt_cur_wimboot_mode" = "1" ]; then
        vtoy_wimboot_func
    fi

    if [ -n "$vtoy_chain_mem_addr" ]; then
        ventoy_acpi_param ${vtoy_chain_mem_addr} 2048
        ventoy_max_resolution
        chainloader ${vtoy_path}/ventoy_${VTOY_EFI_ARCH}.efi  env_param=${env_param} isoefi=${LoadIsoEfiDriver} iso_${ventoy_fs_probe} ${vtdebug_flag} mem:${vtoy_chain_mem_addr}:size:${vtoy_chain_mem_size}
        boot
    else
        echo "chain empty failed"
        ventoy_pause
    fi
}

function uefi_find_replace_initrd {
    if vt_get_efi_vdisk_offset "${1}${2}" vt_efivdisk_offset; then
        loopback -s $vt_efivdisk_offset vtefivdisk "${1}${2}"
        
        unset vt_rp_initrd
        vt_search_replace_initrd (vtefivdisk) vt_rp_initrd
        
        if [ -n "$vt_rp_initrd" ]; then
            vt_add_replace_file $3 "$vt_rp_initrd"
        fi

        loopback -d vtefivdisk
        ventoy_debug_pause
    fi
}

function uefi_linux_menu_func {
    
    if [ "$ventoy_compatible" = "NO" ]; then    
        
        if [ "$ventoy_fs_probe" = "udf" ]; then
            loopback -d loop            
            set ventoy_fs_probe=iso9660
            loopback loop "$1$2"
        fi
        
        vt_load_cpio  $vtoy_path   "$2" "$1" "busybox=$ventoy_busybox_ver"
        
        vt_linux_clear_initrd
        
        if [ -d (loop)/pmagic ]; then
            vt_linux_specify_initrd_file /pmagic/initrd.img
        else
            for file in "boot/grub/grub.cfg" "EFI/BOOT/grub.cfg" "EFI/boot/grub.cfg" "efi/boot/grub.cfg" "EFI/BOOT/BOOTX64.conf" "/grub/grub.cfg" "EFI/BOOT/grub/grub.cfg"; do
                if [ -e (loop)/$file ]; then                    
                    vt_linux_parse_initrd_grub  file  (loop)/$file
                fi
            done
        fi

        # special process for special distros
        if [ -d (loop)/loader/entries ]; then
            vt_linux_parse_initrd_grub  dir  (loop)/loader/entries/
        elif [ -d (loop)/boot/grub ]; then
            vt_linux_parse_initrd_grub  dir  (loop)/boot/grub/
        fi
        
        distro_specify_initrd_file
        
        vt_linux_initrd_count vtcount
        
        if [ $vtcount -eq 0 ]; then
            if [ -e (loop)/EFI/boot/livegrub.cfg ]; then
                vt_linux_parse_initrd_grub  file  (loop)/EFI/boot/livegrub.cfg
            fi
            distro_specify_initrd_file_phase2
            
            if [ "$vt_efi_dir" = "NO" ]; then
                if [ -f (loop)/efi.img ];  then
                    vt_add_replace_file 0 "initrd"
                fi
            fi
        fi
        
        locate_initrd
        
        if [ -d (loop)/loader/entries ]; then
            vt_linux_get_main_initrd_index vtindex
            
            if [ -d (loop)/arch ]; then
                if [ -f (loop)/arch/boot/x86_64/archiso.img ]; then
                    vt_add_replace_file $vtindex "EFI\\archiso\\archiso.img"
                elif [ -f (loop)/arch/boot/x86_64/initramfs-linux.img ]; then
                    vt_add_replace_file $vtindex "arch\\boot\\x86_64\\initramfs-linux.img"
                elif [ -f (loop)/boot/initramfs_x86_64.img ]; then
                    vt_add_replace_file $vtindex "boot\\initramfs_x86_64.img"
                fi
            elif [ -d (loop)/blackarch ]; then
                if [ -f (loop)/blackarch/boot/x86_64/archiso.img ]; then
                    vt_add_replace_file $vtindex "EFI\\archiso\\archiso.img"
                elif [ -f (loop)/blackarch/boot/x86_64/initramfs-linux.img ]; then
                    vt_add_replace_file $vtindex "blackarch\\boot\\x86_64\\initramfs-linux.img"
                fi
            elif [ -d (loop)/anarchy ]; then
                if [ -f (loop)/anarchy/boot/x86_64/initramfs-linux.img ]; then
                    vt_add_replace_file $vtindex "anarchy\\boot\\x86_64\\initramfs-linux.img"
                fi
            elif [ -d (loop)/parabola ]; then
                if [ -f (loop)/parabola/boot/x86_64/parabolaiso.img ]; then
                    vt_add_replace_file $vtindex "EFI\\parabolaiso\\parabolaiso.img"
                elif [ -f (loop)/parabola/boot/x86_64/initramfs-linux-libre.img ]; then
                    vt_add_replace_file $vtindex "parabola\\boot\\x86_64\\initramfs-linux-libre.img"
                fi
            elif [ -f (loop)/EFI/BOOT/initrd.gz ]; then
                vt_add_replace_file $vtindex "EFI\\BOOT\\initrd.gz"
            elif [ -f (loop)/loader/entries/thinstation.conf ]; then
                vt_add_replace_file $vtindex "boot\\initrd"
            elif [ -f (loop)/loader/entries/pisi-efi-x86_64.conf ]; then
                vt_add_replace_file $vtindex "EFI\\pisi\\initrd.img"
            fi

            vt_get_replace_file_cnt vt_replace_cnt
            if [ $vt_replace_cnt -eq 0 ]; then
                uefi_find_replace_initrd "$1" "$2" $vtindex
            fi
        elif [ -d (loop)/EFI/boot/entries ]; then
            if [ -f (loop)/parabola/boot/x86_64/parabolaiso.img ]; then
                vt_add_replace_file 0 "EFI\\parabolaiso\\parabolaiso.img"
            elif [ -f (loop)/hyperbola/boot/x86_64/hyperiso.img ]; then
                vt_add_replace_file 0 "EFI\\hyperiso\\hyperiso.img"
            fi
        elif [ -d (loop)/EFI/BOOT/entries ]; then
            vt_linux_get_main_initrd_index vtindex

            if [ -f (loop)/parabola/boot/x86_64/parabolaiso.img ]; then
                vt_add_replace_file 0 "EFI\\parabolaiso\\parabolaiso.img"
            elif [ -f (loop)/parabola/boot/x86_64/initramfs-linux-libre.img ]; then
                vt_add_replace_file $vtindex "parabola\\boot\\x86_64\\initramfs-linux-libre.img"
            fi
        elif [ -e (loop)/syslinux/alt0/full.cz ]; then
            vt_add_replace_file 0 "EFI\\BOOT\\full.cz"            
            set FirstTryBootFile='@EFI@<EMAIL>'
            
        elif vt_str_begin "$vt_volume_id" "SolusLive"; then
            vt_add_replace_file 0 "initrd"

        fi
        
    fi
    
    vt_linux_chain_data "${1}${chosen_path}"

    if [ -n "$LoadIsoEfiDriver" -a $vt_chosen_size -LT 104857600 ]; then
        if [ -f (loop)/efi/clover/cloverx64.efi ]; then
            unset LoadIsoEfiDriver
        fi
    fi

    if [ -n "$vtoy_chain_mem_addr" ]; then
        ventoy_acpi_param ${vtoy_chain_mem_addr} 2048
        ventoy_cli_console     

        unset vtGrub2Mode
        if vt_check_mode 3 "$vt_chosen_name"; then
            set vtGrub2Mode=1
        elif vt_str_begin "$vt_volume_id" "HOLO_"; then
            if [ -d (loop)/loader/entries ]; then
                set vtGrub2Mode=1
            fi
        elif vt_str_begin "$vt_volume_id" "KRD"; then
            if [ -f (loop)/boot/grub/grub.cfg.sig ]; then
                set vtGrub2Mode=1
            fi
        fi

        if [ -n "$vtGrub2Mode" ]; then
            ventoy_debug_pause
        else
            if [ "$VTOY_EFI_ARCH" != "mips" ]; then
                chainloader ${vtoy_path}/ventoy_${VTOY_EFI_ARCH}.efi fallback env_param=${env_param} isoefi=${LoadIsoEfiDriver} FirstTry=${FirstTryBootFile} ${vtdebug_flag} mem:${vtoy_chain_mem_addr}:size:${vtoy_chain_mem_size}
                boot
            fi
        fi

        # fallback
        set vtback_root=$root        
        vt_push_last_entry
        set vtback_theme=$theme
        unset theme
        
        vt_trailer_cpio "$vtoy_iso_part" "$vt_chosen_path" noinit
        vt_set_boot_opt rdinit=/vtoy/vtoy

        set root=(loop)
        set vtback_cfg_find=0
        for cfg in "/boot/grub/grub.cfg" "/EFI/BOOT/grub.cfg" "/EFI/debian/grub.cfg" "EFI/boot/grub.cfg" "efi/boot/grub.cfg" "/grub/grub.cfg" "EFI/BOOT/BOOTX64.conf"; do
            if [ -e "$cfg" ]; then
                set vtback_cfg_find=1
                configfile "$cfg"
                break
            fi
        done
        if [ $vtback_cfg_find -eq 0 ]; then
            if [ -f (loop)/loader/loader.conf -a -d (loop)/loader/entries ]; then
                if vt_str_begin "$vt_volume_id" "HOLO_"; then
                    set root=(loop,2)
                    vt_systemd_menu (loop,2) vt_sys_menu_mem
                else
                    vt_systemd_menu (loop) vt_sys_menu_mem
                fi
                set vtback_cfg_find=1
                configfile "mem:${vt_sys_menu_mem_addr}:size:${vt_sys_menu_mem_size}"
            fi
        fi
        
        if [ $vtback_cfg_find -eq 0 ]; then
            if [ -f (loop)/boot/isolinux/syslnx64.cfg ]; then
                syslinux_configfile (loop)/boot/isolinux/syslnx64.cfg
                set vtback_cfg_find=1
            elif [ -f (loop)/boot/syslinux/porteus.cfg ]; then
                syslinux_configfile (loop)/boot/syslinux/porteus.cfg
                set vtback_cfg_find=1
            fi
        fi

        if [ "$vtback_cfg_find" = "0" ]; then
            echo " "
            echo "No bootfile found for UEFI!"
            echo "Maybe the image does not support $VTOY_EFI_ARCH UEFI"
            echo " "
            sleep 30
        fi

        vt_unset_boot_opt
        set root=$vtback_root        
        set theme=$vtback_theme
        vt_pop_last_entry
        ventoy_gui_console
    else
        echo "chain empty failed"
        ventoy_pause
    fi
}

function uefi_unix_menu_func {
    ventoy_unix_comm_proc $1 "${chosen_path}"
    
    if [ -n "$vtoy_chain_mem_addr" ]; then
        ventoy_acpi_param ${vtoy_chain_mem_addr} 2048
        ventoy_cli_console
        chainloader ${vtoy_path}/ventoy_${VTOY_EFI_ARCH}.efi  env_param=${env_param} isoefi=${LoadIsoEfiDriver} FirstTry=${FirstTryBootFile} ${vtdebug_flag} mem:${vtoy_chain_mem_addr}:size:${vtoy_chain_mem_size}
        boot
    else
        echo "chain empty failed"
        ventoy_pause
    fi
}

function ventoy_reset_nojoliet {
    if vt_str_begin "$vt_volume_id" "ARCARESCUE"; then
        vt_iso9660_nojoliet 1
    else
        vt_iso9660_nojoliet 0
    fi
    
    vt_append_extra_sector 0
}

function uefi_iso_menu_func {
    if [ -n "$vtisouefi" ]; then
        set LoadIsoEfiDriver=on
        unset vtisouefi
    elif vt_check_mode 2 "$vt_chosen_name"; then
        set LoadIsoEfiDriver=on
    else
        unset LoadIsoEfiDriver
    fi

    set chosen_path="$2"
    vt_select_auto_install "${chosen_path}"
    vt_select_persistence "${chosen_path}"

    if ! vt_is_udf "${1}${chosen_path}"; then
        # Lenovo EasyStartup need an addional sector for boundary check
        if vt_str_begin "$vt_volume_id" "EasyStartup"; then
            vt_skip_svd "${vtoy_iso_part}${vt_chosen_path}"
            vt_append_extra_sector 1
        fi
    fi
    
    if [ -d (loop)/EFI ]; then
        set vt_efi_dir=YES
    elif [ -d (loop)/efi ]; then
        set vt_efi_dir=YES
    else
        set vt_efi_dir=NO
    fi
    
    if [ -n "$vtcompat" ]; then
        set ventoy_compatible=YES
        unset vtcompat
    elif vt_check_mode 1 "$vt_chosen_name"; then
        set ventoy_compatible=YES
    else
        vt_check_compatible (loop)        
    fi
    
    vt_img_sector "${1}${chosen_path}"
    
    if [ "$ventoy_fs_probe" = "iso9660" ]; then
        vt_select_conf_replace "${1}" "${chosen_path}"
    fi
    
    if [ "$vtoy_os" = "Windows" ]; then
        vt_check_compatible_pe (loop)        
        uefi_windows_menu_func  "$1" "${chosen_path}"
    elif [ "$vtoy_os" = "Unix" ]; then
        uefi_unix_menu_func "$1" "${chosen_path}"
    else
        vt_check_compatible_linux (loop)
        uefi_linux_menu_func  "$1" "${chosen_path}"
    fi

    ventoy_gui_console    
}

function uefi_iso_memdisk {    
    echo 'Loading ISO file to memory ...'
    vt_load_img_memdisk "${1}${2}" vtoy_iso_buf
    
    ventoy_cli_console
    chainloader ${vtoy_path}/ventoy_${VTOY_EFI_ARCH}.efi memdisk env_param=${env_param} isoefi=${LoadIsoEfiDriver} ${vtdebug_flag} mem:${vtoy_iso_buf_addr}:size:${vtoy_iso_buf_size}
    boot
    
    ventoy_gui_console
}

function vtoy_windows_wimboot {
    if [ -f (loop)/x86/sources/boot.wim -a -f (loop)/x64/sources/boot.wim ]; then
        vt_sel_wimboot vtoy_wimboot_bit
        if [ "$vtoy_wimboot_bit" = "32" ]; then
            set vtoy_wimboot_prefix=(loop)/x86
        else
            set vtoy_wimboot_prefix=(loop)/x64
        fi
    else
        set vtoy_wimboot_prefix=(loop)
        if vt_is_pe64 $vtoy_wimboot_prefix/setup.exe; then
            set vtoy_wimboot_bit=64
        else
            set vtoy_wimboot_bit=32
        fi
    fi

    if [ -n "${vtdebug_flag}" ]; then
        echo vtoy_wimboot_prefix=$vtoy_wimboot_prefix vtoy_wimboot_bit=$vtoy_wimboot_bit vt_wimkernel=$vt_wimkernel
    fi

    vt_windows_wimboot_data "$vtoy_wimboot_prefix/sources/boot.wim" vtoy_init_exe vtoy_wim_bit
    
    if [ "$grub_platform" = "pc" ]; then
        linux16  "$vtoy_path/$vt_wimkernel"  quiet
        ventoy_debug_pause

        vt_set_wim_load_prompt 1 "Loading files......"
        initrd16 newc:winpeshl.exe:mem:${vtoy_wimboot_mem_addr}:size:${vtoy_wimboot_mem_size} \
             newc:vtoy_wimboot:$vtoy_wimboot_prefix/boot/bcd \
             newc:bcd:$vtoy_wimboot_prefix/boot/bcd \
             newc:boot.sdi:$vtoy_wimboot_prefix/boot/boot.sdi \
             newc:boot.wim:$vtoy_wimboot_prefix/sources/boot.wim
        vt_set_wim_load_prompt 0
        boot
    else
        vt_set_wim_load_prompt 1 "Loading files......"
        vt_load_file_to_mem "nodecompress" $vtoy_wimboot_prefix/sources/boot.wim vtoy_wimfile_mem
        vt_set_wim_load_prompt 0

        if [ $? -eq 0 ]; then
            set vtoy_wimfile_path=mem:${vtoy_wimfile_mem_addr}:size:${vtoy_wimfile_mem_size}
        else
            set vtoy_wimfile_path=$vtoy_wimboot_prefix/sources/boot.wim
        fi

        ventoy_cli_console
        chainloader "$vtoy_path/$vt_wimkernel" quiet \
            "vf=winpeshl.exe:mem:${vtoy_wimboot_mem_addr}:size:${vtoy_wimboot_mem_size}" \
            "vf=vtoy_wimboot:$vtoy_wimboot_prefix/boot/bcd" \
            "vf=bcd:$vtoy_wimboot_prefix/boot/bcd" \
            "vf=boot.sdi:$vtoy_wimboot_prefix/boot/boot.sdi" \
            "vf=boot.wim:$vtoy_wimfile_path" \
            pfsize=$vtoy_chain_file_size  \
            pfread=$vtoy_chain_file_read
        boot
        ventoy_gui_console
    fi
}

function vtoy_winpe_wimboot {
    unset vtoy_boot_sdi_legacy
    unset vtoy_boot_sdi_efi
    
    set vtoy_wimboot_prefix=(loop)    
    set vtoy_wim_path="$1"
    
    if [ -n "${vtdebug_flag}" ]; then
        echo "winpe_wimboot $1 $2 $3"
    fi
    
    if [ "$2" != "0" ]; then
        set vtoy_boot_sdi_legacy="newc:boot.sdi:$vtoy_wimboot_prefix/$2"
        set vtoy_boot_sdi_efi="vf=boot.sdi:$vtoy_wimboot_prefix/$2"
    fi

    vt_windows_wimboot_data $vtoy_wimboot_prefix/$vtoy_wim_path vtoy_init_exe vtoy_wim_bit
    
    if [ "$grub_platform" = "pc" ]; then
        linux16  "$vtoy_path/$vt_wimkernel" quiet
        ventoy_debug_pause

        vt_set_wim_load_prompt 1 "Loading files......"         
        initrd16 newc:$vtoy_init_exe:mem:${vtoy_wimboot_mem_addr}:size:${vtoy_wimboot_mem_size} \
             newc:vtoy_wimboot:$vtoy_path/$vt_wimkernel \
             newc:bootmgr.exe:mem:${vtoy_pe_bootmgr_mem_addr}:size:${vtoy_pe_bootmgr_mem_size} \
             newc:bcd:mem:${vtoy_pe_bcd_mem_addr}:size:${vtoy_pe_bcd_mem_size} \
             $vtoy_boot_sdi_legacy \
             newc:boot.wim:$vtoy_wimboot_prefix/$vtoy_wim_path
        vt_set_wim_load_prompt 0        
        boot
    else
        if [ "$VTOY_EFI_ARCH" = "x64" -a "$vtoy_wim_bit" = "32" ]; then
            echo -e "\nThis is 32bit Windows and does NOT support x86_64 UEFI firmware.\n"
            echo -e "这是32位的 Windows 系统，不支持当前的64位 UEFI 环境。\n"
        fi

        vt_set_wim_load_prompt 1 "Loading files......"
        vt_load_file_to_mem "nodecompress" $vtoy_wimboot_prefix/$vtoy_wim_path vtoy_wimfile_mem
        vt_set_wim_load_prompt 0

        if [ $? -eq 0 ]; then
            set vtoy_wimfile_path=mem:${vtoy_wimfile_mem_addr}:size:${vtoy_wimfile_mem_size}
        else
            set vtoy_wimfile_path=$vtoy_wimboot_prefix/$vtoy_wim_path
        fi
        
        unset vtoy_boot_efi_path
        if [ -F (loop)/efi/boot/boot${VTOY_EFI_ARCH}.efi ]; then
            set vtoy_boot_efi_path="vf=bootx64.efi:(loop)/efi/boot/boot${VTOY_EFI_ARCH}.efi"
        fi

        ventoy_cli_console
        chainloader "$vtoy_path/$vt_wimkernel" quiet \
            "vf=$vtoy_init_exe:mem:${vtoy_wimboot_mem_addr}:size:${vtoy_wimboot_mem_size}" \
            "vf=vtoy_wimboot:$vtoy_path/$vt_wimkernel" \
            "vf=bcd:mem:${vtoy_pe_bcd_mem_addr}:size:${vtoy_pe_bcd_mem_size}" \
            "$vtoy_boot_sdi_efi" \
            "$vtoy_boot_efi_path" \
            "vf=boot.wim:$vtoy_wimfile_path" \
            pfsize=$vtoy_chain_file_size  \
            pfread=$vtoy_chain_file_read
        boot
        ventoy_gui_console
    fi
}

function vtoy_wimboot_func {
    if [ "$grub_platform" = "pc" ]; then
        set vt_wimkernel=wimboot.x86_64.xz        
    else
        if [ "$grub_cpu" = "i386" ]; then
            set vt_wimkernel=wimboot.i386.efi.xz
        else
            set vt_wimkernel=wimboot.x86_64.xz
        fi
    fi

    if vt_is_standard_winiso (loop); then
        echo -e "\n==================== VENTOY WIMBOOT ==================\n"
        vtoy_windows_wimboot
    else
        vt_sel_winpe_wim (loop)
        if [ -n "$vtoy_pe_wim_path" ]; then            
            echo -e "\n==================== VENTOY WIMBOOT ==================\n"
            
            vt_fs_ignore_case 1
            vt_load_file_to_mem "auto" $vtoy_path/common_bcd.xz vtoy_pe_bcd_mem
            
            set vt_sdi_path=0
            for vsdi in "boot/boot.sdi" "2K10/FONTS/boot.sdi" "SSTR/boot.sdi" "ISPE/BOOT.SDI" \
            "boot/uqi.sdi" "ISYL/boot.sdi" "WEPE/WEPE.SDI" ; do
                if [ -F "(loop)/$vsdi" ]; then
                    set vt_sdi_path=$vsdi
                    break
                fi
            done
            
            if [ "$grub_platform" = "pc" ]; then
                vt_load_file_to_mem "auto" $vtoy_path/common_bootmgr.xz vtoy_pe_bootmgr_mem
                vtoy_winpe_wimboot "$vtoy_pe_wim_path" "$vt_sdi_path" 1
            else
                vtoy_winpe_wimboot "$vtoy_pe_wim_path" "$vt_sdi_path" 0
            fi
            
            vt_fs_ignore_case 0
        fi
    fi
}

function legacy_windows_menu_func {
    vt_windows_reset
    
    unset vt_cur_wimboot_mode
    if vt_check_mode 4 "$vt_chosen_name"; then
        set vt_cur_wimboot_mode=1
    fi
    
    if [ "$ventoy_compatible" = "NO" -o "$vt_cur_wimboot_mode" = "1" ]; then
        if [ "$ventoy_fs_probe" = "iso9660" ]; then
            loopback -d loop
            vt_iso9660_nojoliet 1
            loopback loop "$1$2"
        fi
        
        for file in "boot/bcd" "/efi/microsoft/boot/bcd" "SSTR/BCD" "boot/bce"; do
            vt_windows_collect_wim_patch bcd (loop)/$file                
        done
        
        distro_specify_wim_patch

        vt_windows_count_wim_patch vt_wim_cnt
        if [ $vt_wim_cnt -eq 0 ]; then
            distro_specify_wim_patch_phase2
        fi
        
        ventoy_debug_pause
        if [ -z "$vt_cur_wimboot_mode" ]; then
            locate_wim "${chosen_path}"
        fi
    fi
    
    vt_windows_chain_data "${1}${chosen_path}"
    ventoy_debug_pause
    
    if [ "$vt_cur_wimboot_mode" = "1" ]; then
        vtoy_wimboot_func
    fi

    if [ -n "$vtoy_chain_mem_addr" ]; then
        ventoy_acpi_param ${vtoy_chain_mem_addr} 2048
        if [ "$ventoy_compatible" = "NO" ]; then
            linux16   $vtoy_path/ipxe.krn ${vtdebug_flag} mem:${vtoy_chain_mem_addr}:size:${vtoy_chain_mem_size}
        else
            linux16   $vtoy_path/ipxe.krn ${vtdebug_flag} ibft mem:${vtoy_chain_mem_addr}:size:${vtoy_chain_mem_size}
        fi
        boot
    else
        echo "chain empty failed"
        ventoy_pause
    fi
}

function legacy_linux_menu_func {
    if [ "$ventoy_compatible" = "NO" ]; then

        if [ "$ventoy_fs_probe" = "udf" ]; then
            loopback -d loop
            set ventoy_fs_probe=iso9660
            loopback loop "$1$2"
        fi

        
        if vt_syslinux_need_nojoliet "$1$2"; then
            vt_iso9660_nojoliet 1
            loopback -d loop
            loopback loop "$1$2"
        fi

        vt_load_cpio  $vtoy_path  "$2" "$1" "busybox=$ventoy_busybox_ver"

        vt_linux_clear_initrd
        
        if [ -d (loop)/pmagic ]; then
            vt_linux_specify_initrd_file /pmagic/initrd.img
        else
            for dir in "isolinux" "boot/isolinux" "boot/x86_64/loader" "syslinux" "boot/syslinux"; do
                if [ -d (loop)/$dir ]; then
                    vt_linux_parse_initrd_isolinux   (loop)/$dir/
                fi
            done
        fi
        
        # special process for special distros
        #archlinux
        if [ -d (loop)/arch/boot/syslinux ]; then
            vt_linux_parse_initrd_isolinux   (loop)/arch/boot/syslinux/  /arch/
            vt_linux_parse_initrd_isolinux   (loop)/arch/boot/syslinux/  /arch/boot/syslinux/
        elif [ -d (loop)/anarchy/boot/syslinux ]; then
            vt_linux_parse_initrd_isolinux   (loop)/anarchy/boot/syslinux/  /anarchy/
            
        #manjaro
        elif [ -d (loop)/manjaro ]; then
            if [ -e (loop)/boot/grub/kernels.cfg ]; then
                vt_linux_parse_initrd_grub  file  (loop)/boot/grub/kernels.cfg
            fi
        elif [ -e (loop)/boot/grub/grub.cfg ]; then                
            vt_linux_parse_initrd_grub  file  (loop)/boot/grub/grub.cfg
        fi
        
        distro_specify_initrd_file
        
        vt_linux_initrd_count vtcount
        if [ $vtcount -eq 0 ]; then
            if [ -d (loop)/rancheros ]; then
                vt_linux_parse_initrd_isolinux   (loop)/boot/  /boot/isolinux/
            fi

            distro_specify_initrd_file_phase2
        fi
        
        locate_initrd
    fi
    
    vt_linux_chain_data "${1}${chosen_path}"
    ventoy_debug_pause
    
    if [ -n "$vtoy_chain_mem_addr" ]; then
        unset vtGrub2Mode
        if vt_check_mode 3 "$vt_chosen_name"; then
            set vtGrub2Mode=1
        elif vt_str_begin "$vt_volume_id" "HOLO_"; then
            if [ -d (loop)/loader/entries ]; then
                set vtGrub2Mode=1
            fi        
        fi
    
        if [ -n "$vtGrub2Mode" ]; then
            ventoy_acpi_param ${vtoy_chain_mem_addr} 2048
            ventoy_cli_console

            # fallback
            set vtback_root=$root        
            vt_push_last_entry
            set vtback_theme=$theme
            unset theme
            
            vt_trailer_cpio "$vtoy_iso_part" "$vt_chosen_path" noinit
            vt_set_boot_opt rdinit=/vtoy/vtoy

            set root=(loop)
            set vtback_cfg_find=0
            for cfg in "/boot/grub/grub.cfg" "/EFI/BOOT/grub.cfg" "/EFI/debian/grub.cfg" "EFI/boot/grub.cfg" "efi/boot/grub.cfg" "/grub/grub.cfg" "EFI/BOOT/BOOTX64.conf"; do
                if [ -e "$cfg" ]; then
                    set vtback_cfg_find=1
                    configfile "$cfg"
                    break
                fi
            done
            if [ $vtback_cfg_find -eq 0 ]; then
                if [ -f (loop)/loader/loader.conf -a -d (loop)/loader/entries ]; then                    
                    if vt_str_begin "$vt_volume_id" "HOLO_"; then
                        set root=(loop,2)
                        vt_systemd_menu (loop,2) vt_sys_menu_mem
                    else
                        vt_systemd_menu (loop) vt_sys_menu_mem
                    fi
                    set vtback_cfg_find=1  
                    configfile "mem:${vt_sys_menu_mem_addr}:size:${vt_sys_menu_mem_size}"
                fi
            fi
            
            if [ $vtback_cfg_find -eq 0 ]; then
                if [ -f (loop)/boot/isolinux/syslnx64.cfg ]; then
                    syslinux_configfile (loop)/boot/isolinux/syslnx64.cfg
                    set vtback_cfg_find=1
                elif [ -f (loop)/boot/syslinux/porteus.cfg ]; then
                    syslinux_configfile (loop)/boot/syslinux/porteus.cfg
                    set vtback_cfg_find=1
                fi
            fi

            vt_unset_boot_opt
            set root=$vtback_root        
            set theme=$vtback_theme
            vt_pop_last_entry
            ventoy_gui_console
        else
            ventoy_acpi_param ${vtoy_chain_mem_addr} 2048
            linux16   $vtoy_path/ipxe.krn ${vtdebug_flag}  mem:${vtoy_chain_mem_addr}:size:${vtoy_chain_mem_size}            
            boot
        fi
    else
        echo "chain empty failed"
        ventoy_pause
    fi
}


function legacy_unix_menu_func {    
    ventoy_unix_comm_proc $1 "${chosen_path}"
    
    if [ -n "$vtoy_chain_mem_addr" ]; then
        #ventoy_acpi_param ${vtoy_chain_mem_addr} 2048
        linux16   $vtoy_path/ipxe.krn ${vtdebug_flag}  mem:${vtoy_chain_mem_addr}:size:${vtoy_chain_mem_size}
        boot
    else
        echo "chain empty failed"
        ventoy_pause
    fi
}


function legacy_iso_menu_func {
    set chosen_path="$2"
    
    vt_select_auto_install "${chosen_path}"
    vt_select_persistence "${chosen_path}"
    
    if [ -n "$vtcompat" ]; then
        set ventoy_compatible=YES
        unset vtcompat
    elif vt_check_mode 1 "$vt_chosen_name"; then
        set ventoy_compatible=YES
    else
        vt_check_compatible (loop)
    fi
    
    vt_img_sector "${1}${chosen_path}"

    if [ "$ventoy_fs_probe" = "iso9660" ]; then
        vt_select_conf_replace "${1}" "${chosen_path}"
    fi

    if [ "$vtoy_os" = "Windows" ]; then
        vt_check_compatible_pe (loop)        
        legacy_windows_menu_func  "$1" "${chosen_path}"
    elif [ "$vtoy_os" = "Unix" ]; then
        legacy_unix_menu_func "$1" "${chosen_path}"
    else
        vt_check_compatible_linux (loop)
        legacy_linux_menu_func  "$1" "${chosen_path}"
    fi
    vt_secondary_recover_mode
}

function legacy_iso_memdisk {

    linux16   $vtoy_path/memdisk iso raw    
    echo "Loading ISO file to memory ..."
    initrd16  "${1}${2}"
    boot
}


function iso_endless_os_proc {
    if [ -d (loop)/ ]; then
        loopback -d loop
    fi

    loopback loop "${1}${2}"
    vt_img_sector "${1}${2}"

    vt_load_cpio  $vtoy_path  "$2" "$1"  "busybox=$ventoy_busybox_ver"
    vt_trailer_cpio "$1" "$2" noinit
    
    ventoy_debug_pause

    vt_set_boot_opt '@kparams' rdinit=/vtoy/vtoy 

    set eosimage=loop
    set ventoy_bls_bootdev=/boot    
    set ventoy_loading_tip="Loading files ......"
    
    export eosimage    
    configfile (loop)/endless/grub/grub.cfg
    
    unset eosimage
    unset ventoy_bls_bootdev
    unset ventoy_loading_tip
    
    vt_unset_boot_opt
}


function ventoy_iso_busybox_ver {

    if [ "$VTOY_EFI_ARCH" = "aa64" ]; then
        set ventoy_busybox_ver=a64
    elif [ "$VTOY_EFI_ARCH" = "mips" ]; then
        set ventoy_busybox_ver=m64
    else
        set ventoy_busybox_ver=32
    
        #special process for deepin-live iso
        if [ "$vt_chosen_size" = "403701760" ]; then
            if vt_str_str "$vt_chosen_path" "/deepin-live"; then
                set ventoy_busybox_ver=64
            fi
        elif vt_str_begin "$vt_volume_id" "PHOTON_"; then
            set ventoy_busybox_ver=64
        elif vt_str_begin "$vt_volume_id" "smgl-test-quinq-x86_64"; then
            set ventoy_busybox_ver=64
        elif vt_str_begin "$vt_volume_id" "LDiagBootable"; then
            set ventoy_busybox_ver=64
        elif vt_str_begin "$vt_volume_id" "KAOS_"; then
            set ventoy_busybox_ver=64

        fi
    fi
}


function iso_common_menuentry {
    unset vt_system_id
    unset vt_volume_id
    
    vt_chosen_img_path vt_chosen_path vt_chosen_size vt_chosen_name

    vt_parse_iso_volume "${vtoy_iso_part}${vt_chosen_path}" vt_system_id vt_volume_id vt_volume_space
    if [ $vt_volume_space -NE $vt_chosen_size ]; then        
        vt_mod $vt_chosen_size 2048 vt_chosen_size_mod
        if [ $vt_chosen_size_mod -ne 0 ]; then
            echo -e "\n $vt_volume_space $vt_chosen_size $vt_chosen_size_mod\n"
            echo -e "\n The size of the iso file \"$vt_chosen_size\" is invalid. File corrupted ?\n"
            echo -e " 此ISO文件的大小 \"$vt_chosen_size\" 有问题，请确认文件是否损坏。\n"
            echo -en "\n$VTLANG_ENTER_CONTINUE ..."
            read vtInputKey
        fi
    fi
    
    if vt_check_password "${vt_chosen_path}"; then
        return
    fi

    if ventoy_vcfg_proc "${vt_chosen_path}"; then        
        return
    fi
    
    
    
    #secondary boot menu
    if vt_is_udf "${vtoy_iso_part}${vt_chosen_path}"; then
        set ventoy_fs_probe=udf
    else
        set ventoy_fs_probe=iso9660
        ventoy_reset_nojoliet
    fi
    
    if [ -d (loop)/ ]; then
        loopback -d loop
    fi
    loopback loop "${vtoy_iso_part}${vt_chosen_path}" 
    
    get_os_type (loop)
    
    ventoy_debug_pause
    
    if vt_need_secondary_menu "$vt_chosen_name"; then
        vt_show_secondary_menu "$vt_chosen_path" "$vtoy_os" $vt_chosen_size
        if [ "$VTOY_SECOND_EXIT" = "1" ]; then
            return
        fi
    fi


    if vt_str_begin "$vt_volume_id" "Avira"; then 
        vt_skip_svd "${vtoy_iso_part}${vt_chosen_path}"
    fi
    
    ventoy_iso_busybox_ver
    
    #special process for Endless OS
    if vt_str_begin "$vt_volume_id" "Endless-OS"; then
        iso_endless_os_proc $vtoy_iso_part "$vt_chosen_path"
    elif vt_str_begin "$vt_volume_id" "TENS-Public"; then
        set vtcompat=1
    fi


    # auto memdisk mode for some special ISO files
    vt_iso_vd_id_parse "${vtoy_iso_part}${vt_chosen_path}"    
    unset vtMemDiskBoot
    if vt_check_mode 0 "$vt_chosen_name"; then
        set vtMemDiskBoot=1
    else
        if [ "$grub_platform" = "pc" ]; then
            if vt_iso_vd_id_begin 1 0 "Memtest86+"; then
                set vtMemDiskBoot=1
            elif vt_iso_vd_id_begin 0 1 "KolibriOS"; then
                set vtMemDiskBoot=1
            fi
        fi
        
        #For iKuai8 (<64MB)
        if [ $vt_chosen_size -LE 67108864 ]; then
            if vt_str_begin "$vt_chosen_name" "iKuai"; then
                set vtMemDiskBoot=1
            fi
        fi
    fi
    vt_iso_vd_id_clear


    if [ "$grub_platform" = "pc" ]; then
        if [ -n "$vtMemDiskBoot" ]; then
            legacy_iso_memdisk $vtoy_iso_part "$vt_chosen_path"
        else
            legacy_iso_menu_func $vtoy_iso_part "$vt_chosen_path"
        fi
    else
        if [ -n "$vtMemDiskBoot" ]; then
            uefi_iso_memdisk $vtoy_iso_part  "$vt_chosen_path"
        else
            uefi_iso_menu_func $vtoy_iso_part  "$vt_chosen_path"
        fi
    fi

    vt_secondary_recover_mode
}

function miso_common_menuentry {
    vt_chosen_img_path vt_chosen_path vt_chosen_size vt_chosen_name

    if vt_check_password "${vt_chosen_path}"; then
        return
    fi

    echo "memdisk mode boot for $vt_chosen_path"
    echo ""
    ventoy_debug_pause

    if [ "$grub_platform" = "pc" ]; then
        legacy_iso_memdisk $vtoy_iso_part "$vt_chosen_path"
    else
        uefi_iso_memdisk $vtoy_iso_part  "$vt_chosen_path"
    fi      
}


function common_unsupport_menuentry {
    echo -e "\n The name of the iso file could NOT contain space or non-ascii characters. \n"
    echo -e " 文件名中不能有中文或空格 \n"    
    echo -en "\n$VTLANG_ENTER_EXIT ..."  
    read vtInputKey
}

function miso_unsupport_menuentry {
    common_unsupport_menuentry
}

function iso_unsupport_menuentry {
    common_unsupport_menuentry
}

function wim_common_menuentry {
    vt_chosen_img_path vt_chosen_path vt_chosen_size vt_chosen_name
    
    if vt_check_password "${vt_chosen_path}"; then
        return
    fi
    
    if ventoy_vcfg_proc "${vt_chosen_path}"; then        
        return
    fi
    
    if vt_wim_check_bootable "${vtoy_iso_part}${vt_chosen_path}"; then
        vt_wim_chain_data "${vtoy_iso_part}${vt_chosen_path}"
    else
        echo -e "\n This is NOT a bootable WIM file. \n"
        echo -e " 这不是一个可启动的 WIM 文件。\n"
    fi
    
    ventoy_debug_pause    
    
    if [ -n "$vtoy_chain_mem_addr" ]; then
        if [ "$grub_platform" = "pc" ]; then
            linux16   $vtoy_path/ipxe.krn ${vtdebug_flag}  mem:${vtoy_chain_mem_addr}:size:${vtoy_chain_mem_size}            
        else
            ventoy_cli_console
            chainloader ${vtoy_path}/ventoy_${VTOY_EFI_ARCH}.efi  env_param=${env_param} isoefi=${LoadIsoEfiDriver} ${vtdebug_flag} mem:${vtoy_chain_mem_addr}:size:${vtoy_chain_mem_size}
            ventoy_gui_console
        fi
        boot
    else
        echo "chain empty failed"
        ventoy_pause
    fi
}

function wim_unsupport_menuentry {
    common_unsupport_menuentry
}

function efi_common_menuentry {
    vt_chosen_img_path vt_chosen_path vt_chosen_size vt_chosen_name
    
    if vt_check_password "${vt_chosen_path}"; then
        return
    fi
    
    if ventoy_vcfg_proc "${vt_chosen_path}"; then        
        return
    fi
    
    unset vt_vlnk_dst
    if vt_is_vlnk_name "${vt_chosen_path}"; then
        vt_get_vlnk_dst "${vtoy_iso_part}${vt_chosen_path}" vt_vlnk_dst
        if [ -z "$vt_vlnk_dst" ]; then
            echo -e "\n### VLNK FILE NOT FOUND ###\n### VLNK 文件不存在 ###\n"            
            ventoy_pause
            return
        fi
    else
        vt_vlnk_dst="${vtoy_iso_part}${vt_chosen_path}"
    fi
    
    ventoy_debug_pause

    ventoy_cli_console
    
    #first try with chainload
    set vtOldRoot=$root
    set root=$vtoy_iso_part
    chainloader "${vt_vlnk_dst}"
    boot

    #retry with isoboot
    set root=$vtOldRoot
    vt_concat_efi_iso "${vt_vlnk_dst}" vtoy_iso_buf    
    chainloader ${vtoy_path}/ventoy_${VTOY_EFI_ARCH}.efi memdisk env_param=${env_param} dotefi isoefi=on ${vtdebug_flag} mem:${vtoy_iso_buf_addr}:size:${vtoy_iso_buf_size}
    boot    

    ventoy_gui_console
}

function efi_unsupport_menuentry {
    common_unsupport_menuentry
}

function vhdboot_common_func {
    vt_patch_vhdboot "$1"
    
    ventoy_debug_pause    
    
    if [ -n "$vtoy_vhd_buf_addr" ]; then
        if [ "$grub_platform" = "pc" ]; then
            ventoy_cli_console
            linux16   $vtoy_path/memdisk iso raw    
            initrd16  mem:${vtoy_vhd_buf_addr}:size:${vtoy_vhd_buf_size}            
            boot
            ventoy_gui_console
        else
            ventoy_cli_console
            chainloader ${vtoy_path}/ventoy_${VTOY_EFI_ARCH}.efi memdisk env_param=${env_param} isoefi=${LoadIsoEfiDriver} ${vtdebug_flag} mem:${vtoy_vhd_buf_addr}:size:${vtoy_vhd_buf_size}
            boot
            ventoy_gui_console
        fi
    else
        echo "Please put the right ventoy_vhdboot.img file to the 1st partition"
        ventoy_pause
    fi
}

function vhd_common_menuentry {
    vt_chosen_img_path vt_chosen_path vt_chosen_size vt_chosen_name
    
    if vt_check_password "${vt_chosen_path}"; then
        return
    fi
    
    if ventoy_vcfg_proc "${vt_chosen_path}"; then        
        return
    fi
    
    unset vt_vlnk_dst
    if vt_is_vlnk_name "${vt_chosen_path}"; then
        vt_get_vlnk_dst "${vtoy_iso_part}${vt_chosen_path}" vt_vlnk_dst
        if [ -z "$vt_vlnk_dst" ]; then
            echo -e "\n### VLNK FILE NOT FOUND ###\n### VLNK 文件不存在 ###\n"            
            ventoy_pause
            return
        fi
    else
        vt_vlnk_dst="${vt_chosen_path}"
        if [ "$VTOY_VHD_NO_WARNING" != "1" ]; then
            if [ "$vtoy_iso_fs" != "ntfs" ]; then
                echo -e "!!! WARNING !!!\n"
                echo -e "\nPartition1 ($vtoy_iso_fs) is NOT ntfs, the VHD(x) file may not boot normally \n"
                echo -e "\nVHD(x) 文件所在分区不是 ntfs 格式, 可能无法正常启动 \n\n"
                echo -en "\n$VTLANG_ENTER_CONTINUE ..."
                read vtInputKey
            fi
        fi
    fi
    
    vhdboot_common_func "${vt_vlnk_dst}"
}

function vhd_unsupport_menuentry {
    common_unsupport_menuentry
}

function vtoyboot_common_func {
    set AltBootPart=0
    set vtoysupport=0
    
    vt_get_vtoy_type "${1}" vtoytype parttype AltBootPart
    
    if vt_str_begin $vtoytype vhd; then    
        set vtoysupport=1
    elif [ "$vtoytype" = "raw" ]; then
        set vtoysupport=1
    elif [ "$vtoytype" = "vdi" ]; then
        set vtoysupport=1
    fi
    
    if [ $vtoysupport -eq 1 ]; then    
        if [ "$grub_platform" = "pc" ]; then
            if [ "$parttype" = "gpt" -a $AltBootPart -eq 0 ]; then
                echo "The OS in the vdisk was created in UEFI mode, but current is Legacy BIOS mode."
                echo "虚拟磁盘内的系统是在UEFI模式下创建的，而当前系统是Legacy BIOS模式，可能无法正常启动。"
                ventoy_pause
            fi
        else
            if [ "$parttype" = "mbr" -a $AltBootPart -eq 0 ]; then
                echo "The OS in the vdisk was created in Legacy BIOS mode, but current is UEFI mode."
                echo "虚拟磁盘内的系统是在Legacy BIOS模式下创建的，而当前系统是UEFI模式，可能无法正常启动。"
                ventoy_pause
            fi
        fi

        vt_img_sector "${1}"
        vt_raw_chain_data "${1}"

        ventoy_debug_pause
        
        if [ -n "$vtoy_chain_mem_addr" ]; then  
            if [ "$grub_platform" = "pc" ]; then
                vt_acpi_param ${vtoy_chain_mem_addr} 512
                linux16 $vtoy_path/ipxe.krn ${vtdebug_flag} bios80  sector512  mem:${vtoy_chain_mem_addr}:size:${vtoy_chain_mem_size}   
                boot
            else
                if vt_check_secureboot_var; then
                    vt_acpi_param ${vtoy_chain_mem_addr} 512
                fi
                ventoy_cli_console
                chainloader ${vtoy_path}/ventoy_${VTOY_EFI_ARCH}.efi sector512 env_param=${ventoy_env_param} ${vtdebug_flag} mem:${vtoy_chain_mem_addr}:size:${vtoy_chain_mem_size}
                boot
                ventoy_gui_console
            fi  
        else
            echo "chain empty failed!"
            ventoy_pause
        fi
    else
        echo "Unsupported vtoy type $vtoytype"
        ventoy_pause
    fi
}

function vtoy_common_menuentry {    
    vt_chosen_img_path vt_chosen_path vt_chosen_size vt_chosen_name
    
    if vt_check_password "${vt_chosen_path}"; then
        return
    fi    
    
    if ventoy_vcfg_proc "${vt_chosen_path}"; then        
        return
    fi
    
    unset vt_vlnk_dst
    if vt_is_vlnk_name "${vt_chosen_path}"; then
        vt_get_vlnk_dst "${vtoy_iso_part}${vt_chosen_path}" vt_vlnk_dst
        if [ -z "$vt_vlnk_dst" ]; then
            echo -e "\n### VLNK FILE NOT FOUND ###\n### VLNK 文件不存在 ###\n"            
            ventoy_pause
            return
        fi
    else
        vt_vlnk_dst="${vtoy_iso_part}${vt_chosen_path}"
    fi
    
    vtoyboot_common_func "${vt_vlnk_dst}"
}

function vtoy_unsupport_menuentry {
    common_unsupport_menuentry
}

#
#============================================================#
# IMG file boot process                                      #
#============================================================#
#

function only_uefi_tip {
    echo -e "\n This IMG file is only supported in UEFI mode. \n"
    echo -en "\n$VTLANG_ENTER_EXIT ..."
    read vtInputKey 
}

function ventoy_img_easyos {
    vt_load_cpio  $vtoy_path  "${vt_chosen_path}" ${vtoy_iso_part} "busybox=$ventoy_busybox_ver"
    vt_trailer_cpio ${vtoy_iso_part} "${vt_chosen_path}" noinit
    
    loopback easysfs (vtimghd,1)/easy.sfs
    vt_get_lib_module_ver (easysfs) /lib/modules/ vt_module_ver
    
    if [ -n "$vt_module_ver" ]; then        
        for mod in "kernel/drivers/md/dm-mod.ko" "kernel/drivers/dax/dax.ko"; do
            if [ -e (easysfs)/lib/modules/$vt_module_ver/$mod ]; then
                vt_img_extra_initrd_append  (easysfs)/lib/modules/$vt_module_ver/$mod
            fi
        done
    fi

    ventoy_debug_pause

    #boot image file
    vt_set_boot_opt rdinit=/vtoy/vtoy
    vt_img_hook_root
    
    syslinux_configfile (vtimghd,1)/syslinux.cfg
    
    vt_img_unhook_root
    vt_unset_boot_opt
    loopback -d easysfs
}

function ventoy_img_easyos2 {
    vt_load_cpio  $vtoy_path  "${vt_chosen_path}" ${vtoy_iso_part} "busybox=$ventoy_busybox_ver"
    vt_trailer_cpio ${vtoy_iso_part} "${vt_chosen_path}" noinit
    
    if [ -e (vtimghd,2)/easyos/easy.sfs ]; then
        loopback easysfs (vtimghd,2)/easyos/easy.sfs
    elif [ -d (vtimghd,2)/easyos/releases ]; then
        vt_fs_enum_1st_dir (vtimghd,2) /easyos/releases/ vt_dir_name
        loopback easysfs (vtimghd,2)/easyos/releases/$vt_dir_name/easy.sfs
    fi
    
    vt_get_lib_module_ver (easysfs) /lib/modules/ vt_module_ver
    
    if [ -n "$vt_module_ver" ]; then        
        for mod in "kernel/drivers/md/dm-mod.ko" "kernel/drivers/dax/dax.ko"; do
            if [ -e (easysfs)/lib/modules/$vt_module_ver/$mod ]; then
                vt_img_extra_initrd_append  (easysfs)/lib/modules/$vt_module_ver/$mod
            fi
        done
    fi

    ventoy_debug_pause

    #boot image file
    vt_set_boot_opt rdinit=/vtoy/vtoy
    vt_img_hook_root
    
    vt_limine_menu (vtimghd,1)/limine.cfg vt_sys_menu_mem
    configfile "mem:${vt_sys_menu_mem_addr}:size:${vt_sys_menu_mem_size}"
    
    vt_img_unhook_root
    vt_unset_boot_opt
    loopback -d easysfs
}

function ventoy_img_volumio {
    vt_load_cpio  $vtoy_path  "${vt_chosen_path}" ${vtoy_iso_part} "busybox=$ventoy_busybox_ver"
    vt_trailer_cpio ${vtoy_iso_part} "${vt_chosen_path}" noinit

    ventoy_debug_pause

    #boot image file
    vt_set_boot_opt rdinit=/vtoy/vtoy imgpart=/dev/ventoy2 bootpart=/dev/ventoy1
    vt_img_hook_root
    
    syslinux_configfile (vtimghd,1)/syslinux.cfg
    
    vt_img_unhook_root
    vt_unset_boot_opt
}

function ventoy_img_openelec {
    elec_ver=$1
    
    vt_load_cpio  $vtoy_path  "${vt_chosen_path}" ${vtoy_iso_part} "busybox=$ventoy_busybox_ver"
    vt_trailer_cpio ${vtoy_iso_part} "${vt_chosen_path}" noinit

    loopback vtloopex $vtoy_efi_part/ventoy/vtloopex.cpio    
    vt_img_extra_initrd_append  (vtloopex)/$elec_ver/vtloopex.tar.xz

    if [ "$elec_ver" = "LibreELEC" ]; then
        if [ -f (vtimghd,1)/system ]; then
            loopback elecsfs (vtimghd,1)/system
            vt_get_lib_module_ver (elecsfs) /usr/lib/kernel-overlays/base/lib/modules/  vt_module_ver
            if [ -n "$vt_module_ver" ]; then        
                for mod in "kernel/drivers/md/dm-mod.ko"; do
                    if [ -e (elecsfs)/usr/lib/kernel-overlays/base/lib/modules/$vt_module_ver/$mod ]; then
                        vt_img_extra_initrd_append  (elecsfs)/usr/lib/kernel-overlays/base/lib/modules/$vt_module_ver/$mod
                    fi
                done
            fi
        fi
    fi

    ventoy_debug_pause

    #boot image file
    vt_set_boot_opt rdinit=/vtoy/vtoy ventoyos=$elec_ver
    vt_img_hook_root
    
    set root=(vtimghd,1)
    syslinux_configfile (vtimghd,1)/syslinux.cfg
    
    vt_img_unhook_root
    vt_unset_boot_opt
    loopback -d vtloopex
    loopback -d elecsfs
}


function ventoy_img_freedombox {
    vt_load_cpio  $vtoy_path  "${vt_chosen_path}" ${vtoy_iso_part} "busybox=$ventoy_busybox_ver"
    vt_trailer_cpio ${vtoy_iso_part} "${vt_chosen_path}" noinit

    vt_get_lib_module_ver (vtimghd,1) /lib/modules/ vt_module_ver
    if [ -n "$vt_module_ver" ]; then        
        vt_img_extra_initrd_append  (vtimghd,1)/lib/modules/$vt_module_ver/kernel/drivers/md/dm-mod.ko
    fi

    ventoy_debug_pause

    #boot image file
    vt_set_boot_opt rdinit=/vtoy/vtoy ventoyos=freedombox
    vt_img_hook_root
    
    configfile (vtimghd,1)/boot/grub/grub.cfg
    
    vt_img_unhook_root
    vt_unset_boot_opt
}

function ventoy_img_paldo {
    vt_load_cpio  $vtoy_path  "${vt_chosen_path}" ${vtoy_iso_part} "busybox=$ventoy_busybox_ver"
    vt_trailer_cpio ${vtoy_iso_part} "${vt_chosen_path}" noinit

    ventoy_debug_pause

    #boot image file
    vt_set_boot_opt rdinit=/vtoy/vtoy ventoyos=paldo
    vt_img_hook_root
    
    vt_fs_enum_1st_file (vtimghd,1) /loader/entries/ vt_paldo_entry_conf
    vt_file_basename $vt_paldo_entry_conf vtPaldoVer
    
    echo loading file...
    linux (vtimghd,1)/linux-${vtPaldoVer} root=/dev/ventoy1 rootfstype=vfat
    initrd (vtimghd,1)/initramfs-${vtPaldoVer}
    boot
    
    vt_img_unhook_root
    vt_unset_boot_opt
}

function ventoy_img_ubos {
    vt_load_cpio  $vtoy_path  "${vt_chosen_path}" ${vtoy_iso_part} "busybox=$ventoy_busybox_ver"
    vt_trailer_cpio ${vtoy_iso_part} "${vt_chosen_path}" noinit

    vt_get_lib_module_ver (vtimghd,3) /lib/modules/ vt_module_ver
    if [ -n "$vt_module_ver" ]; then        
        vt_img_extra_initrd_append  (vtimghd,3)/lib/modules/$vt_module_ver/kernel/drivers/md/dm-mod.ko.xz
    fi

    ventoy_debug_pause

    #boot image file
    vt_set_boot_opt rdinit=/vtoy/vtoy ventoyos=ubos
    vt_img_hook_root
    
    echo loading file...
    linux (vtimghd,2)/vmlinuz-linux root=/dev/ventoy3 rw
    initrd (vtimghd,2)/initramfs-linux.img
    boot

    vt_img_unhook_root
    vt_unset_boot_opt
}

function ventoy_img_recalbox {
    if [ $vtoy_img_max_part_end -GT $vt_chosen_size ]; then
        echo -e "\nPlease extend the img file size before boot it. \n"
        ventoy_pause
        return
    fi

    vt_load_cpio  $vtoy_path  "${vt_chosen_path}" ${vtoy_iso_part} "busybox=$ventoy_busybox_ver"
    vt_trailer_cpio ${vtoy_iso_part} "${vt_chosen_path}" noinit

    if [ -e (vtimghd,1)/boot/recalbox ]; then
        loopback recalbox (vtimghd,1)/boot/recalbox
        vt_get_lib_module_ver (recalbox) /lib/modules/ vt_module_ver
        if [ -n "$vt_module_ver" ]; then        
            vt_img_extra_initrd_append  (recalbox)/lib/modules/$vt_module_ver/kernel/drivers/md/dm-mod.ko
        fi
    fi

    ventoy_debug_pause

    #boot image file
    vt_set_boot_opt rdinit=/vtoy/vtoy ventoyos=recalbox
    vt_img_hook_root
    
    set root=(vtimghd,1)
    configfile (vtimghd,1)/boot/grub/grub.cfg

    vt_img_unhook_root
    vt_unset_boot_opt
}

function ventoy_img_esysrescue {
    vt_load_cpio  $vtoy_path  "${vt_chosen_path}" ${vtoy_iso_part} "busybox=$ventoy_busybox_ver"
    vt_trailer_cpio ${vtoy_iso_part} "${vt_chosen_path}" noinit

    ventoy_debug_pause

    #boot image file
    vt_set_boot_opt rdinit=/vtoy/vtoy ventoyos=esysrescue
    vt_img_hook_root

    set root=(vtimghd,1)
    configfile (vtimghd,1)/boot/grub/grub.cfg

    vt_img_unhook_root
    vt_unset_boot_opt
}

function ventoy_img_batocera {
    vt_load_cpio  $vtoy_path  "${vt_chosen_path}" ${vtoy_iso_part} "busybox=$ventoy_busybox_ver"
    vt_trailer_cpio ${vtoy_iso_part} "${vt_chosen_path}" noinit

    ventoy_debug_pause

    #boot image file
    vt_set_boot_opt rdinit=/vtoy/vtoy ventoyos=batocera
    vt_img_hook_root
    
    set root=(vtimghd,1)
    syslinux_configfile (vtimghd,1)/boot/syslinux/syslinux.cfg

    vt_img_unhook_root
    vt_unset_boot_opt
}

function ventoy_img_openwrt {
    if [ -e (vtimghd,2)/lib64 ]; then
        set ventoy_busybox_ver=64
    fi

    vt_fs_enum_1st_dir (vtimghd,2) /lib/modules/ vt_dir_name

    if [ -f (vtimghd,2)/lib/modules/$vt_dir_name/dm-mod.ko ]; then
        set openwrt_plugin_need=0
        vt_img_extra_initrd_append  (vtimghd,2)/lib/modules/$vt_dir_name/dm-mod.ko
        if [ -f (vtimghd,2)/lib/modules/$vt_dir_name/dax.ko ]; then
            vt_img_extra_initrd_append  (vtimghd,2)/lib/modules/$vt_dir_name/dax.ko
        fi
    else
        set openwrt_plugin_need=1
        if [ ! -f ${vtoy_iso_part}/ventoy/ventoy_openwrt.xz ]; then
            ventoy_gui_console
            echo -e "\n ventoy_openwrt.xz not found. Please refer https://www.ventoy.net/en/doc_openwrt.html.\n"
            echo -e " 未找到 ventoy_openwrt.xz 文件。请参考 https://www.ventoy.net/cn/doc_openwrt.html\n"
            echo -en "\n$VTLANG_ENTER_EXIT ..."
            read vtInputKey
            ventoy_cli_console
            return
        fi
    fi

    if vt_img_check_range "${vtoy_iso_part}${vt_chosen_path}"; then
        ventoy_debug_pause
    else
        ventoy_gui_console
        echo -e "\n IMG file need processed. Please refer https://www.ventoy.net/en/doc_openwrt.html.\n"
        echo -e " 此 IMG 文件必须处理之后才能支持。请参考 https://www.ventoy.net/cn/doc_openwrt.html\n"
        echo -e "\n press ENTER to exit (请按 回车 键返回) ..."
        read vtInputKey
        ventoy_cli_console
        return
    fi
    
    vt_load_cpio  $vtoy_path  "${vt_chosen_path}" ${vtoy_iso_part} "busybox=$ventoy_busybox_ver"
    vt_trailer_cpio ${vtoy_iso_part} "${vt_chosen_path}" noinit

    if [ $openwrt_plugin_need -eq 1 ]; then
        if [ -f ${vtoy_iso_part}/ventoy/ventoy_openwrt.xz ]; then
            vt_img_extra_initrd_append  ${vtoy_iso_part}/ventoy/ventoy_openwrt.xz
        fi
    fi

    #boot image file
    vt_set_boot_opt rdinit=/vtoy/vtoy ventoyos=openwrt
    vt_img_hook_root
    
    set root=(vtimghd,1)
    configfile (vtimghd,1)/boot/grub/grub.cfg

    vt_img_unhook_root
    vt_unset_boot_opt
}

function ventoy_img_tails {
    vt_load_cpio  $vtoy_path  "${vt_chosen_path}" ${vtoy_iso_part} "busybox=$ventoy_busybox_ver"
    vt_trailer_cpio ${vtoy_iso_part} "${vt_chosen_path}" noinit

    ventoy_debug_pause

    #boot image file
    vt_set_boot_opt rdinit=/vtoy/vtoy live-media=/dev/dm-1 ventoyos=tails
    vt_img_hook_root
    
    set root=(vtimghd,1)
    configfile (vtimghd,1)/efi/debian/grub.cfg

    vt_img_unhook_root
    vt_unset_boot_opt
}

function ventoy_img_fydeos {
    if [ "$grub_platform" = "pc" ]; then
        only_uefi_tip
        return
    fi

    vt_load_cpio  $vtoy_path  "${vt_chosen_path}" ${vtoy_iso_part} "busybox=64"
    vt_trailer_cpio ${vtoy_iso_part} "${vt_chosen_path}" noinit

    ventoy_debug_pause

    #boot image file
    vt_set_boot_opt rdinit=/vtoy/vtoy ventoyos=fydeos
    vt_img_hook_root

    set grubdisk=vtimghd
    set grubpartA=(vtimghd,3)
    set grubpartB=(vtimghd,5)
    set linuxpartA=(sda,3)
    set linuxpartB=(sda,5)

    set root=(vtimghd,12)
    configfile (vtimghd,12)/efi/boot/grub.cfg

    vt_img_unhook_root
    vt_unset_boot_opt
    
    unset grubdisk
    unset grubpartA
    unset grubpartB
    unset linuxpartA
    unset linuxpartB
}

function ventoy_img_cloudready {
    if [ "$grub_platform" = "pc" ]; then
        only_uefi_tip
        return
    fi

    vt_load_cpio  $vtoy_path  "${vt_chosen_path}" ${vtoy_iso_part} "busybox=64"
    vt_trailer_cpio ${vtoy_iso_part} "${vt_chosen_path}" noinit

    ventoy_debug_pause

    #boot image file
    vt_set_boot_opt rdinit=/vtoy/vtoy ventoyos=cloudready
    vt_img_hook_root

    set grubdisk=vtimghd
    set grubpartA=(vtimghd,3)
    set grubpartB=(vtimghd,5)
    set linuxpartA=(sda,3)
    set linuxpartB=(sda,5)

    set root=(vtimghd,12)    
    configfile (vtimghd,12)/efi/boot/grub.cfg

    vt_img_unhook_root
    vt_unset_boot_opt
    
    unset grubdisk
    unset grubpartA
    unset grubpartB
    unset linuxpartA
    unset linuxpartB
}


function ventoy_img_fwts {
    vt_load_cpio  $vtoy_path  "${vt_chosen_path}" ${vtoy_iso_part} "busybox=$ventoy_busybox_ver"
    vt_trailer_cpio ${vtoy_iso_part} "${vt_chosen_path}" noinit

    ventoy_debug_pause

    #boot image file
    vt_set_boot_opt rdinit=/vtoy/vtoy ventoyos=fwts
    vt_img_hook_root
    
    configfile $prefix/distro/fwts.cfg

    vt_img_unhook_root
    vt_unset_boot_opt
}

function ventoy_img_memtest86 {      
    chainloader (vtimghd,1)/efi/boot/BOOTX64.efi
    boot
}

function img_unsupport_tip {
    echo -e "\n This IMG file is NOT supported now. \n"
    echo -e " 当前不支持启动此 IMG 文件 \n"    
    echo -en "\n$VTLANG_ENTER_EXIT ..."
    read vtInputKey 
}

function legacy_img_memdisk {
    linux16   $vtoy_path/memdisk
    echo "Loading img file to memory ..."
    initrd16  "${1}${2}"
    
    ventoy_cli_console
    boot
}

function img_common_menuentry {
    set ventoy_compatible=YES
    set ventoy_busybox_ver=32
    unset LoadIsoEfiDriver

    vt_chosen_img_path vt_chosen_path vt_chosen_size vt_chosen_name
    
    if vt_check_password "${vt_chosen_path}"; then
        return
    fi

    if ventoy_vcfg_proc "${vt_chosen_path}"; then        
        return
    fi

    if [ "$grub_platform" = "pc" ]; then
        if vt_check_mode 0 "$vt_chosen_name"; then
            legacy_img_memdisk $vtoy_iso_part "$vt_chosen_path"
            return
        fi
    fi

    loopback vtimghd "${vtoy_iso_part}${vt_chosen_path}"
    vt_img_sector "${vtoy_iso_part}${vt_chosen_path}"

    vt_img_part_info (vtimghd)

    set vtback_root=$root
    ventoy_cli_console
    vt_push_last_entry
    set vtback_theme=$theme
    unset theme

    vt_img_extra_initrd_reset


    vt_get_fs_label (vtimghd,1) vtImgHd1Label

    if [ "$vtImgHd1Label" = "STATE" ]; then
        vt_get_fs_label (vtimghd,3) vtImgHd3Label
    elif [ -d (vtimghd,2)/lib ]; then
        vt_get_fs_label (vtimghd,2) vtImgHd2Label
    fi

    if [ -z "$vtImgHd1Label" ]; then
        if [ -d (vtimghd,2)/efi ]; then
            vt_get_fs_label (vtimghd,3) vtImgHd3Label
        elif [ -d (vtimghd,12)/efi ]; then
            vt_get_fs_label (vtimghd,3) vtImgHd3Label
        fi
    fi

    if [ -e (vtimghd,1)/etc/hostname ]; then
        vt_1st_line (vtimghd,1)/etc/hostname vtImgHostname
    fi

    if vt_str_begin "$vtImgHd3Label" "ROOT-"; then
        if [ -f (vtimghd,3)/etc/os-release.d/ID ]; then 
            vt_1st_line (vtimghd,3)/etc/os-release.d/ID vt_release_line1
            if vt_str_begin "$vt_release_line1" "FydeOS"; then
                ventoy_img_fydeos
            else
                ventoy_img_cloudready
            fi
        elif [ -f (vtimghd,3)/etc/cloudready-release ]; then
            ventoy_img_cloudready
        elif [ -f (vtimghd,3)/etc/chrome_dev.conf ]; then
            ventoy_img_cloudready
        fi
    elif vt_str_begin "$vtImgHd3Label" "fwts-result"; then
        ventoy_img_fwts
    elif vt_str_begin "$vtImgHd1Label" "LAKKA"; then
        ventoy_img_openelec lakka
    elif vt_str_begin "$vtImgHd1Label" "LIBREELEC"; then
        ventoy_img_openelec LibreELEC
    elif vt_str_begin "$vtImgHd1Label" "paldo-live"; then
        ventoy_img_paldo
    elif vt_str_begin "$vtImgHostname" "freedombox"; then
        ventoy_img_freedombox
    elif vt_str_begin "$vtImgHd1Label" "BATOCERA"; then
        ventoy_img_batocera
    elif vt_str_begin "$vtImgHd1Label" "Tails"; then
        ventoy_img_tails
    elif [ "$vtImgHd2Label" = "RECALBOX" -o "$vtImgHd1Label" = "RECALBOX" ]; then
        ventoy_img_recalbox
    elif [ "$vtImgHd1Label" = "ESYSRESCUE" ]; then
        ventoy_img_esysrescue
    elif [ -e (vtimghd,1)/easy.sfs ]; then
        ventoy_img_easyos
    elif [ -d (vtimghd,2)/easyos ]; then
        ventoy_img_easyos2
    elif [ -e (vtimghd,1)/volumio.initrd ]; then
        ventoy_img_volumio
    elif [ -f (vtimghd,2)/loader/entries/ubos.conf ]; then
        ventoy_img_ubos
    elif [ -f (vtimghd,2)/etc/openwrt_version ]; then
        ventoy_img_openwrt    
    else
        if [ -f (vtimghd,1)/efi/boot/mt86.png ]; then 
            if [ "$grub_platform" = "pc" ]; then
                img_unsupport_tip
            fi 
        fi

        #common chain
        vt_linux_chain_data "${vtoy_iso_part}${vt_chosen_path}"
        ventoy_acpi_param ${vtoy_chain_mem_addr} 512
        if [ "$grub_platform" = "pc" ]; then 
            linux16 $vtoy_path/ipxe.krn ${vtdebug_flag}  sector512  mem:${vtoy_chain_mem_addr}:size:${vtoy_chain_mem_size}   
            boot
        else            
            chainloader ${vtoy_path}/ventoy_${VTOY_EFI_ARCH}.efi sector512 env_param=${env_param} isoefi=${LoadIsoEfiDriver} FirstTry=${FirstTryBootFile} ${vtdebug_flag} mem:${vtoy_chain_mem_addr}:size:${vtoy_chain_mem_size}
            boot
        fi
    fi

    loopback -d vtimghd

    set root=$vtback_root
    vt_pop_last_entry
    set theme=$vtback_theme
    ventoy_gui_console
    set ventoy_compatible=NO
}

function img_unsupport_menuentry {
    common_unsupport_menuentry
}

function mimg_common_menuentry {
    vt_chosen_img_path vt_chosen_path vt_chosen_size vt_chosen_name

    if vt_check_password "${vt_chosen_path}"; then
        return
    fi

    echo "memdisk mode boot for $vt_chosen_path"
    echo ""
    ventoy_debug_pause

    if [ "$grub_platform" = "pc" ]; then
        legacy_img_memdisk $vtoy_iso_part "$vt_chosen_path"
    else
        vt_load_img_memdisk "$vtoy_iso_part$vt_chosen_path" vtoy_img_buf
        ventoy_cli_console
        chainloader ${vtoy_path}/ventoy_${VTOY_EFI_ARCH}.efi memdisk env_param=${env_param} isoefi=${LoadIsoEfiDriver} ${vtdebug_flag} mem:${vtoy_img_buf_addr}:size:${vtoy_img_buf_size}
        boot
        ventoy_gui_console    
    fi      
}


#############################################################
#############################################################
#############################################################
#######                 主处理流程                  ###########
#############################################################
#############################################################
#############################################################

# 定义Ventoy版本号
set VENTOY_VERSION="2.0.01"

# ACPI与Windows7/8不兼容，因此默认禁用
set VTOY_PARAM_NO_ACPI=1

# 默认菜单显示模式，可根据需要修改
#    0: 列表模式   
#    1: 树状视图模式
set VTOY_DEFAULT_MENU_MODE=0

# 定义菜单中显示的各类模式文本
set VTOY_MEM_DISK_STR="[内存磁盘]"
set VTOY_ISO_RAW_STR="兼容模式"
set VTOY_GRUB2_MODE_STR="GRUB2模式"
set VTOY_WIMBOOT_MODE_STR="WIMBOOT模式"
set VTOY_ISO_UEFI_DRV_STR="UEFI文件系统"

# 定义功能键对应的命令
set VTOY_F2_CMD="vt_browser_disk"       # F2键：浏览磁盘
set VTOY_F4_CMD="ventoy_localboot"      # F4键：本地启动
set VTOY_F5_CMD="ventoy_diagnosis"      # F5键：Ventoy诊断
set VTOY_F6_CMD="ventoy_ext_menu"       # F6键：扩展菜单
set VTOY_HELP_CMD="ventoy_show_help"    # 帮助命令
set VTOY_CHKSUM_CMD="ventoy_checksum"   # 校验和命令
set VTOY_HELP_TXT_LANGUAGE="en_US"      # 帮助文本语言
set VTOY_CHKSUM_FILE_PATH="X"           # 校验和文件路径
set VTOY_LANG_CMD="ventoy_language"     # 语言设置命令


# 根据GRUB平台类型设置版本显示文本和架构信息
if [ "$grub_platform" = "pc" ]; then
    # BIOS平台
    set VTOY_TEXT_MENU_VER="版本：$VENTOY_VERSION 平台：BIOS"
else    
    # UEFI平台，根据CPU架构区分
    if [ "$grub_cpu" = "i386" ]; then
        set VTOY_EFI_ARCH=ia32
        set VTOY_TEXT_MENU_VER="版本：$VENTOY_VERSION 平台：IA32"
    elif [ "$grub_cpu" = "arm64" ]; then
        set VTOY_EFI_ARCH=aa64
        set VTOY_TEXT_MENU_VER="版本：$VENTOY_VERSION 平台：AA64"
    elif [ "$grub_cpu" = "mips64el" ]; then
        set VTOY_EFI_ARCH=mips
        set VTOY_TEXT_MENU_VER="版本：$VENTOY_VERSION 平台：MIPS"
    else
        set VTOY_EFI_ARCH=x64
        set VTOY_TEXT_MENU_VER="版本：$VENTOY_VERSION 平台：UEFI"    
    fi
fi

# 获取Ventoy设备信息
vt_device $root  vtoy_dev

# 如果是TFTP启动方式，特殊处理路径和字体加载
if [ "$vtoy_dev" = "tftp" ]; then
    set vtoy_path=($root)
    # 查找包含Ventoy文件的分区
    for vtid in 0 1 2 3; do
        if [ -f (hd$vtid,2)/ventoy/ventoy.cpio ]; then
            set vtoy_iso_part=(hd$vtid,1)   # ISO文件所在分区
            set vtoy_efi_part=(hd$vtid,2)   # EFI系统分区
            set vtoydev=hd$vtid
            break
        fi
    done
    loadfont ascii  # 加载ASCII字体

    # 加载Unicode字体（如果存在）
    if [ -n "$vtoy_efi_part" ]; then
        vt_load_file_to_mem "auto" $vtoy_efi_part/grub/fonts/unicode.pf2 vtoy_font_mem
        loadfont mem:${vtoy_font_mem_addr}:size:${vtoy_font_mem_size}
    fi

    # 设置插件路径
    if [ -f $vtoy_efi_part/ventoy/ventoy.json ]; then
        set vt_plugin_path=$vtoy_efi_part
    else
        set vt_plugin_path=$prefix
        vt_load_plugin $vt_plugin_path
    fi
else
    # 本地设备启动方式，设置路径
    if [ "$prefix" = "(ventoydisk)/grub" ]; then
        set vtoy_path=(ventoydisk)/ventoy
    else
        set vtoy_path=($root)/ventoy
    fi

    set vtoydev=$vtoy_dev
    set vtoy_iso_part=($vtoy_dev,2)   # ISO文件所在分区
    set vtoy_efi_part=($vtoy_dev,2)   # EFI系统分区
    
    # 加载Unicode字体
    vt_load_file_to_mem "auto" $prefix/fonts/unicode.pf2 vtoy_font_mem
    loadfont mem:${vtoy_font_mem_addr}:size:${vtoy_font_mem_size}

    set vt_plugin_path=$vtoy_efi_part
fi

# 加载分区表
vt_load_part_table $vtoydev

# 加载菜单语言文件
ventoy_load_menu_lang_file

# 加载插件配置
if [ -f $vtoy_efi_part/ventoy/ventoy.json ]; then
    clear
    vt_load_plugin $vtoy_efi_part
    clear
else
    # 检查JSON文件路径大小写
    vt_check_json_path_case $vtoy_efi_part
fi

# 初始化菜单语言
if [ -n "$VTOY_MENU_LANGUAGE" ]; then
    vt_init_menu_lang "$VTOY_MENU_LANGUAGE"
else
    vt_init_menu_lang en_US  # 默认使用英文
fi

# 设置菜单超时时间
if [ -n "$VTOY_MENU_TIMEOUT" ]; then
    set timeout=$VTOY_MENU_TIMEOUT
else
    unset timeout  # 不超时
fi

# 加载wimboot镜像（用于支持WIM文件启动）
if [ -f $vtoy_efi_part/ventoy/ventoy_wimboot.img ]; then
    vt_load_wimboot $vtoy_efi_part/ventoy/ventoy_wimboot.img
elif [ -f $vtoy_efi_part/ventoy/ventoy_wimboot.img ]; then
    vt_load_wimboot $vtoy_efi_part/ventoy/ventoy_wimboot.img
fi

# 加载vhdboot镜像（用于支持VHD文件启动）
if [ -f $vtoy_efi_part/ventoy/ventoy_vhdboot.img ]; then
    vt_load_vhdboot $vtoy_efi_part/ventoy/ventoy_vhdboot.img
elif [ -f $vtoy_efi_part/ventoy/ventoy_vhdboot.img ]; then
    vt_load_vhdboot $vtoy_efi_part/ventoy/ventoy_vhdboot.img
fi


# 设置F3键功能（切换菜单模式：列表/树状）
if [ $VTOY_DEFAULT_MENU_MODE -eq 0 ]; then
    set VTOY_F3_CMD="vt_dynamic_menu 1 1"  # 当前是列表模式，F3切换到树状
else
    set VTOY_F3_CMD="vt_dynamic_menu 1 0"  # 当前是树状模式，F3切换到列表
fi

# 设置终端输出为控制台
terminal_output  console

# 配置图形模式分辨率
if [ -n "$vtoy_gfxmode" ]; then
    set gfxmode=$vtoy_gfxmode
    set gfxpayload=keep  # 保持图形模式
else
    set gfxmode=1920x1080  # 默认分辨率
    set gfxpayload=keep
fi


# 根据显示模式配置终端
if [ "$vtoy_display_mode" = "CLI" ]; then
    # 命令行模式
    terminal_output  console
elif [ "$vtoy_display_mode" = "serial" ]; then
    # 串口模式
    if [ -n "$vtoy_serial_param" ]; then
        serial $vtoy_serial_param
    fi
    terminal_input   serial
    terminal_output  serial
elif [ "$vtoy_display_mode" = "serial_console" ]; then
    # 串口+控制台模式
    if [ -n "$vtoy_serial_param" ]; then
        serial $vtoy_serial_param
    fi
    terminal_input   serial console
    terminal_output  serial console    
else
    # 图形模式
    if [ "$vtoy_gfxmode" = "max" ]; then
        # 自动适配最大分辨率
        set gfxmode=1920x1080
        terminal_output  gfxterm

        vt_enum_video_mode  # 枚举视频模式
        vt_get_video_mode 0 vtCurMode
        terminal_output console
        set gfxmode=$vtCurMode
        terminal_output gfxterm
    elif [ "$vtoy_res_fit" = "1" ]; then
        terminal_output  gfxterm
    fi    

    # 设置主题
    if [ -n "$vtoy_theme" ]; then
        vt_set_theme
    else
        set theme=$prefix/themes/ventoy/theme.txt  # 默认主题
    fi
    terminal_output  gfxterm
fi

# EFI平台下的鼠标配置
if [ "$grub_platform" = "efi" ]; then
    set mouse_delta=4000  # 鼠标灵敏度
    # terminal_input --append mouse  # 启用鼠标（注释掉表示默认不启用）
fi

# 设置默认键盘布局
if [ -n "$VTOY_DEFAULT_KBD_LAYOUT" ]; then
    set_keyboard_layout "$VTOY_DEFAULT_KBD_LAYOUT"
fi

# 检查路径大小写不匹配错误（ventoy目录和json文件必须全小写）
if [ -n "$VTOY_PLUGIN_PATH_CASE_MISMATCH" ]; then
    clear
    echo "$VTOY_PLUGIN_PATH_CASE_MISMATCH"
    echo -e "\n\n路径大小写不匹配！ventoy 目录和 ventoy.json 文件的名字必须是全部小写，请修正！"
    echo -e "\n\npress ENTER to continue (请按回车键继续) ..."
    read vtInputKey
fi



# 显示主菜单前清除所有输入按键
vt_clear_key

# 导出必要的变量供后续使用
export theme
export gfxmode
export gfxpayload
export vtoydev
export vtoy_path
export vtdebug_flag
export vtoy_iso_fs
export vtoy_iso_part
export vtoy_efi_part
export VENTOY_VERSION
export VTOY_CUR_VIDEO_MODE
export VTOY_EFI_ARCH
export VTOY_MEM_DISK_STR
export VTOY_ISO_RAW_STR
export VTOY_GRUB2_MODE_STR
export VTOY_WIMBOOT_MODE_STR
export VTOY_ISO_UEFI_DRV_STR
export VTOY_F2_CMD
export VTOY_F4_CMD
export VTOY_F5_CMD
export VTOY_F6_CMD
export VTOY_HELP_CMD
export VTOY_CHKSUM_CMD
export VTOY_HELP_TXT_LANGUAGE
export VTOY_CHKSUM_FILE_PATH
export VTOY_LANG_CMD


# 收集所有镜像文件（ISO等）
set ventoy_img_count=0
vt_list_img $vtoy_iso_part ventoy_img_count

# 始终显示菜单（无论是否有镜像文件）
if [ $VTOY_DEFAULT_MENU_MODE -eq 0 ]; then
    vt_dynamic_menu 0 0  # 列表模式
else
    vt_dynamic_menu 0 1  # 树状模式
fi


# 处理默认镜像和热键自动启动
if [ -n "$VTOY_DEFAULT_IMAGE" ]; then
    # 解析默认镜像配置（格式如"F2>镜像路径"）
    if regexp --set 1:vtHotkey --set 2:vtDefault "(F[2-9])>(.*)" "$VTOY_DEFAULT_IMAGE"; then
    
        set default="$vtDefault"  # 设置默认启动项
        # 设置超时时间
        if [ -z "$VTOY_MENU_TIMEOUT" ]; then
            set timeout=0
        else
            set timeout=$VTOY_MENU_TIMEOUT
        fi
        
        export timeout
        export default

        vt_fn_mutex_lock 1  # 加锁防止冲突

        # 执行热键对应的命令
        if [ "$vtHotkey" = "F2" ]; then
            unset timeout
            vt_browser_disk
        elif [ "$vtHotkey" = "F4" ]; then
            ventoy_localboot
        elif [ "$vtHotkey" = "F5" ]; then
            ventoy_diagnosis
        elif [ "$vtHotkey" = "F6" ]; then
            ventoy_ext_menu
        fi
        
        vt_fn_mutex_lock 0  # 解锁
        
        unset timeout
        unset default
    fi    
fi