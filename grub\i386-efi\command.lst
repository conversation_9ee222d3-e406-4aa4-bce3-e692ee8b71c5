*acpi: acpi
*all_functional_test: functional_test
*background_image: gfxterm_background
*bls_import: blscfg
*blscfg: blscfg
*cat: cat
*cpuid: cpuid
*crc: hashsum
*cryptomount: cryptodisk
*echo: echo
*extract_syslinux_entries_configfile: syslinuxcfg
*extract_syslinux_entries_source: syslinuxcfg
*file: file
*functional_test: functional_test
*fwconnect: fwload
*fwload: fwload
*gettext: gettext
*hashsum: hashsum
*hdparm: hdparm
*hello: hello
*help: help
*hexdump: hexdump
*inb: iorw
*inl: iorw
*inw: iorw
*keystatus: keystatus
*kfreebsd: bsd
*knetbsd: bsd
*kopenbsd: bsd
*list_env: loadenv
*load_env: loadenv
*loopback: loopback
*ls: ls
*lsacpi: lsacpi
*lspci: lspci
*md5sum: hashsum
*menuentry: normal
*pcidump: pcidump
*probe: probe
*rdmsr: rdmsr
*read_byte: memrw
*read_dword: memrw
*read_word: memrw
*regexp: regexp
*save_env: loadenv
*search: search
*serial: serial
*set_keyboard_layout: setkey
*setkey: setkey
*setpci: setpci
*sha1sum: hashsum
*sha256sum: hashsum
*sha512sum: hashsum
*sleep: sleep
*smbios: smbios
*submenu: normal
*syslinux_configfile: syslinuxcfg
*syslinux_source: syslinuxcfg
*terminfo: terminfo
*test_blockarg: test_blockarg
*testspeed: testspeed
*tr: tr
*trust: pgp
*verify_detached: pgp
*xnu_splash: xnu
*zfskey: zfscrypt
.: configfile
[: test
appleloader: appleldr
authenticate: normal
background_color: gfxterm_background
backtrace: backtrace
badram: mmap
blocklist: blocklist
boot: boot
break: normal
cat: minicmd
cbmemc: cbmemc
chainloader: chain
clear: normal
cmp: cmp
configfile: configfile
continue: normal
coreboot_boottime: cbtime
cutmem: mmap
date: date
distrust: pgp
dump: minicmd
eval: eval
exit: minicmd
export: normal
extract_entries_configfile: configfile
extract_entries_source: configfile
extract_legacy_entries_configfile: legacycfg
extract_legacy_entries_source: legacycfg
fakebios: loadbios
false: true
fix_video: fixvideo
fwsetup: efifwsetup
gdbstub: gdb
gdbstub_break: gdb
gdbstub_stop: gdb
gptsync: gptsync
halt: halt
help: minicmd
hexdump_random: random
initrd16: linux16
initrd: linux
initrdefi: linux
keymap: keylayouts
kfreebsd_loadenv: bsd
kfreebsd_module: bsd
kfreebsd_module_elf: bsd
knetbsd_module: bsd
knetbsd_module_elf: bsd
kopenbsd_ramdisk: bsd
legacy_check_password: legacycfg
legacy_configfile: legacycfg
legacy_initrd: legacycfg
legacy_initrd_nounzip: legacycfg
legacy_kernel: legacycfg
legacy_password: legacycfg
legacy_source: legacycfg
linux16: linux16
linux: linux
linuxefi: linux
list_trusted: pgp
loadbios: loadbios
loadfont: font
lscoreboot: cbls
lsefi: lsefi
lsefimmap: lsefimmap
lsefisystab: lsefisystab
lsfonts: font
lsmmap: lsmmap
lsmod: minicmd
lssal: lssal
macppcbless: macbless
mactelbless: macbless
module2: multiboot2
module: multiboot
multiboot2: multiboot2
multiboot: multiboot
nativedisk: nativedisk
net_add_addr: net
net_add_dns: net
net_add_route: net
net_bootp: net
net_del_addr: net
net_del_dns: net
net_del_route: net
net_dhcp: net
net_get_dhcp_option: net
net_ipv6_autoconf: net
net_ls_addr: net
net_ls_cards: net
net_ls_dns: net
net_ls_routes: net
net_nslookup: net
normal: normal
normal_exit: normal
outb: iorw
outl: iorw
outw: iorw
parttool: parttool
password: password
password_pbkdf2: password_pbkdf2
play: play
read: read
reboot: reboot
return: normal
rmmod: minicmd
search.file: search_fs_file
search.fs_label: search_label
search.fs_uuid: search_fs_uuid
setparams: normal
shift: normal
source: configfile
terminal_input: terminal
terminal_output: terminal
test: test
testload: testload
time: time
true: true
usb: usbtest
videoinfo: videoinfo
videotest: videotest
vt_img_extra_initrd_append: linux
vt_img_extra_initrd_reset: linux
vt_set_boot_opt: linux
vt_unset_boot_opt: linux
write_byte: memrw
write_dword: memrw
write_word: memrw
wrmsr: wrmsr
xnu_devprop_load: xnu
xnu_kernel64: xnu
xnu_kernel: xnu
xnu_kext: xnu
xnu_kextdir: xnu
xnu_mkext: xnu
xnu_ramdisk: xnu
xnu_resume: xnu
xnu_uuid: xnu_uuid
zfs-bootfs: zfsinfo
zfsinfo: zfsinfo
