#ventoy_pack.sh will generate menuentry here
menuentry "zh_CN  -  Chinese Simplified (简体中文)" --class=menu_lang_item --class=debug_menu_lang --class=F5tool {
    vt_load_menu_lang zh_CN
}
menuentry "ar_AR  -  Arabic (العربية)" --class=menu_lang_item --class=debug_menu_lang --class=F5tool {
    vt_load_menu_lang ar_AR
}
menuentry "bn_BN  -  Bengali (বাংলা)" --class=menu_lang_item --class=debug_menu_lang --class=F5tool {
    vt_load_menu_lang bn_BN
}
menuentry "cs_CZ  -  Czech (Čeština)" --class=menu_lang_item --class=debug_menu_lang --class=F5tool {
    vt_load_menu_lang cs_CZ
}
menuentry "de_DE  -  German (Deutsch)" --class=menu_lang_item --class=debug_menu_lang --class=F5tool {
    vt_load_menu_lang de_DE
}
menuentry "el_GR  -  Greek (Ελληνικά)" --class=menu_lang_item --class=debug_menu_lang --class=F5tool {
    vt_load_menu_lang el_GR
}
menuentry "en_US  -  English (English)" --class=menu_lang_item --class=debug_menu_lang --class=F5tool {
    vt_load_menu_lang en_US
}
menuentry "es_ES  -  Spanish (Español)" --class=menu_lang_item --class=debug_menu_lang --class=F5tool {
    vt_load_menu_lang es_ES
}
menuentry "fr_FR  -  French (Français)" --class=menu_lang_item --class=debug_menu_lang --class=F5tool {
    vt_load_menu_lang fr_FR
}
menuentry "hi_HI  -  Hindi (हिन्दी)" --class=menu_lang_item --class=debug_menu_lang --class=F5tool {
    vt_load_menu_lang hi_HI
}
menuentry "hr_HR  -  Croatian (Hrvatski)" --class=menu_lang_item --class=debug_menu_lang --class=F5tool {
    vt_load_menu_lang hr_HR
}
menuentry "hu_HU  -  Magyar (Hungarian)" --class=menu_lang_item --class=debug_menu_lang --class=F5tool {
    vt_load_menu_lang hu_HU
}
menuentry "id_ID  -  Indonesian (Bahasa Indonesia)" --class=menu_lang_item --class=debug_menu_lang --class=F5tool {
    vt_load_menu_lang id_ID
}
menuentry "it_IT  -  Italiano (Italian)" --class=menu_lang_item --class=debug_menu_lang --class=F5tool {
    vt_load_menu_lang it_IT
}
menuentry "ja_JP  -  Japanese (日本語)" --class=menu_lang_item --class=debug_menu_lang --class=F5tool {
    vt_load_menu_lang ja_JP
}
menuentry "ka_GE  -  Georgian (ქართული)" --class=menu_lang_item --class=debug_menu_lang --class=F5tool {
    vt_load_menu_lang ka_GE
}
menuentry "ko_KR  -  Korean (한국어)" --class=menu_lang_item --class=debug_menu_lang --class=F5tool {
    vt_load_menu_lang ko_KR
}
menuentry "pl_PL  -  Polish (Poland)" --class=menu_lang_item --class=debug_menu_lang --class=F5tool {
    vt_load_menu_lang pl_PL
}
menuentry "pt_BR  -  Brazilian Portuguese (Português Brasileiro)" --class=menu_lang_item --class=debug_menu_lang --class=F5tool {
    vt_load_menu_lang pt_BR
}
menuentry "pt_PT  -  Portuguese (Português de Portugal)" --class=menu_lang_item --class=debug_menu_lang --class=F5tool {
    vt_load_menu_lang pt_PT
}
menuentry "ru_RU  -  Russian (Русский)" --class=menu_lang_item --class=debug_menu_lang --class=F5tool {
    vt_load_menu_lang ru_RU
}
menuentry "sl_si  -  Slovenija (Slovenski)" --class=menu_lang_item --class=debug_menu_lang --class=F5tool {
    vt_load_menu_lang sl_si
}
menuentry "sr_RS  -  Serbian Latin (Srpski)" --class=menu_lang_item --class=debug_menu_lang --class=F5tool {
    vt_load_menu_lang sr_RS
}
menuentry "ta_IN  -  Tamil (தமிழ்)" --class=menu_lang_item --class=debug_menu_lang --class=F5tool {
    vt_load_menu_lang ta_IN
}
menuentry "tr_TR  -  Turkish (Türkçe)" --class=menu_lang_item --class=debug_menu_lang --class=F5tool {
    vt_load_menu_lang tr_TR
}
menuentry "uk_UA  -  Ukrainian (Українська)" --class=menu_lang_item --class=debug_menu_lang --class=F5tool {
    vt_load_menu_lang uk_UA
}
menuentry "vi_VN  -  Vietnamese (Tiếng Việt)" --class=menu_lang_item --class=debug_menu_lang --class=F5tool {
    vt_load_menu_lang vi_VN
}
menuentry "zh_TW  -  Chinese Traditional (正體中文)" --class=menu_lang_item --class=debug_menu_lang --class=F5tool {
    vt_load_menu_lang zh_TW
}
menuentry "$VTLANG_RETURN_PREVIOUS" --class=vtoyret VTOY_RET {
        echo "Return ..."
}
