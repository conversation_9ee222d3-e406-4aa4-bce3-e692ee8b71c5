#!/usr/bin/env python3
"""
基于十六进制转储的EFI文件分析工具
Hexdump-based EFI File Analysis Tool
"""

import os
import sys
import struct
import hashlib
import json
import re
from pathlib import Path
from typing import Dict, List, Any, Optional, Tuple

class HexdumpEFIAnalyzer:
    """基于十六进制转储的EFI文件分析器"""
    
    def __init__(self, file_path: str):
        self.file_path = file_path
        self.file_data = None
        
    def load_file(self) -> bool:
        """加载EFI文件"""
        try:
            with open(self.file_path, 'rb') as f:
                self.file_data = f.read()
            return True
        except Exception as e:
            print(f"Error loading file {self.file_path}: {e}")
            return False
    
    def get_basic_info(self) -> Dict[str, Any]:
        """获取基本文件信息"""
        if not self.file_data:
            return {}
        
        info = {
            "file_path": self.file_path,
            "file_size": len(self.file_data),
            "file_size_kb": round(len(self.file_data) / 1024, 2),
            "md5": hashlib.md5(self.file_data).hexdigest(),
            "sha1": hashlib.sha1(self.file_data).hexdigest(),
            "sha256": hashlib.sha256(self.file_data).hexdigest()
        }
        
        return info
    
    def analyze_pe_header_simple(self) -> Dict[str, Any]:
        """简单的PE头分析"""
        if not self.file_data or len(self.file_data) < 64:
            return {"error": "File too small"}
        
        header_info = {}
        
        # DOS头
        dos_sig = self.file_data[:2]
        header_info["dos_signature"] = dos_sig.hex().upper()
        header_info["dos_signature_valid"] = dos_sig == b'MZ'
        
        if dos_sig == b'MZ':
            # PE偏移
            pe_offset = struct.unpack('<L', self.file_data[60:64])[0]
            header_info["pe_offset"] = f"0x{pe_offset:08X}"
            
            if pe_offset + 4 <= len(self.file_data):
                pe_sig = self.file_data[pe_offset:pe_offset+4]
                header_info["pe_signature"] = pe_sig.hex().upper()
                header_info["pe_signature_valid"] = pe_sig == b'PE\x00\x00'
                
                # 尝试读取机器类型
                if pe_offset + 6 <= len(self.file_data):
                    machine_type = struct.unpack('<H', self.file_data[pe_offset+4:pe_offset+6])[0]
                    header_info["machine_type"] = f"0x{machine_type:04X}"
                    header_info["architecture"] = self._get_architecture(machine_type)
        
        return header_info
    
    def _get_architecture(self, machine_type: int) -> str:
        """获取架构描述"""
        arch_map = {
            0x014c: "Intel 386 (i386)",
            0x8664: "AMD64 (x86-64)",
            0xaa64: "ARM64 (AArch64)",
            0x01c0: "ARM (32-bit)",
            0x0200: "Intel IA64"
        }
        return arch_map.get(machine_type, f"Unknown (0x{machine_type:04X})")
    
    def extract_strings_advanced(self, min_length: int = 4) -> Dict[str, List[str]]:
        """提取字符串（分类）"""
        if not self.file_data:
            return {}
        
        # ASCII字符串
        ascii_strings = []
        unicode_strings = []
        
        # 提取ASCII字符串
        ascii_pattern = rb'[\x20-\x7E]{' + str(min_length).encode() + rb',}'
        for match in re.finditer(ascii_pattern, self.file_data):
            string = match.group().decode('ascii', errors='ignore')
            if len(string) >= min_length:
                ascii_strings.append(string)
        
        # 提取Unicode字符串（UTF-16LE）
        try:
            unicode_pattern = rb'(?:[\x20-\x7E]\x00){' + str(min_length).encode() + rb',}'
            for match in re.finditer(unicode_pattern, self.file_data):
                try:
                    string = match.group().decode('utf-16le', errors='ignore').rstrip('\x00')
                    if len(string) >= min_length and string not in unicode_strings:
                        unicode_strings.append(string)
                except:
                    pass
        except:
            pass
        
        # 分类字符串
        categorized = {
            "efi_protocols": [],
            "file_paths": [],
            "error_messages": [],
            "debug_strings": [],
            "certificates": [],
            "other_ascii": [],
            "unicode_strings": unicode_strings[:50]  # 限制数量
        }
        
        for string in ascii_strings[:200]:  # 限制总数
            string_lower = string.lower()
            
            if any(keyword in string_lower for keyword in ['protocol', 'guid', 'interface']):
                categorized["efi_protocols"].append(string)
            elif any(keyword in string_lower for keyword in ['\\', '/', '.efi', '.exe', '.dll']):
                categorized["file_paths"].append(string)
            elif any(keyword in string_lower for keyword in ['error', 'fail', 'invalid', 'corrupt']):
                categorized["error_messages"].append(string)
            elif any(keyword in string_lower for keyword in ['debug', 'trace', 'log', 'print']):
                categorized["debug_strings"].append(string)
            elif any(keyword in string_lower for keyword in ['cert', 'key', 'rsa', 'sha', 'signature']):
                categorized["certificates"].append(string)
            else:
                categorized["other_ascii"].append(string)
        
        # 限制每个类别的数量
        for key in categorized:
            if isinstance(categorized[key], list) and len(categorized[key]) > 20:
                categorized[key] = categorized[key][:20]
        
        return categorized
    
    def analyze_entropy(self, block_size: int = 1024) -> Dict[str, Any]:
        """分析文件熵值"""
        if not self.file_data:
            return {}
        
        def calculate_entropy(data):
            if not data:
                return 0.0

            byte_counts = [0] * 256
            for byte in data:
                byte_counts[byte] += 1

            entropy = 0.0
            data_len = len(data)

            import math
            for count in byte_counts:
                if count > 0:
                    probability = count / data_len
                    entropy -= probability * math.log2(probability)

            return entropy
        
        # 整体熵值
        overall_entropy = calculate_entropy(self.file_data)
        
        # 分块熵值分析
        block_entropies = []
        high_entropy_blocks = []
        
        for i in range(0, len(self.file_data), block_size):
            block = self.file_data[i:i+block_size]
            if len(block) >= 256:  # 只分析足够大的块
                entropy = calculate_entropy(block)
                block_entropies.append({
                    "offset": f"0x{i:08X}",
                    "size": len(block),
                    "entropy": round(entropy, 3)
                })
                
                # 高熵块可能表示加密或压缩数据
                if entropy > 7.5:
                    high_entropy_blocks.append({
                        "offset": f"0x{i:08X}",
                        "entropy": round(entropy, 3)
                    })
        
        return {
            "overall_entropy": round(overall_entropy, 3),
            "entropy_analysis": "High" if overall_entropy > 7.5 else "Medium" if overall_entropy > 6.0 else "Low",
            "block_count": len(block_entropies),
            "high_entropy_blocks": high_entropy_blocks[:10],  # 限制数量
            "average_block_entropy": round(sum(b["entropy"] for b in block_entropies) / len(block_entropies), 3) if block_entropies else 0
        }
    
    def find_embedded_files(self) -> List[Dict[str, Any]]:
        """查找嵌入的文件"""
        if not self.file_data:
            return []
        
        embedded_files = []
        
        # 查找常见文件签名
        signatures = {
            b'MZ': 'PE/EXE',
            b'PK': 'ZIP/JAR',
            b'\x7fELF': 'ELF',
            b'\xca\xfe\xba\xbe': 'Java Class',
            b'\x89PNG': 'PNG Image',
            b'GIF8': 'GIF Image',
            b'\xff\xd8\xff': 'JPEG Image',
            b'%PDF': 'PDF Document',
            b'<?xml': 'XML Document'
        }
        
        for signature, file_type in signatures.items():
            offset = 0
            while True:
                pos = self.file_data.find(signature, offset)
                if pos == -1:
                    break
                
                embedded_files.append({
                    "offset": f"0x{pos:08X}",
                    "type": file_type,
                    "signature": signature.hex().upper()
                })
                
                offset = pos + 1
                
                # 限制查找数量
                if len(embedded_files) >= 20:
                    break
        
        return embedded_files
    
    def analyze_all(self) -> Dict[str, Any]:
        """执行完整分析"""
        if not self.load_file():
            return {"error": "Failed to load file"}
        
        results = {
            "basic_info": self.get_basic_info(),
            "pe_header": self.analyze_pe_header_simple(),
            "strings": self.extract_strings_advanced(),
            "entropy_analysis": self.analyze_entropy(),
            "embedded_files": self.find_embedded_files()
        }
        
        return results

def main():
    """主函数"""
    print("=== 基于十六进制转储的EFI文件分析工具 ===")
    print("=== Hexdump-based EFI File Analysis Tool ===\n")
    
    efi_files = [
        "EFI/BOOT/BOOTX64.EFI",
        "EFI/BOOT/grub.efi",
        "EFI/BOOT/BOOTAA64.EFI",
        "EFI/BOOT/BOOTIA32.EFI"
    ]
    
    all_results = {}
    
    for efi_file in efi_files:
        if os.path.exists(efi_file):
            print(f"分析EFI文件: {efi_file}")
            analyzer = HexdumpEFIAnalyzer(efi_file)
            all_results[efi_file] = analyzer.analyze_all()
    
    # 保存结果
    with open('hexdump_efi_analysis_results.json', 'w', encoding='utf-8') as f:
        json.dump(all_results, f, indent=2, ensure_ascii=False)
    
    print(f"\n分析完成！结果已保存到 hexdump_efi_analysis_results.json")
    return all_results

if __name__ == "__main__":
    main()
