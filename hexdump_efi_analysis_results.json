{"EFI/BOOT/BOOTX64.EFI": {"basic_info": {"file_path": "EFI/BOOT/BOOTX64.EFI", "file_size": 965672, "file_size_kb": 943.04, "md5": "a47b8bb55bd2bbd277c59009aa477879", "sha1": "f33701d80f1614ad1c7e0a114f298196660c760f", "sha256": "a60d256c802849a0a5e23fe5298ddcf7f78445cc71f519b64573dcb61af0e6ff"}, "pe_header": {"dos_signature": "4D5A", "dos_signature_valid": true, "pe_offset": "0x00000080", "pe_signature": "50450000", "pe_signature_valid": true, "machine_type": "0x8664", "architecture": "AMD64 (x86-64)"}, "strings": {"efi_protocols": [], "file_paths": ["B/14", "h[]A\\A]A^A_", "[]A\\A]A^A_", "H[]A\\A]A^A_", "[]A\\A]A^A_", "[]A\\A]A^A_", "[]A\\A]A^", "[]A\\A]A^A_", "[]A\\A]A^A_", "[]A\\A]A^A_", "[]A\\", "[]A\\", "[]A\\A]A^A_", "[]A\\A]A^A_", "[]A\\A]", "\\$xL", "[]A\\A]A^", "\\$HL", "x[]A\\A]A^A_", "[]A\\A]A^A_"], "error_messages": [], "debug_strings": [], "certificates": [], "other_ascii": ["!This program cannot be run in DOS mode.", "@.text", "`.reloc", "@.data", "@.dynamic", ".rela", "@.sbat", "YZQR", "~CfB", "AWAVE1", "AUATUSH", "L$0L", "D$ H", "D$@H", "D$HH", "D$XH", "D$8H", "H;D$", "t$ L", "ATPL"], "unicode_strings": ["%a:%d:%a() trying to verify cert %d (%s)", "%a:%d:%a() cert[0:1] is [%02x%02x], should be [%02x%02x]", "%a:%d:%a() Cert length is %ld, expecting %ld", "2%a:%d:%a() AuthenticodeVerify() succeeded: %d", "AuthenticodeVerify(): %d", "Not a DER encoded x.509 Certificate", "%a:%d:%a() cert:", "binary sha256hash found in vendor dbx", "binary sha1hash found in vendor dbx", "cert sha256hash found in vendor dbx", "binary sha256hash found in system dbx", "binary sha1hash found in system dbx", "cert sha256hash found in system dbx", "MokListX", "binary sha256hash found in Mok dbx", "cert sha256hash found in Mok dbx", "check_db_hash(db, sha256hash) != DATA_FOUND", "check_db_hash(db, sha1hash) != DATA_FOUND", "MokListRT", "check_db_hash(MokListRT, sha256hash) != DATA_FOUND"]}, "entropy_analysis": {"overall_entropy": 5.791, "entropy_analysis": "Low", "block_count": 943, "high_entropy_blocks": [{"offset": "0x00094C00", "entropy": 7.777}, {"offset": "0x000EA400", "entropy": 7.507}], "average_block_entropy": 4.578}, "embedded_files": [{"offset": "0x00000000", "type": "PE/EXE", "signature": "4D5A"}, {"offset": "0x00027E4C", "type": "PE/EXE", "signature": "4D5A"}, {"offset": "0x0002A18D", "type": "PE/EXE", "signature": "4D5A"}, {"offset": "0x0002BDD9", "type": "PE/EXE", "signature": "4D5A"}, {"offset": "0x0006E533", "type": "PE/EXE", "signature": "4D5A"}, {"offset": "0x0002ADB6", "type": "ZIP/JAR", "signature": "504B"}, {"offset": "0x0004507A", "type": "ZIP/JAR", "signature": "504B"}, {"offset": "0x0008102F", "type": "ZIP/JAR", "signature": "504B"}, {"offset": "0x00081A5B", "type": "ZIP/JAR", "signature": "504B"}, {"offset": "0x00082A65", "type": "ZIP/JAR", "signature": "504B"}, {"offset": "0x00082E24", "type": "ZIP/JAR", "signature": "504B"}, {"offset": "0x00082E33", "type": "ZIP/JAR", "signature": "504B"}, {"offset": "0x0008684D", "type": "ZIP/JAR", "signature": "504B"}, {"offset": "0x00087145", "type": "ZIP/JAR", "signature": "504B"}, {"offset": "0x00087157", "type": "ZIP/JAR", "signature": "504B"}, {"offset": "0x00087168", "type": "ZIP/JAR", "signature": "504B"}, {"offset": "0x00087178", "type": "ZIP/JAR", "signature": "504B"}, {"offset": "0x0008718E", "type": "ZIP/JAR", "signature": "504B"}, {"offset": "0x000871A5", "type": "ZIP/JAR", "signature": "504B"}, {"offset": "0x000871DB", "type": "ZIP/JAR", "signature": "504B"}]}, "EFI/BOOT/grub.efi": {"basic_info": {"file_path": "EFI/BOOT/grub.efi", "file_size": 64120, "file_size_kb": 62.62, "md5": "f5787f65638d6e2688bf5c651fcd33d0", "sha1": "19e997ac4b8b848a357520e8a38b0ec7cb5b0934", "sha256": "290fad7c528fbc694c4963da6c0ec74543fba1e425c8f3e77c3c4218ff0d7bb3"}, "pe_header": {"dos_signature": "4D5A", "dos_signature_valid": true, "pe_offset": "0x00000080", "pe_signature": "50450000", "pe_signature_valid": true, "machine_type": "0x8664", "architecture": "AMD64 (x86-64)"}, "strings": {"efi_protocols": [], "file_paths": ["[]A\\", "[]A\\", "[]A\\", "\\$ H", "[]A\\A]", "[]A\\A]", "X[]A\\A]A^A_", "\\$PH", "\\$0D", "X[]A\\A]A^A_", "@[]A\\", "A\\A]A^A_", "\\$ H", "<$\\L", "<$\\uGI", "[]A\\A]A^A_", "[]A\\A]", "[]A\\A]", "[^_]A\\A]A^A_", "[^_]A\\A]A^"], "error_messages": [], "debug_strings": [], "certificates": [], "other_ascii": ["!This program cannot be run in DOS mode.", ".text", "`.reloc", "B.data", ".dynamic", ".rela", "@.dynsym", "@.sbat", "YZQR", "ATUSH", "d$ H", "D$(H", "D$$H", "t5f9", "AWAVAUATUSH", "|$ D", "L$`L", "D$hH", "L$`H", "D$`A"], "unicode_strings": ["SecureBoot", "Not a Secure Boot Platform %d", "Secure Boot Disabled", "Failed to install override security policy", "Failed to start ", "Failed to uninstall security policy.  Platform needs rebooting", "grubx64_real.efi", "Starting Position (%d,%d) is off screen", "Failed Allocation", "ERROR", "Success", "<PERSON><PERSON>", "Invalid Parameter", "Unsupported", "Bad Buffer Size", "<PERSON><PERSON><PERSON>", "Not Ready", "<PERSON><PERSON>", "Write Protected", "Out of Resources"]}, "entropy_analysis": {"overall_entropy": 5.492, "entropy_analysis": "Low", "block_count": 63, "high_entropy_blocks": [], "average_block_entropy": 4.504}, "embedded_files": [{"offset": "0x00000000", "type": "PE/EXE", "signature": "4D5A"}, {"offset": "0x0000F3D7", "type": "ZIP/JAR", "signature": "504B"}, {"offset": "0x0000F420", "type": "ZIP/JAR", "signature": "504B"}]}, "EFI/BOOT/BOOTAA64.EFI": {"basic_info": {"file_path": "EFI/BOOT/BOOTAA64.EFI", "file_size": 2220032, "file_size_kb": 2168.0, "md5": "c93db17afcd179ed7a852bca7323bbe2", "sha1": "218612848af8361e703fd9b057a5f958514a74db", "sha256": "bf14944a518acb3f2c3abcf0aa5301a0fa65e98590f1b87714cf05f5de6b05dd"}, "pe_header": {"dos_signature": "4D5A", "dos_signature_valid": true, "pe_offset": "0x00000080", "pe_signature": "50450000", "pe_signature_valid": true, "machine_type": "0xAA64", "architecture": "ARM64 (AArch64)"}, "strings": {"efi_protocols": ["/Protocol(%08x-%04x-%04x-%02x%02x-%02x%02x%02x%02x%02x%02x)"], "file_paths": ["kern/efi/fdt.c", "kern/arm64/cache.c", "kern/arm64/dl.c", "kern/arm64/dl_helper.c", "disk/efi/efidisk.c", "/%sVendor(%08x-%04x-%04x-%02x%02x-%02x%02x%02x%02x%02x%02x)[%x: ", "/EndEntire", "/EndThis", "/EndUnknown(%x)", "/PCI(%x,%x)", "/PCCARD(%x)", "/MMap(%x,%llx,%llx)", "/Ctrl(%x)", "/UnknownHW(%x)", "/ACPI(%x,%x)", "/ACPI(", "/UnknownACPI(%x)", "/ATAPI(%x,%x,%x)", "/SCSI(%x,%x)", "/FibreChannel(%llx,%llx)"], "error_messages": ["invalid arch-dependent ELF magic", "invalid buffer alignment %d", "invalid sector size %d", "failure reading sector 0x%llx from `%s'", "failure writing sector 0x%llx to `%s'", "set_virtual_address_map failed"], "debug_strings": [], "certificates": [], "other_ascii": ["!This program cannot be run in DOS mode.", ".text", "`.data", "mods", ".reloc", "RB|@", "8aDL", "8a,F", "8a,F", "Q'8@9&4@9%0@9", "!L@9", "aJ@9", "aF@9", "aB@9", "a>@9", "@@QB$", "\"@ysR", "Q!@A", "QB@A", "@9_p"], "unicode_strings": ["'2Gs", "yPQ<", "YqaQAwzWZmM", "t123", "n4567890", "A[sSveErpRPtTfFugUGcICoOlLdDihHjJkKnNxXVbB", "QV222222222]]", "]]]]]]]]]]]]]]]", "<GLQV[`ejoty~", "!\"#\"!!!!!!!!!", "+&)'*9<:;<@CAB432,-.2\"", "!!!!", "+($%98756@?>=4-.", "!!!!!", "!!!!!/0", "\"#)-35", "&Zt*)^v{", " $[% ?", "22[2", "M==II6==>D>K>"]}, "entropy_analysis": {"overall_entropy": 4.135, "entropy_analysis": "Low", "block_count": 2168, "high_entropy_blocks": [{"offset": "0x0009F800", "entropy": 7.737}, {"offset": "0x0009FC00", "entropy": 7.551}, {"offset": "0x000A0000", "entropy": 7.952}, {"offset": "0x000A0400", "entropy": 7.902}, {"offset": "0x00213400", "entropy": 7.621}], "average_block_entropy": 3.251}, "embedded_files": [{"offset": "0x00000000", "type": "PE/EXE", "signature": "4D5A"}, {"offset": "0x00083160", "type": "ZIP/JAR", "signature": "504B"}, {"offset": "0x0012CDE0", "type": "ZIP/JAR", "signature": "504B"}, {"offset": "0x001D1598", "type": "ZIP/JAR", "signature": "504B"}, {"offset": "0x0001D020", "type": "ELF", "signature": "7F454C46"}, {"offset": "0x0001D9E0", "type": "ELF", "signature": "7F454C46"}, {"offset": "0x0001FEE0", "type": "ELF", "signature": "7F454C46"}, {"offset": "0x00023B20", "type": "ELF", "signature": "7F454C46"}, {"offset": "0x00025EE0", "type": "ELF", "signature": "7F454C46"}, {"offset": "0x0002DEF8", "type": "ELF", "signature": "7F454C46"}, {"offset": "0x000426C0", "type": "ELF", "signature": "7F454C46"}, {"offset": "0x00043800", "type": "ELF", "signature": "7F454C46"}, {"offset": "0x00044E40", "type": "ELF", "signature": "7F454C46"}, {"offset": "0x00049258", "type": "ELF", "signature": "7F454C46"}, {"offset": "0x0004C2B0", "type": "ELF", "signature": "7F454C46"}, {"offset": "0x0004E5E8", "type": "ELF", "signature": "7F454C46"}, {"offset": "0x00050330", "type": "ELF", "signature": "7F454C46"}, {"offset": "0x00053C18", "type": "ELF", "signature": "7F454C46"}, {"offset": "0x00056B40", "type": "ELF", "signature": "7F454C46"}, {"offset": "0x00057C08", "type": "ELF", "signature": "7F454C46"}, {"offset": "0x0017E94E", "type": "PNG Image", "signature": "89504E47"}]}, "EFI/BOOT/BOOTIA32.EFI": {"basic_info": {"file_path": "EFI/BOOT/BOOTIA32.EFI", "file_size": 742064, "file_size_kb": 724.67, "md5": "b6410a7309a1aa9a05df02ea1954c02d", "sha1": "a9d468686be854bc40bb964c623e2368b1ce1d2e", "sha256": "f6f6b9369bcdac99fbc24c457d9010479f47af62b00fb19f1dfd865bfb75d369"}, "pe_header": {"dos_signature": "4D5A", "dos_signature_valid": true, "pe_offset": "0x00000080", "pe_signature": "50450000", "pe_signature_valid": true, "machine_type": "0x014C", "architecture": "Intel 386 (i386)"}, "strings": {"efi_protocols": [], "file_paths": ["_Xj/", "~l/t", "VPh\\"], "error_messages": [], "debug_strings": [], "certificates": [], "other_ascii": ["!This program cannot be run in DOS mode.", ".text", "`.reloc", ".data", "@.dynamic", ".rel", "@.sbat", "<[^_]", "[^_]", "[^_]", "[^_]", "[^_]", "},PP", "QQj ", "[^_]", "[^_]", "[^_]", "[^_]", "PVhb", "WVhv"], "unicode_strings": ["jjjj", "jjjh", "SHIM_DEBUG", "add-symbol-file /usr/lib/debug/usr/share/shim/ia32-15.6-1/shimia32.efi.debug 0x%08x -s .data 0x%08x", "Pausing for debugger attachment.", "To disable this, remove the EFI variable %s-%g .", "h%a:%d:%a() hexdump of a NULL pointer!", "%a:%d:%a() %08lx  %a  %a", "%a:%d:%a() trying to verify cert %d (%s)", "%a:%d:%a() cert[0:1] is [%02x%02x], should be [%02x%02x]", "%a:%d:%a() Cert length is %ld, expecting %ld", "2%a:%d:%a() AuthenticodeVerify() succeeded: %d", "AuthenticodeVerify(): %d", "Not a DER encoded x.509 Certificate", "%a:%d:%a() cert:", "check_db_hash(db, sha256hash) != DATA_FOUND", "check_db_hash(db, sha1hash) != DATA_FOUND", "MokList", "check_db_hash(<PERSON><PERSON><PERSON><PERSON>, sha256hash) != DATA_FOUND", "check_db_cert(<PERSON><PERSON><PERSON><PERSON>, sha256hash) != DATA_FOUND"]}, "entropy_analysis": {"overall_entropy": 6.222, "entropy_analysis": "Medium", "block_count": 725, "high_entropy_blocks": [{"offset": "0x0007B000", "entropy": 7.569}], "average_block_entropy": 5.059}, "embedded_files": [{"offset": "0x00000000", "type": "PE/EXE", "signature": "4D5A"}, {"offset": "0x000089CE", "type": "PE/EXE", "signature": "4D5A"}, {"offset": "0x000096DF", "type": "PE/EXE", "signature": "4D5A"}, {"offset": "0x0006C5D0", "type": "ZIP/JAR", "signature": "504B"}, {"offset": "0x0006CFFC", "type": "ZIP/JAR", "signature": "504B"}, {"offset": "0x0006E006", "type": "ZIP/JAR", "signature": "504B"}, {"offset": "0x0006E3C5", "type": "ZIP/JAR", "signature": "504B"}, {"offset": "0x0006E3D4", "type": "ZIP/JAR", "signature": "504B"}, {"offset": "0x000766B8", "type": "ZIP/JAR", "signature": "504B"}, {"offset": "0x000770BF", "type": "ZIP/JAR", "signature": "504B"}, {"offset": "0x000770D1", "type": "ZIP/JAR", "signature": "504B"}, {"offset": "0x000770E2", "type": "ZIP/JAR", "signature": "504B"}, {"offset": "0x000770F2", "type": "ZIP/JAR", "signature": "504B"}, {"offset": "0x00077108", "type": "ZIP/JAR", "signature": "504B"}, {"offset": "0x0007711F", "type": "ZIP/JAR", "signature": "504B"}, {"offset": "0x00077155", "type": "ZIP/JAR", "signature": "504B"}, {"offset": "0x00077174", "type": "ZIP/JAR", "signature": "504B"}, {"offset": "0x000771AD", "type": "ZIP/JAR", "signature": "504B"}, {"offset": "0x000771BC", "type": "ZIP/JAR", "signature": "504B"}, {"offset": "0x000771D4", "type": "ZIP/JAR", "signature": "504B"}]}}