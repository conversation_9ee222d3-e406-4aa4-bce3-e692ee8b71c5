#!/usr/bin/env python3
import struct

def locate_verification_system(filename):
    """定位BOOT.EFI中的完整验证系统"""
    
    try:
        with open(filename, 'rb') as f:
            data = f.read()
            
            print("=== 定位验证系统 ===\n")
            
            # 1. 从已知的DmarInsert和GenuineIntel位置开始分析
            dmar_pos = data.find(b"DmarInsert")
            intel_pos = data.find(b"GenuineIntel") 
            
            print(f"DmarInsert位置: 0x{dmar_pos:04X}")
            print(f"GenuineIntel位置: 0x{intel_pos:04X}")
            
            # 2. 搜索可能调用这些数据的函数
            print(f"\n寻找引用这些字符串的代码:")
            
            # 在这些字符串前面2KB范围内搜索可能的函数调用
            search_areas = [(dmar_pos, "DmarInsert"), (intel_pos, "GenuineIntel")]
            
            for str_pos, str_name in search_areas:
                print(f"\n分析 {str_name} 周围的代码:")
                
                # 向前搜索可能的函数开始
                search_start = max(0, str_pos - 2048)
                search_end = str_pos
                
                # 寻找可能引用这个字符串的LEA指令
                for i in range(search_start, search_end - 10):
                    # 检查是否有指向字符串的相对地址引用
                    for offset_size in [4]:  # 32位偏移
                        if i + offset_size < search_end:
                            potential_offset = struct.unpack('<i', data[i:i+offset_size])[0]
                            calculated_addr = i + offset_size + potential_offset
                            
                            # 如果计算地址接近字符串位置
                            if abs(calculated_addr - str_pos) < 20:
                                print(f"  可能的引用 @ 0x{i:04X}, 目标: 0x{calculated_addr:04X}")
                                
                                # 分析这个位置周围的代码
                                code_start = max(0, i - 50)
                                code_end = min(len(data), i + 100)
                                code_context = data[code_start:code_end]
                                
                                print(f"    代码上下文:")
                                for j in range(0, len(code_context), 16):
                                    offset = code_start + j
                                    line = code_context[j:j+16]
                                    hex_str = ' '.join(f'{b:02x}' for b in line)
                                    print(f"      {offset:08x}: {hex_str}")
                                
                                # 向前搜索函数开始
                                func_search_start = max(0, i - 200)
                                for k in range(i, func_search_start, -1):
                                    # 寻找函数序言
                                    if (data[k:k+3] == b'\x55\x48\x89' or  # push rbp; mov rbp, rsp
                                        data[k:k+4] == b'\x48\x83\xEC' or  # sub rsp, imm
                                        data[k:k+3] == b'\x48\x89'):       # mov [reg], reg
                                        print(f"    可能的函数开始: 0x{k:04X}")
                                        break
                                print()
            
            # 3. 搜索错误代码12的设置和检查
            print(f"\n搜索错误代码12的设置位置:")
            
            # 搜索mov eax, 12的指令
            error_12_patterns = [
                (b'\xB8\x0C\x00\x00\x00', 'mov eax, 12'),
                (b'\x48\xC7\xC0\x0C\x00\x00\x00', 'mov rax, 12'),
                (b'\xC7\x45', 'mov [rbp+offset], 12'),  # 可能在栈上设置错误码
                (b'\x89\x45', 'mov [rbp+offset], eax')   # 保存错误码到栈
            ]
            
            for pattern, description in error_12_patterns:
                positions = []
                start = 0
                while True:
                    pos = data.find(pattern, start)
                    if pos == -1:
                        break
                    
                    # 对于mov [rbp+offset]类型的指令，检查是否是12
                    if pattern == b'\xC7\x45' and pos + 6 < len(data):
                        immediate = struct.unpack('<I', data[pos+3:pos+7])[0]
                        if immediate == 12:
                            positions.append(pos)
                    elif pattern in [b'\xB8\x0C\x00\x00\x00', b'\x48\xC7\xC0\x0C\x00\x00\x00']:
                        positions.append(pos)
                    
                    start = pos + 1
                
                if positions:
                    print(f"\n{description}: {len(positions)} 次")
                    for pos in positions:
                        print(f"  0x{pos:04X}:")
                        
                        # 显示这个指令周围的代码
                        context_start = max(0, pos - 30)
                        context_end = min(len(data), pos + 30)
                        context = data[context_start:context_end]
                        
                        for j in range(0, len(context), 16):
                            offset = context_start + j
                            line = context[j:j+16]
                            hex_str = ' '.join(f'{b:02x}' for b in line)
                            print(f"    {offset:08x}: {hex_str}")
                        
                        # 搜索这个位置附近的函数
                        func_start = max(0, pos - 500)
                        for k in range(pos, func_start, -1):
                            if (data[k:k+3] == b'\x55\x48\x89' or
                                data[k:k+4] == b'\x48\x83\xEC'):
                                print(f"    所在函数可能开始于: 0x{k:04X}")
                                break
                        print()
            
            # 4. 搜索可能的验证函数特征
            print(f"\n搜索验证函数的特征模式:")
            
            # 搜索可能的比较和跳转模式
            verification_patterns = [
                (b'\x48\x85\xC0\x0F\x84', 'test rax, rax; je (验证失败跳转)'),
                (b'\x85\xC0\x0F\x84', 'test eax, eax; je (验证失败跳转)'),
                (b'\x48\x83\xF8\x00\x0F\x84', 'cmp rax, 0; je (验证失败)'),
                (b'\x83\xF8\x00\x0F\x84', 'cmp eax, 0; je (验证失败)')
            ]
            
            for pattern, description in verification_patterns:
                positions = []
                start = 0
                while True:
                    pos = data.find(pattern, start)
                    if pos == -1:
                        break
                    positions.append(pos)
                    start = pos + 1
                
                if positions:
                    print(f"\n{description}: {len(positions)} 次")
                    for pos in positions[:3]:  # 只显示前3个
                        print(f"  0x{pos:04X}: 检查返回值并跳转")
                        
                        # 分析跳转目标
                        if len(pattern) >= 5:
                            jump_offset = struct.unpack('<i', data[pos+len(pattern):pos+len(pattern)+4])[0]
                            jump_target = pos + len(pattern) + 4 + jump_offset
                            print(f"    跳转目标: 0x{jump_target:04X}")
            
            # 5. 总结可能的修改点
            print(f"\n=== 可能的修改策略 ===")
            print("1. 找到设置错误代码12的位置，改为设置成功代码0")
            print("2. 找到验证失败的跳转指令，改为无条件成功")
            print("3. 找到验证函数的入口，直接返回成功")
            print("4. 在函数开始处插入直接返回成功的代码")
            
    except FileNotFoundError:
        print(f"文件未找到: {filename}")
    except Exception as e:
        print(f"分析过程中出错: {e}")

if __name__ == "__main__":
    filename = r"D:\新建文件夹 (2)\新建文件夹\EFI\BOOT\BOOT.EFI"
    locate_verification_system(filename)