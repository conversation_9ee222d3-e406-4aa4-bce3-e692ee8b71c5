#!/usr/bin/env python3
"""
手动设置Ghidra分析环境
"""

import os
import shutil

def setup_ghidra_analysis():
    """设置Ghidra分析环境"""
    
    ghidra_path = r"D:\ghidra_11.4.1_PUBLIC"
    boot_efi_path = r"D:\新建文件夹 (2)\新建文件夹\EFI\BOOT\BOOT.EFI"
    
    print("=== 手动Ghidra分析设置 ===")
    print(f"Ghidra路径: {ghidra_path}")
    print(f"BOOT.EFI路径: {boot_efi_path}")
    
    # 检查文件
    if not os.path.exists(boot_efi_path):
        print(f"错误: BOOT.EFI文件不存在!")
        return False
    
    file_size = os.path.getsize(boot_efi_path)
    print(f"BOOT.EFI文件大小: {file_size} 字节")
    
    # 检查Ghidra
    if not os.path.exists(ghidra_path):
        print(f"错误: Ghidra路径不存在!")
        return False
    
    # 列出Ghidra目录内容
    print(f"\\nGhidra目录内容:")
    for item in os.listdir(ghidra_path):
        item_path = os.path.join(ghidra_path, item)
        if os.path.isdir(item_path):
            print(f"  [DIR]  {item}")
        else:
            print(f"  [FILE] {item}")
    
    # 检查support目录
    support_dir = os.path.join(ghidra_path, "support")
    if os.path.exists(support_dir):
        print(f"\\nSupport目录内容:")
        for item in os.listdir(support_dir):
            print(f"  {item}")
    
    # 创建简化的分析脚本
    analysis_script = """
=== BOOT.EFI 手动分析指南 ===

1. 启动Ghidra:
   - 双击运行: D:\\ghidra_11.4.1_PUBLIC\\ghidraRun.bat
   - 或者运行: D:\\ghidra_11.4.1_PUBLIC\\ghidra.exe

2. 创建新项目:
   - File -> New Project
   - 选择 Non-Shared Project
   - 项目目录: D:\\新建文件夹 (2)\\新建文件夹\\ghidra_project
   - 项目名称: BOOT_EFI_Analysis

3. 导入BOOT.EFI:
   - File -> Import File
   - 选择: D:\\新建文件夹 (2)\\新建文件夹\\EFI\\BOOT\\BOOT.EFI
   - Format: Portable Executable (PE)
   - 点击 OK

4. 分析文件:
   - 双击导入的BOOT.EFI文件
   - 在弹出的对话框中选择 "Yes" 进行自动分析
   - 等待分析完成

5. 关键分析步骤:

   a) 字符串搜索:
      - Search -> For Strings
      - 搜索关键词: "MBR", "check", "verify", "error", "DmarInsert", "General_UDisk"

   b) 函数列表:
      - Window -> Functions
      - 查看所有识别的函数

   c) 入口点分析:
      - 在Symbol Tree中找到 "entry" 函数
      - 双击查看反编译代码

   d) 内存映射:
      - Window -> Memory Map
      - 查看内存布局

   e) 数据类型:
      - Window -> Data Type Manager
      - 查看结构体定义

6. 重点分析目标:

   a) 硬件验证函数:
      - 搜索包含 "4C35A0E2" 或类似硬件ID的函数
      - 查找错误代码12的设置位置

   b) VTD模拟功能:
      - 搜索 "DmarInsert", "DMAR", "ACPI" 相关代码
      - 分析ACPI表操作函数

   c) 启动流程:
      - 从entry函数开始，追踪主要的执行路径
      - 识别初始化、验证、VTD设置的顺序

7. 导出分析结果:
   - File -> Export Program
   - 选择导出格式和位置

请按照以上步骤手动进行分析，这将比自动化脚本提供更详细的控制和结果。
"""
    
    # 保存分析指南
    guide_path = r"D:\新建文件夹 (2)\新建文件夹\ghidra_analysis_guide.txt"
    with open(guide_path, 'w', encoding='utf-8') as f:
        f.write(analysis_script)
    
    print(f"\\n已创建分析指南: {guide_path}")
    
    # 创建批处理文件来启动Ghidra
    bat_content = f"""@echo off
echo Starting Ghidra for BOOT.EFI analysis...
cd /d "{ghidra_path}"
if exist ghidraRun.bat (
    call ghidraRun.bat
) else if exist ghidra.exe (
    start ghidra.exe
) else (
    echo Ghidra executable not found!
    pause
)
"""
    
    bat_path = r"D:\新建文件夹 (2)\新建文件夹\start_ghidra.bat"
    with open(bat_path, 'w', encoding='utf-8') as f:
        f.write(bat_content)
    
    print(f"已创建Ghidra启动脚本: {bat_path}")
    
    return True

def create_alternative_analysis():
    """创建备用的二进制分析"""
    
    print("\\n=== 创建备用分析工具 ===")
    
    # 创建详细的十六进制分析工具
    hex_analyzer = """#!/usr/bin/env python3
import struct
import re

def detailed_boot_efi_analysis():
    filename = r"D:\\新建文件夹 (2)\\新建文件夹\\EFI\\BOOT\\BOOT.EFI"
    
    with open(filename, 'rb') as f:
        data = f.read()
    
    print("=== BOOT.EFI 详细二进制分析 ===")
    print(f"文件大小: {len(data)} 字节\\n")
    
    # 1. PE文件头详细分析
    print("1. PE文件头分析:")
    dos_header = struct.unpack('<H', data[0:2])[0]
    print(f"  DOS签名: 0x{dos_header:04X}")
    
    pe_offset = struct.unpack('<I', data[0x3C:0x40])[0]
    print(f"  PE头偏移: 0x{pe_offset:04X}")
    
    if pe_offset < len(data) - 24:
        pe_signature = data[pe_offset:pe_offset+4]
        print(f"  PE签名: {pe_signature}")
        
        machine = struct.unpack('<H', data[pe_offset+4:pe_offset+6])[0]
        sections = struct.unpack('<H', data[pe_offset+6:pe_offset+8])[0]
        timestamp = struct.unpack('<I', data[pe_offset+8:pe_offset+12])[0]
        
        print(f"  机器类型: 0x{machine:04X}")
        print(f"  节数量: {sections}")
        print(f"  时间戳: 0x{timestamp:08X}")
    
    # 2. 搜索所有ASCII字符串
    print("\\n2. ASCII字符串 (长度>=6):")
    strings = []
    current = ""
    start_pos = 0
    
    for i, byte in enumerate(data):
        if 32 <= byte <= 126:
            if not current:
                start_pos = i
            current += chr(byte)
        else:
            if len(current) >= 6:
                strings.append((start_pos, current))
            current = ""
    
    if len(current) >= 6:
        strings.append((len(data) - len(current), current))
    
    print(f"  找到 {len(strings)} 个字符串:")
    for offset, string in strings[:50]:
        print(f"    0x{offset:08X}: {string}")
    
    # 3. 搜索特定模式
    print("\\n3. 验证相关模式搜索:")
    patterns = {
        b"MBR": "MBR相关",
        b"check": "检查功能", 
        b"verify": "验证功能",
        b"error": "错误处理",
        b"device": "设备相关",
        b"DmarInsert": "VTD功能",
        b"DMAR": "DMAR表",
        b"ACPI": "ACPI表",
        b"General_UDisk": "硬件标识"
    }
    
    for pattern, desc in patterns.items():
        positions = []
        start = 0
        while True:
            pos = data.find(pattern, start)
            if pos == -1:
                break
            positions.append(pos)
            start = pos + 1
        
        if positions:
            print(f"  {desc} ('{pattern.decode('ascii', errors='ignore')}'): {len(positions)} 次")
            for pos in positions[:3]:
                context = data[max(0, pos-20):pos+len(pattern)+20]
                ascii_context = ''.join(chr(b) if 32 <= b <= 126 else '.' for b in context)
                print(f"    0x{pos:08X}: {ascii_context}")
    
    # 4. 搜索call和jmp指令
    print("\\n4. 函数调用分析:")
    calls = []
    
    for i in range(len(data) - 5):
        if data[i] == 0xE8:  # call near
            offset = struct.unpack('<i', data[i+1:i+5])[0]
            target = (i + 5 + offset) & 0xFFFFFFFF
            calls.append(('call', i, target))
        elif data[i] == 0xE9:  # jmp near
            offset = struct.unpack('<i', data[i+1:i+5])[0]
            target = (i + 5 + offset) & 0xFFFFFFFF
            calls.append(('jmp', i, target))
    
    print(f"  找到 {len(calls)} 个call/jmp指令")
    print("  前20个:")
    for instr, addr, target in calls[:20]:
        print(f"    0x{addr:08X}: {instr} -> 0x{target:08X}")
    
    # 5. 搜索错误代码12
    print("\\n5. 错误代码12分析:")
    error_patterns = [
        (b'\\xB8\\x0C\\x00\\x00\\x00', 'mov eax, 12'),
        (b'\\x48\\xC7\\xC0\\x0C\\x00\\x00\\x00', 'mov rax, 12'),
        (b'\\x83\\xF8\\x0C', 'cmp eax, 12'),
        (b'\\x48\\x83\\xF8\\x0C', 'cmp rax, 12')
    ]
    
    for pattern, desc in error_patterns:
        positions = []
        start = 0
        while True:
            pos = data.find(pattern, start)
            if pos == -1:
                break
            positions.append(pos)
            start = pos + 1
        
        if positions:
            print(f"  {desc}: {len(positions)} 次")
            for pos in positions:
                context = data[max(0, pos-15):pos+len(pattern)+15]
                hex_str = ' '.join(f'{b:02x}' for b in context)
                print(f"    0x{pos:08X}: {hex_str}")
    
    print("\\n=== 分析完成 ===")

if __name__ == "__main__":
    detailed_boot_efi_analysis()
"""
    
    analyzer_path = r"D:\新建文件夹 (2)\新建文件夹\detailed_boot_analysis.py"
    with open(analyzer_path, 'w', encoding='utf-8') as f:
        f.write(hex_analyzer)
    
    print(f"已创建详细分析工具: {analyzer_path}")
    
    return analyzer_path

if __name__ == "__main__":
    setup_success = setup_ghidra_analysis()
    analyzer_path = create_alternative_analysis()
    
    if setup_success:
        print("\\n=== 设置完成 ===")
        print("\\n建议的分析步骤:")
        print("1. 运行 start_ghidra.bat 启动Ghidra进行GUI分析")
        print("2. 运行 detailed_boot_analysis.py 进行二进制分析")
        print("3. 参考 ghidra_analysis_guide.txt 进行手动分析")
    else:
        print("\\n=== 设置失败 ===")