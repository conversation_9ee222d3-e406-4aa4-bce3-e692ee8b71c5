#!/usr/bin/env python3
"""
专业EFI文件反编译工具
Professional EFI File Decompiler
使用capstone反汇编引擎
"""

import os
import sys
import struct
import hashlib
import json
import re
from pathlib import Path
from typing import Dict, List, Any, Optional, Tuple

# 尝试导入capstone，如果没有则使用内置反汇编器
try:
    import capstone
    HAS_CAPSTONE = True
except ImportError:
    HAS_CAPSTONE = False
    print("警告: 未安装capstone库，将使用内置反汇编器")
    print("建议安装: pip install capstone")

class ProfessionalEFIDecompiler:
    """专业EFI文件反编译器"""
    
    def __init__(self, file_path: str):
        self.file_path = file_path
        self.file_data = None
        self.pe_info = {}
        self.sections = []
        
    def load_file(self) -> bool:
        """加载EFI文件"""
        try:
            with open(self.file_path, 'rb') as f:
                self.file_data = f.read()
            return True
        except Exception as e:
            print(f"Error loading file {self.file_path}: {e}")
            return False
    
    def parse_pe_header_detailed(self) -> Dict[str, Any]:
        """详细解析PE头"""
        if not self.file_data or len(self.file_data) < 64:
            return {"error": "File too small"}
        
        try:
            # DOS头
            dos_header = struct.unpack('<HHHHHHHHHHHHHHHH', self.file_data[:32])
            if dos_header[0] != 0x5A4D:  # MZ
                return {"error": "Invalid DOS signature"}
            
            pe_offset = struct.unpack('<L', self.file_data[60:64])[0]
            
            # PE签名
            if pe_offset + 4 > len(self.file_data):
                return {"error": "Invalid PE offset"}
            
            pe_sig = struct.unpack('<L', self.file_data[pe_offset:pe_offset+4])[0]
            if pe_sig != 0x00004550:  # PE\0\0
                return {"error": "Invalid PE signature"}
            
            # COFF头
            coff_offset = pe_offset + 4
            if coff_offset + 20 > len(self.file_data):
                return {"error": "COFF header too small"}
            
            coff_data = struct.unpack('<HHLLHH', self.file_data[coff_offset:coff_offset+18])
            characteristics = struct.unpack('<H', self.file_data[coff_offset+18:coff_offset+20])[0]
            
            machine, num_sections, timestamp, ptr_symtab, num_symbols, opt_hdr_size = coff_data
            
            # 可选头
            opt_hdr_offset = coff_offset + 20
            if opt_hdr_offset + 2 > len(self.file_data):
                return {"error": "Optional header too small"}
            
            magic = struct.unpack('<H', self.file_data[opt_hdr_offset:opt_hdr_offset+2])[0]
            
            pe_info = {
                "dos_header": {
                    "signature": "MZ",
                    "pe_offset": pe_offset
                },
                "coff_header": {
                    "machine": machine,
                    "machine_name": self._get_machine_name(machine),
                    "num_sections": num_sections,
                    "timestamp": timestamp,
                    "characteristics": characteristics
                },
                "optional_header": {
                    "magic": magic,
                    "pe_type": "PE32+" if magic == 0x20b else "PE32" if magic == 0x10b else "Unknown"
                }
            }
            
            # 解析可选头的更多字段
            if magic == 0x20b:  # PE32+
                if opt_hdr_offset + 24 <= len(self.file_data):
                    opt_data = struct.unpack('<BBLLLL', self.file_data[opt_hdr_offset+2:opt_hdr_offset+18])
                    major_linker, minor_linker, size_code, size_init_data, size_uninit_data, entry_point = opt_data
                    
                    pe_info["optional_header"].update({
                        "linker_version": f"{major_linker}.{minor_linker}",
                        "size_of_code": size_code,
                        "size_of_initialized_data": size_init_data,
                        "size_of_uninitialized_data": size_uninit_data,
                        "entry_point": entry_point,
                        "entry_point_hex": f"0x{entry_point:08x}"
                    })
                
                # 读取更多PE32+字段
                if opt_hdr_offset + 88 <= len(self.file_data):
                    image_base = struct.unpack('<Q', self.file_data[opt_hdr_offset+24:opt_hdr_offset+32])[0]
                    section_align = struct.unpack('<L', self.file_data[opt_hdr_offset+32:opt_hdr_offset+36])[0]
                    file_align = struct.unpack('<L', self.file_data[opt_hdr_offset+36:opt_hdr_offset+40])[0]
                    
                    pe_info["optional_header"].update({
                        "image_base": image_base,
                        "image_base_hex": f"0x{image_base:016x}",
                        "section_alignment": section_align,
                        "file_alignment": file_align
                    })
            
            self.pe_info = pe_info
            return pe_info
            
        except struct.error as e:
            return {"error": f"PE parsing error: {e}"}
    
    def _get_machine_name(self, machine: int) -> str:
        """获取机器类型名称"""
        machines = {
            0x014c: "Intel 386",
            0x8664: "AMD64 (x86-64)",
            0xaa64: "ARM64 (AArch64)",
            0x01c0: "ARM (32-bit)",
            0x0200: "Intel IA64"
        }
        return machines.get(machine, f"Unknown (0x{machine:04x})")
    
    def parse_sections_detailed(self) -> List[Dict[str, Any]]:
        """详细解析节表"""
        if not self.pe_info:
            self.parse_pe_header_detailed()
        
        if "error" in self.pe_info:
            return []
        
        try:
            pe_offset = self.pe_info["dos_header"]["pe_offset"]
            opt_hdr_size = self.pe_info["coff_header"].get("opt_header_size", 0)
            num_sections = self.pe_info["coff_header"]["num_sections"]
            
            # 计算节表偏移
            section_table_offset = pe_offset + 24 + opt_hdr_size
            sections = []
            
            for i in range(min(num_sections, 20)):
                section_offset = section_table_offset + i * 40
                
                if section_offset + 40 > len(self.file_data):
                    break
                
                # 读取节头
                section_data = self.file_data[section_offset:section_offset+40]
                name = section_data[:8].rstrip(b'\x00').decode('ascii', errors='ignore')
                
                # 解析节头字段
                virtual_size, virtual_addr, raw_size, raw_addr = struct.unpack('<LLLL', section_data[8:24])
                ptr_relocs, ptr_line_nums, num_relocs, num_line_nums = struct.unpack('<LLHH', section_data[24:36])
                characteristics = struct.unpack('<L', section_data[36:40])[0]
                
                section_info = {
                    "name": name,
                    "virtual_size": virtual_size,
                    "virtual_address": virtual_addr,
                    "virtual_address_hex": f"0x{virtual_addr:08x}",
                    "raw_size": raw_size,
                    "raw_address": raw_addr,
                    "raw_address_hex": f"0x{raw_addr:08x}",
                    "characteristics": characteristics,
                    "characteristics_hex": f"0x{characteristics:08x}",
                    "flags": self._parse_section_flags(characteristics)
                }
                
                # 检查是否是代码节
                section_info["is_executable"] = bool(characteristics & 0x20000000)
                section_info["is_readable"] = bool(characteristics & 0x40000000)
                section_info["is_writable"] = bool(characteristics & 0x80000000)
                
                sections.append(section_info)
            
            self.sections = sections
            return sections
            
        except (struct.error, UnicodeDecodeError) as e:
            return [{"error": f"Section parsing error: {e}"}]
    
    def _parse_section_flags(self, characteristics: int) -> List[str]:
        """解析节特征标志"""
        flags = []
        flag_map = {
            0x00000020: "CODE",
            0x00000040: "INITIALIZED_DATA",
            0x00000080: "UNINITIALIZED_DATA",
            0x02000000: "DISCARDABLE",
            0x04000000: "NOT_CACHED",
            0x08000000: "NOT_PAGED",
            0x10000000: "SHARED",
            0x20000000: "EXECUTE",
            0x40000000: "READ",
            0x80000000: "WRITE"
        }
        
        for flag, name in flag_map.items():
            if characteristics & flag:
                flags.append(name)
        
        return flags
    
    def disassemble_with_capstone(self, code_data: bytes, base_addr: int = 0) -> List[Dict[str, Any]]:
        """使用Capstone进行反汇编"""
        if not HAS_CAPSTONE:
            return self.disassemble_builtin(code_data, base_addr)
        
        try:
            # 创建Capstone反汇编器
            md = capstone.Cs(capstone.CS_ARCH_X86, capstone.CS_MODE_64)
            md.detail = True
            
            instructions = []
            
            for insn in md.disasm(code_data, base_addr):
                instr_info = {
                    "address": f"0x{insn.address:08x}",
                    "bytes": " ".join([f"{b:02x}" for b in insn.bytes]),
                    "mnemonic": insn.mnemonic,
                    "operands": insn.op_str,
                    "size": insn.size,
                    "id": insn.id,
                    "groups": [md.group_name(g) for g in insn.groups],
                    "type": "instruction"
                }
                
                # 分析指令类型
                if insn.group(capstone.CS_GRP_CALL):
                    instr_info["type"] = "call"
                elif insn.group(capstone.CS_GRP_JUMP):
                    instr_info["type"] = "jump"
                elif insn.group(capstone.CS_GRP_RET):
                    instr_info["type"] = "return"
                elif insn.group(capstone.CS_GRP_INT):
                    instr_info["type"] = "interrupt"
                
                instructions.append(instr_info)
                
                if len(instructions) >= 2000:  # 限制指令数量
                    break
            
            return instructions
            
        except Exception as e:
            print(f"Capstone反汇编错误: {e}")
            return self.disassemble_builtin(code_data, base_addr)
    
    def disassemble_builtin(self, code_data: bytes, base_addr: int = 0) -> List[Dict[str, Any]]:
        """内置反汇编器（简化版本）"""
        instructions = []
        offset = 0
        
        while offset < len(code_data) - 1:
            try:
                byte = code_data[offset]
                
                # 简单的指令识别
                if byte == 0x55:  # push rbp
                    instructions.append({
                        "address": f"0x{base_addr + offset:08x}",
                        "bytes": f"{byte:02x}",
                        "mnemonic": "push",
                        "operands": "rbp",
                        "size": 1,
                        "type": "instruction"
                    })
                elif byte == 0x5D:  # pop rbp
                    instructions.append({
                        "address": f"0x{base_addr + offset:08x}",
                        "bytes": f"{byte:02x}",
                        "mnemonic": "pop",
                        "operands": "rbp",
                        "size": 1,
                        "type": "instruction"
                    })
                elif byte == 0xC3:  # ret
                    instructions.append({
                        "address": f"0x{base_addr + offset:08x}",
                        "bytes": f"{byte:02x}",
                        "mnemonic": "ret",
                        "operands": "",
                        "size": 1,
                        "type": "return"
                    })
                elif byte == 0xE8 and offset + 4 < len(code_data):  # call rel32
                    rel_offset = struct.unpack('<l', code_data[offset+1:offset+5])[0]
                    target = base_addr + offset + 5 + rel_offset
                    instructions.append({
                        "address": f"0x{base_addr + offset:08x}",
                        "bytes": " ".join([f"{b:02x}" for b in code_data[offset:offset+5]]),
                        "mnemonic": "call",
                        "operands": f"0x{target:08x}",
                        "size": 5,
                        "type": "call",
                        "target": target
                    })
                    offset += 4  # 额外跳过4字节
                else:
                    # 未识别的字节
                    instructions.append({
                        "address": f"0x{base_addr + offset:08x}",
                        "bytes": f"{byte:02x}",
                        "mnemonic": "db",
                        "operands": f"0x{byte:02x}",
                        "size": 1,
                        "type": "data"
                    })
                
                offset += 1
                
                if len(instructions) >= 1000:  # 限制指令数量
                    break
                    
            except Exception as e:
                offset += 1
                continue
        
        return instructions
    
    def find_entry_point(self) -> Optional[int]:
        """查找入口点"""
        if not self.pe_info:
            self.parse_pe_header_detailed()
        
        entry_point = self.pe_info.get("optional_header", {}).get("entry_point")
        if entry_point:
            return entry_point
        
        return None
    
    def generate_advanced_c_code(self, sections_analysis: Dict[str, Any]) -> str:
        """生成高级C代码"""
        c_lines = []
        
        # 文件头注释
        c_lines.extend([
            "/*",
            f" * Decompiled EFI Boot Application",
            f" * Source: {self.file_path}",
            f" * Architecture: {self.pe_info.get('coff_header', {}).get('machine_name', 'Unknown')}",
            f" * Entry Point: {self.pe_info.get('optional_header', {}).get('entry_point_hex', 'Unknown')}",
            " * ",
            " * This is pseudocode generated by Professional EFI Decompiler",
            " * Manual review and modification required for compilation",
            " */",
            "",
            "#include <Uefi.h>",
            "#include <Library/UefiLib.h>",
            "#include <Library/UefiBootServicesTableLib.h>",
            "#include <Library/UefiRuntimeServicesTableLib.h>",
            "#include <Library/MemoryAllocationLib.h>",
            "#include <Library/DevicePathLib.h>",
            "#include <Library/PrintLib.h>",
            "",
            "// Global variables and structures",
            "EFI_HANDLE gImageHandle;",
            "EFI_SYSTEM_TABLE *gSystemTable;",
            "",
            "// Function prototypes",
        ])
        
        # 生成函数原型
        function_count = 0
        for section_name, section_data in sections_analysis.items():
            if section_data.get("is_executable", False):
                instructions = section_data.get("instructions", [])
                functions = self._identify_functions(instructions)
                
                for func in functions[:10]:  # 限制函数数量
                    func_name = f"Function_{func['start_address'].replace('0x', '')}"
                    c_lines.append(f"EFI_STATUS {func_name}(VOID);")
                    function_count += 1
        
        c_lines.extend([
            "",
            "// Function implementations",
            ""
        ])
        
        # 生成函数实现
        impl_count = 0
        for section_name, section_data in sections_analysis.items():
            if section_data.get("is_executable", False) and impl_count < 5:
                instructions = section_data.get("instructions", [])
                functions = self._identify_functions(instructions)
                
                for func in functions[:3]:  # 限制实现数量
                    func_name = f"Function_{func['start_address'].replace('0x', '')}"
                    
                    c_lines.extend([
                        f"/**",
                        f" * Function at {func['start_address']}",
                        f" * Size: {func.get('size', 'Unknown')} instructions",
                        f" * Section: {section_name}",
                        f" */",
                        f"EFI_STATUS {func_name}(VOID) {{",
                        "    EFI_STATUS Status = EFI_SUCCESS;",
                        ""
                    ])
                    
                    # 分析函数内容
                    for instr in func.get("instructions", [])[:20]:
                        comment = f"    // {instr['address']}: {instr['mnemonic']} {instr['operands']}"
                        c_lines.append(comment)
                        
                        # 生成对应的C代码
                        if instr["type"] == "call":
                            c_lines.append("    // Function call - implementation needed")
                        elif instr["type"] == "return":
                            c_lines.append("    return Status;")
                        elif instr["mnemonic"] == "push" and "rbp" in instr["operands"]:
                            c_lines.append("    // Function prologue")
                        elif instr["mnemonic"] == "pop" and "rbp" in instr["operands"]:
                            c_lines.append("    // Function epilogue")
                        else:
                            c_lines.append(f"    // {instr['mnemonic']} operation")
                    
                    c_lines.extend([
                        "",
                        "    return Status;",
                        "}",
                        ""
                    ])
                    
                    impl_count += 1
                    if impl_count >= 5:
                        break
        
        # 主入口点
        entry_point = self.find_entry_point()
        c_lines.extend([
            "/**",
            " * EFI Application Entry Point",
            f" * Original Entry Point: {f'0x{entry_point:08x}' if entry_point else 'Unknown'}",
            " */",
            "EFI_STATUS",
            "EFIAPI",
            "UefiMain (",
            "  IN EFI_HANDLE        ImageHandle,",
            "  IN EFI_SYSTEM_TABLE  *SystemTable",
            "  ) {",
            "    EFI_STATUS Status;",
            "",
            "    // Store global variables",
            "    gImageHandle = ImageHandle;",
            "    gSystemTable = SystemTable;",
            "",
            "    // Initialize UEFI Library",
            "    Status = UefiBootServicesTableLib->HandleProtocol(",
            "               ImageHandle,",
            "               &gEfiLoadedImageProtocolGuid,",
            "               (VOID**)&LoadedImage",
            "               );",
            "    if (EFI_ERROR(Status)) {",
            "        return Status;",
            "    }",
            "",
            "    // Call main application logic",
            f"    Status = Function_{entry_point:08x}();" if entry_point else "    // Main function call needed",
            "",
            "    return Status;",
            "}"
        ])
        
        return "\n".join(c_lines)
    
    def _identify_functions(self, instructions: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """识别函数"""
        functions = []
        current_function = None
        
        for instr in instructions:
            # 函数开始：push rbp
            if instr["mnemonic"] == "push" and "rbp" in instr["operands"]:
                if current_function:
                    functions.append(current_function)
                
                current_function = {
                    "start_address": instr["address"],
                    "instructions": [],
                    "calls": 0,
                    "returns": 0
                }
            
            if current_function:
                current_function["instructions"].append(instr)
                
                if instr["type"] == "call":
                    current_function["calls"] += 1
                elif instr["type"] == "return":
                    current_function["returns"] += 1
                    current_function["end_address"] = instr["address"]
                    current_function["size"] = len(current_function["instructions"])
                    functions.append(current_function)
                    current_function = None
        
        return functions
    
    def full_professional_analysis(self) -> Dict[str, Any]:
        """完整专业分析"""
        if not self.load_file():
            return {"error": "Failed to load file"}
        
        print(f"开始专业分析: {self.file_path}")
        
        # 解析PE结构
        pe_info = self.parse_pe_header_detailed()
        if "error" in pe_info:
            return pe_info
        
        # 解析节
        sections = self.parse_sections_detailed()
        
        # 分析每个节
        sections_analysis = {}
        total_instructions = 0
        
        for section in sections:
            if section.get("is_executable", False):
                print(f"反汇编可执行节: {section['name']}")
                
                raw_addr = section["raw_address"]
                raw_size = section["raw_size"]
                virtual_addr = section["virtual_address"]
                
                if raw_addr + raw_size <= len(self.file_data):
                    code_data = self.file_data[raw_addr:raw_addr + raw_size]
                    
                    # 使用Capstone或内置反汇编器
                    instructions = self.disassemble_with_capstone(code_data, virtual_addr)
                    
                    sections_analysis[section["name"]] = {
                        **section,
                        "instructions": instructions,
                        "instruction_count": len(instructions)
                    }
                    
                    total_instructions += len(instructions)
        
        # 生成C代码
        print("生成专业C代码...")
        c_code = self.generate_advanced_c_code(sections_analysis)
        
        return {
            "file_path": self.file_path,
            "file_size": len(self.file_data),
            "pe_info": pe_info,
            "sections": sections,
            "sections_analysis": sections_analysis,
            "c_code": c_code,
            "statistics": {
                "total_sections": len(sections),
                "executable_sections": len(sections_analysis),
                "total_instructions": total_instructions,
                "capstone_available": HAS_CAPSTONE
            }
        }

def main():
    """主函数"""
    print("=== 专业EFI文件反编译工具 ===")
    print("=== Professional EFI File Decompiler ===\n")
    
    if HAS_CAPSTONE:
        print("✅ 使用Capstone反汇编引擎")
    else:
        print("⚠️  使用内置反汇编引擎（功能有限）")
    
    target_file = "EFI/BOOT/BOOTX64.EFI"
    
    if not os.path.exists(target_file):
        print(f"文件不存在: {target_file}")
        return
    
    decompiler = ProfessionalEFIDecompiler(target_file)
    result = decompiler.full_professional_analysis()
    
    if "error" in result:
        print(f"分析失败: {result['error']}")
        return
    
    # 保存结果
    base_name = target_file.replace('/', '_').replace('.', '_')
    
    # 保存C代码
    c_file = f"{base_name}_professional.c"
    with open(c_file, 'w', encoding='utf-8') as f:
        f.write(result["c_code"])
    
    # 保存分析报告
    report_file = f"{base_name}_analysis_report.json"
    with open(report_file, 'w', encoding='utf-8') as f:
        # 移除C代码以减小文件大小
        result_copy = result.copy()
        result_copy.pop('c_code', None)
        json.dump(result_copy, f, indent=2, ensure_ascii=False)
    
    print(f"\n专业分析完成!")
    print(f"统计信息:")
    print(f"  - 文件大小: {result['file_size']:,} 字节")
    print(f"  - 总节数: {result['statistics']['total_sections']}")
    print(f"  - 可执行节: {result['statistics']['executable_sections']}")
    print(f"  - 总指令数: {result['statistics']['total_instructions']}")
    print(f"  - 入口点: {result['pe_info'].get('optional_header', {}).get('entry_point_hex', 'Unknown')}")
    print(f"\n输出文件:")
    print(f"  - C源代码: {c_file}")
    print(f"  - 分析报告: {report_file}")

if __name__ == "__main__":
    main()
