@echo off
echo Starting Ghidra Analysis of BOOT.EFI...
set GHIDRA_HOME=D:\ghidra_11.4.1_PUBLIC
set PROJECT_DIR=D:\新建文件夹 (2)\新建文件夹\ghidra_project
set BOOT_EFI=D:\新建文件夹 (2)\新建文件夹\EFI\BOOT\BOOT.EFI

echo GHIDRA_HOME: %GHIDRA_HOME%
echo PROJECT_DIR: %PROJECT_DIR%
echo BOOT_EFI: %BOOT_EFI%

if not exist "%PROJECT_DIR%" mkdir "%PROJECT_DIR%"

echo Calling Ghidra analyzeHeadless...
"%GHIDRA_HOME%\support\analyzeHeadless.bat" "%PROJECT_DIR%" "BOOT_EFI_Analysis" -import "%BOOT_EFI%" -analysisTimeoutPerFile 120

echo Analysis complete.
pause