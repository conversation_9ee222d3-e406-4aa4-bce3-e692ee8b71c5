#!/usr/bin/env python3

def analyze_potential_acpi_table(data, pos):
    """分析潜在的ACPI表"""
    if pos + 36 > len(data):
        return False
    
    # 检查是否为有效的ACPI表头
    signature = data[pos:pos+4]
    length = int.from_bytes(data[pos+4:pos+8], byteorder='little')
    
    # ACPI表长度应该合理
    if length < 36 or length > 65536:
        return False
        
    # 如果表长度超出文件范围
    if pos + length > len(data):
        return False
        
    revision = data[pos+8]
    checksum = data[pos+9]
    oemid = data[pos+10:pos+16]
    oemtableid = data[pos+16:pos+24]
    
    print(f"Potential ACPI table at 0x{pos:x}:")
    print(f"  Signature: {signature}")
    print(f"  Length: {length}")
    print(f"  Revision: {revision}")
    print(f"  Checksum: 0x{checksum:02x}")
    print(f"  OEM ID: {oemid}")
    print(f"  OEM Table ID: {oemtableid}")
    
    # 显示完整表数据
    table_data = data[pos:pos+length]
    print(f"Complete table data:")
    for i in range(0, min(length, 256), 16):
        hex_bytes = ' '.join(f'{b:02x}' for b in table_data[i:i+16])
        ascii_repr = ''.join(chr(b) if 32 <= b <= 126 else '.' for b in table_data[i:i+16])
        print(f'{pos+i:08x}: {hex_bytes:<48} {ascii_repr}')
    print()
    
    return True

with open('EFI/BOOT/BOOT.EFI', 'rb') as f:
    data = f.read()

print(f"File size: {len(data)} bytes")
print()

# 搜索所有可能的DMAR表
# 方法1：搜索"DMAR"字符串
print("=== Searching for 'DMAR' strings ===")
dmar_positions = []
pos = 0
while True:
    pos = data.find(b'DMAR', pos)
    if pos == -1:
        break
    dmar_positions.append(pos)
    print(f"Found 'DMAR' at offset 0x{pos:x}")
    pos += 1

print()

# 方法2：搜索小端序的"RAMD"
print("=== Searching for little-endian 'RAMD' ===")
ramd_positions = []
pos = 0
while True:
    pos = data.find(b'RAMD', pos)
    if pos == -1:
        break
    ramd_positions.append(pos)
    print(f"Found 'RAMD' (little-endian) at offset 0x{pos:x}")
    pos += 1

print()

# 分析每个找到的位置
print("=== Analyzing potential DMAR tables ===")
all_positions = set(dmar_positions + ramd_positions)
for pos in sorted(all_positions):
    print(f"\nAnalyzing position 0x{pos:x}:")
    
    # 检查前后不同的对齐位置
    for offset in [-16, -8, -4, 0, 4, 8, 16]:
        check_pos = pos + offset
        if check_pos >= 0 and check_pos < len(data):
            if analyze_potential_acpi_table(data, check_pos):
                break
    else:
        # 如果没有找到有效的ACPI表，显示原始数据
        print(f"Raw data around 0x{pos:x}:")
        start = max(0, pos - 32)
        end = min(len(data), pos + 64)
        raw_data = data[start:end]
        for i in range(0, len(raw_data), 16):
            hex_bytes = ' '.join(f'{b:02x}' for b in raw_data[i:i+16])
            ascii_repr = ''.join(chr(b) if 32 <= b <= 126 else '.' for b in raw_data[i:i+16])
            print(f'{start+i:08x}: {hex_bytes:<48} {ascii_repr}')

print("\nAnalysis complete.")