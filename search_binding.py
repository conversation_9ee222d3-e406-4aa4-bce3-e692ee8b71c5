#!/usr/bin/env python3
import struct

def search_binary_patterns(filename):
    """搜索BOOT.EFI中的硬件绑定标识符"""
    
    # 目标搜索模式
    mbr_signature = 0x4C35A0E2
    mbr_little_endian = struct.pack('<I', mbr_signature)  # 小端序
    mbr_big_endian = struct.pack('>I', mbr_signature)     # 大端序
    
    # GUID: 33125640-96BD-44FE-B53F-644C9F6D6924
    # GUID在二进制中的表示 (小端序格式)
    guid_part1 = struct.pack('<I', 0x33125640)  # 40 56 12 33
    guid_part2 = struct.pack('<H', 0x96BD)      # BD 96  
    guid_part3 = struct.pack('<H', 0x44FE)      # FE 44
    guid_part4 = b'\xB5\x3F\x64\x4C\x9F\x6D\x69\x24'  # 剩余8字节
    
    print("搜索模式:")
    print(f"MBR签名 (小端): {mbr_little_endian.hex().upper()}")
    print(f"MBR签名 (大端): {mbr_big_endian.hex().upper()}")
    print(f"GUID部分1: {guid_part1.hex().upper()}")
    print(f"GUID部分2: {guid_part2.hex().upper()}")
    print(f"GUID部分3: {guid_part3.hex().upper()}")
    print(f"GUID部分4: {guid_part4.hex().upper()}")
    print("\n开始搜索...\n")
    
    try:
        with open(filename, 'rb') as f:
            data = f.read()
            
            # 搜索MBR签名
            pos = data.find(mbr_little_endian)
            if pos != -1:
                print(f"找到MBR签名 (小端) 位置: 0x{pos:04X}")
                print(f"上下文: {data[pos-20:pos+20].hex().upper()}")
                print()
            
            pos = data.find(mbr_big_endian)
            if pos != -1:
                print(f"找到MBR签名 (大端) 位置: 0x{pos:04X}")
                print(f"上下文: {data[pos-20:pos+20].hex().upper()}")
                print()
            
            # 搜索GUID各部分
            pos = data.find(guid_part1)
            if pos != -1:
                print(f"找到GUID部分1 位置: 0x{pos:04X}")
                print(f"上下文: {data[pos-20:pos+20].hex().upper()}")
                print()
                
            pos = data.find(guid_part2)
            if pos != -1:
                print(f"找到GUID部分2 位置: 0x{pos:04X}")
                print(f"上下文: {data[pos-10:pos+10].hex().upper()}")
                print()
            
            # 搜索特定的4字节模式组合
            target_4bytes = b'\x4C\x35\xA0\xE2'
            target_4bytes_rev = b'\xE2\xA0\x35\x4C'
            
            pos = data.find(target_4bytes)
            if pos != -1:
                print(f"找到4字节模式 (原序) 位置: 0x{pos:04X}")
                print(f"上下文: {data[pos-20:pos+20].hex().upper()}")
                print()
                
            pos = data.find(target_4bytes_rev) 
            if pos != -1:
                print(f"找到4字节模式 (反序) 位置: 0x{pos:04X}")
                print(f"上下文: {data[pos-20:pos+20].hex().upper()}")
                print()
            
            # 搜索可能的比较指令附近的常量
            # 寻找4字节对齐的常量区域
            for i in range(0, len(data)-4, 4):
                chunk = data[i:i+4]
                if chunk == mbr_little_endian or chunk == mbr_big_endian:
                    print(f"在对齐位置找到MBR签名: 0x{i:04X}")
                    print(f"上下文: {data[i-20:i+24].hex().upper()}")
                    print()
                    
            print("搜索完成")
            
    except FileNotFoundError:
        print(f"文件未找到: {filename}")
    except Exception as e:
        print(f"搜索过程中出错: {e}")

if __name__ == "__main__":
    filename = r"D:\新建文件夹 (2)\新建文件夹\EFI\BOOT\BOOT.EFI"
    search_binary_patterns(filename)