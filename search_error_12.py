#!/usr/bin/env python3

def search_error_messages(filename):
    """搜索错误消息和错误代码12"""
    
    try:
        with open(filename, 'rb') as f:
            data = f.read()
            
            print("搜索具体的错误消息...\n")
            
            # 搜索错误消息模式
            error_patterns = [
                b"NOT a standard",
                b"standard Ventoy device", 
                b"NOT supported",
                b"MBR check failed",
                b"WARNING",
                b"Error message",
                b"(12)",
                b"12"
            ]
            
            for pattern in error_patterns:
                pos = data.find(pattern)
                if pos != -1:
                    print(f"找到 '{pattern.decode('utf-8', errors='ignore')}' 位置: 0x{pos:04X}")
                    start = max(0, pos - 50)
                    end = min(len(data), pos + len(pattern) + 50)
                    context = data[start:end]
                    print(f"上下文: {context}")
                    print(f"HEX: {context.hex().upper()}")
                    print("-" * 80)
                    
                # UTF-16版本
                try:
                    utf16_pattern = pattern.decode('utf-8').encode('utf-16le')
                    pos = data.find(utf16_pattern)
                    if pos != -1:
                        print(f"找到 '{pattern.decode('utf-8', errors='ignore')}' (UTF-16) 位置: 0x{pos:04X}")
                        start = max(0, pos - 50)
                        end = min(len(data), pos + len(utf16_pattern) + 50)
                        context = data[start:end]
                        print(f"HEX: {context.hex().upper()}")
                        print("-" * 80)
                except:
                    pass
            
            # 搜索数字12相关的常量
            print("\n搜索可能的错误代码12:")
            
            # 搜索可能的比较指令和常量
            for i in range(len(data) - 4):
                # 搜索 cmp reg, 12 类型的指令
                if data[i] == 0x83 and i < len(data) - 3:  # cmp reg, imm8
                    if data[i+2] == 0x0C:  # 12 in hex
                        print(f"找到 'cmp reg, 12' 指令在 0x{i:04X}")
                        context = data[i-10:i+10]
                        print(f"上下文: {context.hex().upper()}")
                        
                # 搜索常量12
                if data[i:i+4] == b'\x0C\x00\x00\x00':  # 12 as little-endian 32-bit
                    print(f"找到32位常量12在 0x{i:04X}")
                    context = data[i-20:i+20]
                    print(f"上下文: {context.hex().upper()}")
            
            print("\n搜索完成")
            
    except FileNotFoundError:
        print(f"文件未找到: {filename}")
    except Exception as e:
        print(f"搜索过程中出错: {e}")

if __name__ == "__main__":
    filename = r"D:\新建文件夹 (2)\新建文件夹\EFI\BOOT\BOOT.EFI"
    search_error_messages(filename)