#!/usr/bin/env python3

def search_error_messages(filename):
    """搜索BOOT.EFI中的错误消息"""
    
    try:
        with open(filename, 'rb') as f:
            data = f.read()
            
            # 搜索错误消息
            error_patterns = [
                b"MBR check failed",
                b"Error message",
                b"check failed", 
                b"MBR",
                b"UUID",
                b"GUID",
                b"binding",
                b"failed"
            ]
            
            print("搜索错误消息和绑定相关字符串:\n")
            
            for pattern in error_patterns:
                pos = data.find(pattern)
                if pos != -1:
                    print(f"找到 '{pattern.decode('utf-8', errors='ignore')}' 位置: 0x{pos:04X}")
                    # 提取上下文
                    start = max(0, pos - 50)
                    end = min(len(data), pos + len(pattern) + 50)
                    context = data[start:end]
                    
                    print(f"上下文 (ASCII): {context}")
                    print(f"上下文 (HEX): {context.hex().upper()}")
                    print("-" * 80)
                    
                # 也搜索UTF-16编码版本
                utf16_pattern = pattern.decode('utf-8').encode('utf-16le')
                pos = data.find(utf16_pattern)
                if pos != -1:
                    print(f"找到 '{pattern.decode('utf-8', errors='ignore')}' (UTF-16) 位置: 0x{pos:04X}")
                    start = max(0, pos - 50)
                    end = min(len(data), pos + len(utf16_pattern) + 50)
                    context = data[start:end]
                    print(f"上下文 (HEX): {context.hex().upper()}")
                    print("-" * 80)
                    
            print("\n搜索完成")
            
    except FileNotFoundError:
        print(f"文件未找到: {filename}")
    except Exception as e:
        print(f"搜索过程中出错: {e}")

if __name__ == "__main__":
    filename = r"D:\新建文件夹 (2)\新建文件夹\EFI\BOOT\BOOT.EFI"
    search_error_messages(filename)