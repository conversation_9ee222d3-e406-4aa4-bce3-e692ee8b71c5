#!/usr/bin/env python3
import struct

def search_expected_values(filename):
    """搜索BOOT.EFI中可能存储的预期硬件值"""
    
    try:
        with open(filename, 'rb') as f:
            data = f.read()
            
            print("=== 搜索预期硬件值 ===\n")
            
            # 1. 搜索原版U盘的硬件标识符的各种编码形式
            print("1. 搜索原版U盘硬件标识 'General_UDisk___________5':")
            
            original_hardware_variants = [
                b"General_UDisk___________5",
                b"General_UDisk",
                b"UDisk",
                b"General",
                b"GenDisk",  # 从你的硬件ID中看到的
            ]
            
            for variant in original_hardware_variants:
                positions = []
                start = 0
                while True:
                    pos = data.find(variant, start)
                    if pos == -1:
                        break
                    positions.append(pos)
                    start = pos + 1
                
                if positions:
                    print(f"  找到 '{variant.decode('ascii', errors='ignore')}' {len(positions)} 次:")
                    for pos in positions:
                        context = data[max(0, pos-40):pos+len(variant)+40]
                        ascii_context = ''.join(chr(b) if 32 <= b <= 126 else '.' for b in context)
                        print(f"    0x{pos:04X}: {ascii_context}")
            
            # 2. 搜索哈希值或加密后的硬件信息
            print(f"\n2. 搜索可能的哈希值或编码后的硬件信息:")
            
            # 搜索WPSettings.dat中的8字节数据
            wpsettings_data_1 = bytes.fromhex("4366 5FD1 19DD C6D2".replace(" ", ""))
            wpsettings_data_2 = bytes.fromhex("4FF8 8231 FA28 A13E".replace(" ", ""))
            
            for i, data_pattern in enumerate([wpsettings_data_1, wpsettings_data_2], 1):
                pos = data.find(data_pattern)
                if pos != -1:
                    print(f"  找到WPSettings数据{i} @ 0x{pos:04X}")
                    context = data[max(0, pos-30):pos+len(data_pattern)+30]
                    print(f"    上下文: {context.hex().upper()}")
                    ascii_context = ''.join(chr(b) if 32 <= b <= 126 else '.' for b in context)
                    print(f"    ASCII: {ascii_context}")
            
            # 3. 搜索可能的查找表或数组
            print(f"\n3. 搜索可能的硬件ID查找表:")
            
            # 搜索连续的字符串模式，可能是硬件ID数组
            potential_strings = []
            current_string = ""
            string_start = 0
            
            for i, byte in enumerate(data):
                if 32 <= byte <= 126:  # 可打印字符
                    if not current_string:
                        string_start = i
                    current_string += chr(byte)
                else:
                    if len(current_string) >= 8:  # 至少8个字符的字符串
                        potential_strings.append((string_start, current_string))
                    current_string = ""
            
            # 查找可能的硬件相关字符串
            hardware_related = []
            for offset, string in potential_strings:
                if any(keyword in string.lower() for keyword in ['usb', 'disk', 'device', 'vendor', 'product', 'id']):
                    hardware_related.append((offset, string))
            
            print(f"  找到 {len(hardware_related)} 个可能的硬件相关字符串:")
            for offset, string in hardware_related[:10]:
                print(f"    0x{offset:04X}: {string}")
            
            # 4. 搜索可能的比较常量
            print(f"\n4. 搜索可能的比较常量:")
            
            # 搜索4字节和8字节的常量，可能是硬件ID的哈希值
            constants_found = []
            
            for i in range(0, len(data) - 8, 4):  # 4字节对齐
                # 检查是否是有意义的32位常量
                const32 = struct.unpack('<I', data[i:i+4])[0]
                
                # 过滤掉一些明显的非硬件ID值
                if (0x1000 < const32 < 0xFFFFFFFF and 
                    const32 != 0x00000000 and 
                    const32 != 0xFFFFFFFF and
                    const32 != 0x12345678):
                    
                    # 检查前后是否有相关的字符串
                    context_before = data[max(0, i-50):i]
                    context_after = data[i+4:i+54]
                    
                    # 如果附近有硬件相关的字符串
                    context_str = (context_before + context_after).decode('ascii', errors='ignore')
                    if any(keyword in context_str.lower() for keyword in 
                           ['device', 'disk', 'usb', 'vendor', 'id', 'check', 'verify']):
                        constants_found.append((i, const32, context_str))
            
            print(f"  找到 {len(constants_found)} 个可能的硬件相关常量:")
            for offset, const, context in constants_found[:15]:
                print(f"    0x{offset:04X}: 0x{const:08X}")
                print(f"      上下文: {context[:60]}...")
            
            # 5. 搜索可能的算法或哈希函数
            print(f"\n5. 搜索可能的哈希或加密算法标识:")
            
            crypto_patterns = [
                b"MD5", b"SHA", b"CRC", b"hash", b"crypt",
                b"algorithm", b"digest", b"checksum"
            ]
            
            for pattern in crypto_patterns:
                pos = data.find(pattern)
                if pos != -1:
                    print(f"  找到算法标识 '{pattern.decode('ascii')}' @ 0x{pos:04X}")
                    context = data[max(0, pos-30):pos+len(pattern)+30]
                    ascii_context = ''.join(chr(b) if 32 <= b <= 126 else '.' for b in context)
                    print(f"    上下文: {ascii_context}")
            
            # 6. 搜索可能的配置数据结构
            print(f"\n6. 搜索可能的配置数据结构:")
            
            # 搜索看起来像结构化数据的模式
            structured_data = []
            
            for i in range(0, len(data) - 32, 16):  # 16字节对齐的数据块
                block = data[i:i+32]
                
                # 检查是否包含混合的数据（既有字符串又有数值）
                printable_count = sum(1 for b in block if 32 <= b <= 126)
                zero_count = sum(1 for b in block if b == 0)
                
                # 如果有适量的可打印字符和一些零字节，可能是结构化数据
                if 8 <= printable_count <= 24 and 2 <= zero_count <= 8:
                    # 检查是否包含硬件相关的字符
                    block_str = ''.join(chr(b) if 32 <= b <= 126 else '' for b in block)
                    if any(keyword in block_str.lower() for keyword in 
                           ['usb', 'disk', 'id', 'device', 'gen']):
                        structured_data.append((i, block, block_str))
            
            print(f"  找到 {len(structured_data)} 个可能的结构化数据块:")
            for offset, block, text in structured_data[:10]:
                print(f"    0x{offset:04X}: {block.hex().upper()}")
                print(f"      文本: {text}")
            
            print(f"\n=== 搜索完成 ===")
            
    except FileNotFoundError:
        print(f"文件未找到: {filename}")
    except Exception as e:
        print(f"分析过程中出错: {e}")

if __name__ == "__main__":
    filename = r"D:\新建文件夹 (2)\新建文件夹\EFI\BOOT\BOOT.EFI"
    search_expected_values(filename)