#!/usr/bin/env python3

def search_wpsettings_references(filename):
    """搜索BOOT.EFI中对WPSettings.dat的引用"""
    
    try:
        with open(filename, 'rb') as f:
            data = f.read()
            
            print("搜索WPSettings.dat相关引用...\n")
            
            # 搜索文件名引用
            patterns = [
                b"WPSettings.dat",
                b"WPSettings",
                b"wpSettings", 
                b"WPSETTINGS",
                b"Settings.dat",
                b"Volume Information",
                b"System Volume"
            ]
            
            for pattern in patterns:
                pos = data.find(pattern)
                if pos != -1:
                    print(f"找到 '{pattern.decode('utf-8', errors='ignore')}' 位置: 0x{pos:04X}")
                    start = max(0, pos - 50)
                    end = min(len(data), pos + len(pattern) + 50)
                    context = data[start:end]
                    print(f"上下文: {context.hex().upper()}")
                    print("-" * 80)
                    
                # UTF-16版本
                utf16_pattern = pattern.decode('utf-8', errors='ignore').encode('utf-16le')
                pos = data.find(utf16_pattern)
                if pos != -1:
                    print(f"找到 '{pattern.decode('utf-8', errors='ignore')}' (UTF-16) 位置: 0x{pos:04X}")
                    start = max(0, pos - 50)
                    end = min(len(data), pos + len(utf16_pattern) + 50)
                    context = data[start:end]
                    print(f"上下文: {context.hex().upper()}")
                    print("-" * 80)
            
            # 搜索WPSettings.dat文件中的特定字节序列
            target_bytes = bytes.fromhex("4FF8823 1FA28A13E".replace(" ", ""))
            pos = data.find(target_bytes)
            if pos != -1:
                print(f"找到WPSettings.dat中的数据序列 位置: 0x{pos:04X}")
                start = max(0, pos - 50)
                end = min(len(data), pos + len(target_bytes) + 50)
                context = data[start:end]
                print(f"上下文: {context.hex().upper()}")
                print("-" * 80)
            
            # 搜索可能的哈希算法常量
            hash_constants = [
                b"\x67\x45\x23\x01",  # MD5常量
                b"\x01\x23\x45\x67",  # MD5常量
                b"\x98\xBA\xDC\xFE",  # MD5常量
                b"\x10\x32\x54\x76",  # MD5常量
            ]
            
            print("\n搜索可能的哈希算法常量:")
            for const in hash_constants:
                pos = data.find(const)
                if pos != -1:
                    print(f"找到哈希常量 {const.hex().upper()} 位置: 0x{pos:04X}")
            
            print("\n搜索完成")
            
    except FileNotFoundError:
        print(f"文件未找到: {filename}")
    except Exception as e:
        print(f"搜索过程中出错: {e}")

if __name__ == "__main__":
    filename = r"D:\新建文件夹 (2)\新建文件夹\EFI\BOOT\BOOT.EFI"
    search_wpsettings_references(filename)