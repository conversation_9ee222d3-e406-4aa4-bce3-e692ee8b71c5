#!/bin/bash

# Ventoy UUID 动态适配脚本
# 用于在任何U盘设备上临时启用Ventoy系统

echo "=== Ventoy UUID 动态适配工具 ==="
echo ""

# 检查是否以root权限运行
if [ "$EUID" -ne 0 ]; then
    echo "错误: 请以root权限运行此脚本"
    echo "使用: sudo $0"
    exit 1
fi

# 自动检测U盘设备
USB_DEVICE=""
for dev in /dev/sd[b-z]; do
    if [ -e "$dev" ]; then
        # 检查是否是可移动设备
        if [ -f "/sys/block/$(basename $dev)/removable" ]; then
            if [ "$(cat /sys/block/$(basename $dev)/removable)" = "1" ]; then
                USB_DEVICE="$dev"
                break
            fi
        fi
    fi
done

if [ -z "$USB_DEVICE" ]; then
    echo "错误: 未找到U盘设备"
    echo "请手动指定设备: $0 /dev/sdX"
    exit 1
fi

# 如果用户指定了设备参数
if [ ! -z "$1" ]; then
    USB_DEVICE="$1"
fi

echo "检测到U盘设备: $USB_DEVICE"

# 安全检查
if [ ! -b "$USB_DEVICE" ]; then
    echo "错误: $USB_DEVICE 不是有效的块设备"
    exit 1
fi

# 检查设备大小，防止误操作硬盘
DEVICE_SIZE=$(blockdev --getsize64 "$USB_DEVICE" 2>/dev/null)
if [ -z "$DEVICE_SIZE" ] || [ "$DEVICE_SIZE" -gt 137438953472 ]; then  # 128GB
    echo "警告: 设备 $USB_DEVICE 容量超过128GB，可能不是U盘"
    echo "设备大小: $(echo $DEVICE_SIZE | awk '{print $1/1024/1024/1024 " GB"}')"
    read -p "确定要继续吗? (y/N): " confirm
    if [ "$confirm" != "y" ] && [ "$confirm" != "Y" ]; then
        echo "操作已取消"
        exit 1
    fi
fi

echo ""
echo "=== 备份当前MBR ==="
BACKUP_FILE="./mbr_backup_$(date +%Y%m%d_%H%M%S).bin"
dd if="$USB_DEVICE" bs=512 count=1 of="$BACKUP_FILE" 2>/dev/null
if [ $? -eq 0 ]; then
    echo "MBR备份成功: $BACKUP_FILE"
else
    echo "错误: MBR备份失败"
    exit 1
fi

echo ""
echo "=== 读取当前磁盘签名 ==="
CURRENT_SIG=$(dd if="$USB_DEVICE" bs=1 skip=440 count=8 2>/dev/null | xxd -p)
echo "当前签名: $CURRENT_SIG"

echo ""
echo "=== 写入Ventoy绑定签名 ==="
TARGET_SIG="e2a0354cc1a40000"
echo "目标签名: $TARGET_SIG"

# 写入绑定签名
printf '\xe2\xa0\x35\x4c\xc1\xa4\x00\x00' | dd of="$USB_DEVICE" bs=1 seek=440 count=8 2>/dev/null
if [ $? -eq 0 ]; then
    echo "签名写入成功"
else
    echo "错误: 签名写入失败"
    exit 1
fi

# 验证写入结果
NEW_SIG=$(dd if="$USB_DEVICE" bs=1 skip=440 count=8 2>/dev/null | xxd -p)
echo "新签名: $NEW_SIG"

if [ "$NEW_SIG" = "$TARGET_SIG" ]; then
    echo ""
    echo "✓ UUID适配完成！"
    echo ""
    echo "说明:"
    echo "1. 原始签名已备份到: $BACKUP_FILE"
    echo "2. 现在可以尝试启动Ventoy系统"
    echo "3. 如果出现问题，可以恢复备份:"
    echo "   dd if='$BACKUP_FILE' of='$USB_DEVICE' bs=512 count=1"
    echo ""
    echo "注意: 此修改只是临时的，如果问题仍然存在，"
    echo "说明验证机制可能更复杂，需要其他解决方案。"
else
    echo ""
    echo "✗ 签名验证失败，自动恢复备份..."
    dd if="$BACKUP_FILE" of="$USB_DEVICE" bs=512 count=1 2>/dev/null
    echo "已恢复原始MBR"
    exit 1
fi