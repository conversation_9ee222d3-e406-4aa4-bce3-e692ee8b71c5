{"file_info": {"boot_efi_size": 23552, "grub_cfg_size": 87328}, "dmar_analysis": {"dmar_insert_function": {"offset": "0x00004270", "context": "72 c1 9f ef b2 a1 93 46 b3 27 6d 32 fc 41 60 42 74 69 d9 0f aa 23 dc 4c b9 cb 98 d1 77 50 32 2a 44 6d 61 72 49 6e 73 65 72 74 00 00 00 00 00 00 0d 00 0a 00 43 00 50 00 55 00 82 53 46 55 c0 68"}, "dmar_table_structure": null, "acpi_signatures": [{"signature": "DMAR", "offset": "0x0000437c", "context": "2e 00 31 00 32 00 61 00 29 00 0d 00 0a 00 00 00 44 4d 41 52 00 00 00 00 41 43 50 49 00 00 00 00"}, {"signature": "ACPI", "offset": "0x00004384", "context": "29 00 0d 00 0a 00 00 00 44 4d 41 52 00 00 00 00 41 43 50 49 00 00 00 00 00 00 00 00 0d 00 0a 00"}], "iommu_references": [], "cpu_vendor_checks": [{"vendor": "GenuineIntel", "offset": "0x000042a0", "context": "4b 6d d3 7e 9c 67 3a 00 0d 00 0a 00 00 00 00 00 47 65 6e 75 69 6e 65 49 6e 74 65 6c 00 00 00 00"}, {"vendor": "AuthenticAMD", "offset": "0x000042f0", "context": "31 00 32 00 61 00 29 00 0d 00 0a 00 00 00 00 00 41 75 74 68 65 6e 74 69 63 41 4d 44 00 00 00 00"}]}, "grub_analysis": {"acpi_functions": [{"pattern": "ventoy_acpi_param\\s+([^}]+)", "match": "ventoy_acpi_param {  \n    if [ \"$VTOY_PARAM_NO_ACPI\" != \"1\" ]; then\n        vt_acpi_param \"$1\" \"$2\"\n    fi\n", "context": "  terminal_output  gfxterm\n    fi    \n}\n\nfunction ventoy_acpi_param {  \n    if [ \"$VTOY_PARAM_NO_ACP"}, {"pattern": "ventoy_acpi_param\\s+([^}]+)", "match": "ventoy_acpi_param ${vtoy_chain_mem_addr", "context": "   if [ -n \"$vtoy_chain_mem_addr\" ]; then\n        ventoy_acpi_param ${vtoy_chain_mem_addr} 2048\n    "}, {"pattern": "ventoy_acpi_param\\s+([^}]+)", "match": "ventoy_acpi_param ${vtoy_chain_mem_addr", "context": "   if [ -n \"$vtoy_chain_mem_addr\" ]; then\n        ventoy_acpi_param ${vtoy_chain_mem_addr} 2048\n    "}, {"pattern": "ventoy_acpi_param\\s+([^}]+)", "match": "ventoy_acpi_param ${vtoy_chain_mem_addr", "context": "   if [ -n \"$vtoy_chain_mem_addr\" ]; then\n        ventoy_acpi_param ${vtoy_chain_mem_addr} 2048\n    "}, {"pattern": "ventoy_acpi_param\\s+([^}]+)", "match": "ventoy_acpi_param ${vtoy_chain_mem_addr", "context": "   if [ -n \"$vtoy_chain_mem_addr\" ]; then\n        ventoy_acpi_param ${vtoy_chain_mem_addr} 2048\n    "}, {"pattern": "ventoy_acpi_param\\s+([^}]+)", "match": "ventoy_acpi_param ${vtoy_chain_mem_addr", "context": "       if [ -n \"$vtGrub2Mode\" ]; then\n            ventoy_acpi_param ${vtoy_chain_mem_addr} 2048\n    "}, {"pattern": "ventoy_acpi_param\\s+([^}]+)", "match": "ventoy_acpi_param ${vtoy_chain_mem_addr", "context": "      ventoy_gui_console\n        else\n            ventoy_acpi_param ${vtoy_chain_mem_addr} 2048\n    "}, {"pattern": "ventoy_acpi_param\\s+([^}]+)", "match": "ventoy_acpi_param ${vtoy_chain_mem_addr", "context": "  if [ -n \"$vtoy_chain_mem_addr\" ]; then\n        #ventoy_acpi_param ${vtoy_chain_mem_addr} 2048\n    "}, {"pattern": "ventoy_acpi_param\\s+([^}]+)", "match": "ventoy_acpi_param ${vtoy_chain_mem_addr", "context": "_data \"${vtoy_iso_part}${vt_chosen_path}\"\n        ventoy_acpi_param ${vtoy_chain_mem_addr} 512\n     "}, {"pattern": "vt_acpi_param\\s+([^}]+)", "match": "vt_acpi_param \"$1\" \"$2\"\n    fi\n", "context": "if [ \"$VTOY_PARAM_NO_ACPI\" != \"1\" ]; then\n        vt_acpi_param \"$1\" \"$2\"\n    fi\n}\n\nfunction ventoy_"}, {"pattern": "vt_acpi_param\\s+([^}]+)", "match": "vt_acpi_param ${vtoy_chain_mem_addr", "context": "[ \"$grub_platform\" = \"pc\" ]; then\n                vt_acpi_param ${vtoy_chain_mem_addr} 512\n         "}, {"pattern": "vt_acpi_param\\s+([^}]+)", "match": "vt_acpi_param ${vtoy_chain_mem_addr", "context": "vt_check_secureboot_var; then\n                    vt_acpi_param ${vtoy_chain_mem_addr} 512\n         "}, {"pattern": "vtoy_chain_mem_addr", "match": "vtoy_chain_mem_addr", "context": "n\n        vtoy_wimboot_func\n    fi\n\n    if [ -n \"$vtoy_chain_mem_addr\" ]; then\n        ventoy_acpi_p"}, {"pattern": "vtoy_chain_mem_addr", "match": "vtoy_chain_mem_addr", "context": "ain_mem_addr\" ]; then\n        ventoy_acpi_param ${vtoy_chain_mem_addr} 2048\n        ventoy_max_resol"}, {"pattern": "vtoy_chain_mem_addr", "match": "vtoy_chain_mem_addr", "context": "ver} iso_${ventoy_fs_probe} ${vtdebug_flag} mem:${vtoy_chain_mem_addr}:size:${vtoy_chain_mem_size}\n "}, {"pattern": "vtoy_chain_mem_addr", "match": "vtoy_chain_mem_addr", "context": "LoadIsoEfiDriver\n        fi\n    fi\n\n    if [ -n \"$vtoy_chain_mem_addr\" ]; then\n        ventoy_acpi_p"}, {"pattern": "vtoy_chain_mem_addr", "match": "vtoy_chain_mem_addr", "context": "ain_mem_addr\" ]; then\n        ventoy_acpi_param ${vtoy_chain_mem_addr} 2048\n        ventoy_cli_conso"}, {"pattern": "vtoy_chain_mem_addr", "match": "vtoy_chain_mem_addr", "context": "irstTry=${FirstTryBootFile} ${vtdebug_flag} mem:${vtoy_chain_mem_addr}:size:${vtoy_chain_mem_size}\n "}, {"pattern": "vtoy_chain_mem_addr", "match": "vtoy_chain_mem_addr", "context": "_comm_proc $1 \"${chosen_path}\"\n    \n    if [ -n \"$vtoy_chain_mem_addr\" ]; then\n        ventoy_acpi_p"}, {"pattern": "vtoy_chain_mem_addr", "match": "vtoy_chain_mem_addr", "context": "ain_mem_addr\" ]; then\n        ventoy_acpi_param ${vtoy_chain_mem_addr} 2048\n        ventoy_cli_conso"}, {"pattern": "vtoy_chain_mem_addr", "match": "vtoy_chain_mem_addr", "context": "irstTry=${FirstTryBootFile} ${vtdebug_flag} mem:${vtoy_chain_mem_addr}:size:${vtoy_chain_mem_size}\n "}, {"pattern": "vtoy_chain_mem_addr", "match": "vtoy_chain_mem_addr", "context": "n\n        vtoy_wimboot_func\n    fi\n\n    if [ -n \"$vtoy_chain_mem_addr\" ]; then\n        ventoy_acpi_p"}, {"pattern": "vtoy_chain_mem_addr", "match": "vtoy_chain_mem_addr", "context": "ain_mem_addr\" ]; then\n        ventoy_acpi_param ${vtoy_chain_mem_addr} 2048\n        if [ \"$ventoy_co"}, {"pattern": "vtoy_chain_mem_addr", "match": "vtoy_chain_mem_addr", "context": "nux16   $vtoy_path/ipxe.krn ${vtdebug_flag} mem:${vtoy_chain_mem_addr}:size:${vtoy_chain_mem_size}\n "}, {"pattern": "vtoy_chain_mem_addr", "match": "vtoy_chain_mem_addr", "context": "   $vtoy_path/ipxe.krn ${vtdebug_flag} ibft mem:${vtoy_chain_mem_addr}:size:${vtoy_chain_mem_size}\n "}, {"pattern": "vtoy_chain_mem_addr", "match": "vtoy_chain_mem_addr", "context": "_path}\"\n    ventoy_debug_pause\n    \n    if [ -n \"$vtoy_chain_mem_addr\" ]; then\n        unset vtGrub2"}, {"pattern": "vtoy_chain_mem_addr", "match": "vtoy_chain_mem_addr", "context": "rub2Mode\" ]; then\n            ventoy_acpi_param ${vtoy_chain_mem_addr} 2048\n            ventoy_cli_c"}, {"pattern": "vtoy_chain_mem_addr", "match": "vtoy_chain_mem_addr", "context": "sole\n        else\n            ventoy_acpi_param ${vtoy_chain_mem_addr} 2048\n            linux16   $v"}, {"pattern": "vtoy_chain_mem_addr", "match": "vtoy_chain_mem_addr", "context": "ux16   $vtoy_path/ipxe.krn ${vtdebug_flag}  mem:${vtoy_chain_mem_addr}:size:${vtoy_chain_mem_size}  "}, {"pattern": "vtoy_chain_mem_addr", "match": "vtoy_chain_mem_addr", "context": "_comm_proc $1 \"${chosen_path}\"\n    \n    if [ -n \"$vtoy_chain_mem_addr\" ]; then\n        #ventoy_acpi_"}, {"pattern": "vtoy_chain_mem_addr", "match": "vtoy_chain_mem_addr", "context": "in_mem_addr\" ]; then\n        #ventoy_acpi_param ${vtoy_chain_mem_addr} 2048\n        linux16   $vtoy_"}, {"pattern": "vtoy_chain_mem_addr", "match": "vtoy_chain_mem_addr", "context": "ux16   $vtoy_path/ipxe.krn ${vtdebug_flag}  mem:${vtoy_chain_mem_addr}:size:${vtoy_chain_mem_size}\n "}, {"pattern": "vtoy_chain_mem_addr", "match": "vtoy_chain_mem_addr", "context": "   \n    ventoy_debug_pause    \n    \n    if [ -n \"$vtoy_chain_mem_addr\" ]; then\n        if [ \"$grub_p"}, {"pattern": "vtoy_chain_mem_addr", "match": "vtoy_chain_mem_addr", "context": "ux16   $vtoy_path/ipxe.krn ${vtdebug_flag}  mem:${vtoy_chain_mem_addr}:size:${vtoy_chain_mem_size}  "}, {"pattern": "vtoy_chain_mem_addr", "match": "vtoy_chain_mem_addr", "context": " isoefi=${LoadIsoEfiDriver} ${vtdebug_flag} mem:${vtoy_chain_mem_addr}:size:${vtoy_chain_mem_size}\n "}, {"pattern": "vtoy_chain_mem_addr", "match": "vtoy_chain_mem_addr", "context": "    ventoy_debug_pause\n        \n        if [ -n \"$vtoy_chain_mem_addr\" ]; then  \n            if [ \"$"}, {"pattern": "vtoy_chain_mem_addr", "match": "vtoy_chain_mem_addr", "context": "m\" = \"pc\" ]; then\n                vt_acpi_param ${vtoy_chain_mem_addr} 512\n                linux16 $"}, {"pattern": "vtoy_chain_mem_addr", "match": "vtoy_chain_mem_addr", "context": "ipxe.krn ${vtdebug_flag} bios80  sector512  mem:${vtoy_chain_mem_addr}:size:${vtoy_chain_mem_size}  "}, {"pattern": "vtoy_chain_mem_addr", "match": "vtoy_chain_mem_addr", "context": "oot_var; then\n                    vt_acpi_param ${vtoy_chain_mem_addr} 512\n                fi\n      "}, {"pattern": "vtoy_chain_mem_addr", "match": "vtoy_chain_mem_addr", "context": "v_param=${ventoy_env_param} ${vtdebug_flag} mem:${vtoy_chain_mem_addr}:size:${vtoy_chain_mem_size}\n "}, {"pattern": "vtoy_chain_mem_addr", "match": "vtoy_chain_mem_addr", "context": "rt}${vt_chosen_path}\"\n        ventoy_acpi_param ${vtoy_chain_mem_addr} 512\n        if [ \"$grub_platf"}, {"pattern": "vtoy_chain_mem_addr", "match": "vtoy_chain_mem_addr", "context": "y_path/ipxe.krn ${vtdebug_flag}  sector512  mem:${vtoy_chain_mem_addr}:size:${vtoy_chain_mem_size}  "}, {"pattern": "vtoy_chain_mem_addr", "match": "vtoy_chain_mem_addr", "context": "irstTry=${FirstTryBootFile} ${vtdebug_flag} mem:${vtoy_chain_mem_addr}:size:${vtoy_chain_mem_size}\n "}, {"pattern": "vtoy_chain_mem_size", "match": "vtoy_chain_mem_size", "context": "${vtdebug_flag} mem:${vtoy_chain_mem_addr}:size:${vtoy_chain_mem_size}\n        boot\n    else\n       "}, {"pattern": "vtoy_chain_mem_size", "match": "vtoy_chain_mem_size", "context": "${vtdebug_flag} mem:${vtoy_chain_mem_addr}:size:${vtoy_chain_mem_size}\n                boot\n        "}, {"pattern": "vtoy_chain_mem_size", "match": "vtoy_chain_mem_size", "context": "${vtdebug_flag} mem:${vtoy_chain_mem_addr}:size:${vtoy_chain_mem_size}\n        boot\n    else\n       "}, {"pattern": "vtoy_chain_mem_size", "match": "vtoy_chain_mem_size", "context": "${vtdebug_flag} mem:${vtoy_chain_mem_addr}:size:${vtoy_chain_mem_size}\n        else\n            linu"}, {"pattern": "vtoy_chain_mem_size", "match": "vtoy_chain_mem_size", "context": "ebug_flag} ibft mem:${vtoy_chain_mem_addr}:size:${vtoy_chain_mem_size}\n        fi\n        boot\n    e"}, {"pattern": "vtoy_chain_mem_size", "match": "vtoy_chain_mem_size", "context": "{vtdebug_flag}  mem:${vtoy_chain_mem_addr}:size:${vtoy_chain_mem_size}            \n            boot\n"}, {"pattern": "vtoy_chain_mem_size", "match": "vtoy_chain_mem_size", "context": "{vtdebug_flag}  mem:${vtoy_chain_mem_addr}:size:${vtoy_chain_mem_size}\n        boot\n    else\n       "}, {"pattern": "vtoy_chain_mem_size", "match": "vtoy_chain_mem_size", "context": "{vtdebug_flag}  mem:${vtoy_chain_mem_addr}:size:${vtoy_chain_mem_size}            \n        else\n    "}, {"pattern": "vtoy_chain_mem_size", "match": "vtoy_chain_mem_size", "context": "${vtdebug_flag} mem:${vtoy_chain_mem_addr}:size:${vtoy_chain_mem_size}\n            ventoy_gui_consol"}, {"pattern": "vtoy_chain_mem_size", "match": "vtoy_chain_mem_size", "context": "s80  sector512  mem:${vtoy_chain_mem_addr}:size:${vtoy_chain_mem_size}   \n                boot\n     "}, {"pattern": "vtoy_chain_mem_size", "match": "vtoy_chain_mem_size", "context": "${vtdebug_flag} mem:${vtoy_chain_mem_addr}:size:${vtoy_chain_mem_size}\n                boot\n        "}, {"pattern": "vtoy_chain_mem_size", "match": "vtoy_chain_mem_size", "context": "ag}  sector512  mem:${vtoy_chain_mem_addr}:size:${vtoy_chain_mem_size}   \n            boot\n        e"}, {"pattern": "vtoy_chain_mem_size", "match": "vtoy_chain_mem_size", "context": "${vtdebug_flag} mem:${vtoy_chain_mem_addr}:size:${vtoy_chain_mem_size}\n            boot\n        fi\n "}], "vtoy_chain_usage": [{"usage": "vtoy_chain_mem_addr\" ]; then\n        ventoy_acpi_param ${vtoy_chain_mem_addr", "context": "ause\n\n    if [ \"$vt_cur_wimboot_mode\" = \"1\" ]; then\n        vtoy_wimboot_func\n    fi\n\n    if [ -n \"$vtoy_chain_mem_addr\" ]; then\n        ventoy_acpi_param ${vtoy_chain_mem_addr} 2048\n        ventoy_ma"}, {"usage": "vtoy_chain_mem_addr", "context": "efi  env_param=${env_param} isoefi=${LoadIsoEfiDriver} iso_${ventoy_fs_probe} ${vtdebug_flag} mem:${vtoy_chain_mem_addr}:size:${vtoy_chain_mem_size}\n        boot\n    else\n        echo \"chain empty fai"}, {"usage": "vtoy_chain_mem_addr\" ]; then\n        ventoy_acpi_param ${vtoy_chain_mem_addr", "context": "fi/clover/cloverx64.efi ]; then\n            unset LoadIsoEfiDriver\n        fi\n    fi\n\n    if [ -n \"$vtoy_chain_mem_addr\" ]; then\n        ventoy_acpi_param ${vtoy_chain_mem_addr} 2048\n        ventoy_cl"}, {"usage": "vtoy_chain_mem_addr", "context": "nv_param=${env_param} isoefi=${LoadIsoEfiDriver} FirstTry=${FirstTryBootFile} ${vtdebug_flag} mem:${vtoy_chain_mem_addr}:size:${vtoy_chain_mem_size}\n                boot\n            fi\n        fi\n\n   "}, {"usage": "vtoy_chain_mem_addr\" ]; then\n        ventoy_acpi_param ${vtoy_chain_mem_addr", "context": "\n}\n\nfunction uefi_unix_menu_func {\n    ventoy_unix_comm_proc $1 \"${chosen_path}\"\n    \n    if [ -n \"$vtoy_chain_mem_addr\" ]; then\n        ventoy_acpi_param ${vtoy_chain_mem_addr} 2048\n        ventoy_cl"}, {"usage": "vtoy_chain_mem_addr", "context": "nv_param=${env_param} isoefi=${LoadIsoEfiDriver} FirstTry=${FirstTryBootFile} ${vtdebug_flag} mem:${vtoy_chain_mem_addr}:size:${vtoy_chain_mem_size}\n        boot\n    else\n        echo \"chain empty fai"}, {"usage": "vtoy_chain_mem_addr\" ]; then\n        ventoy_acpi_param ${vtoy_chain_mem_addr", "context": "\n    \n    if [ \"$vt_cur_wimboot_mode\" = \"1\" ]; then\n        vtoy_wimboot_func\n    fi\n\n    if [ -n \"$vtoy_chain_mem_addr\" ]; then\n        ventoy_acpi_param ${vtoy_chain_mem_addr} 2048\n        if [ \"$ve"}, {"usage": "vtoy_chain_mem_addr", "context": "\"$ventoy_compatible\" = \"NO\" ]; then\n            linux16   $vtoy_path/ipxe.krn ${vtdebug_flag} mem:${vtoy_chain_mem_addr}:size:${vtoy_chain_mem_size}\n        else\n            linux16   $vtoy_path/ipxe."}, {"usage": "vtoy_chain_mem_addr", "context": "y_chain_mem_size}\n        else\n            linux16   $vtoy_path/ipxe.krn ${vtdebug_flag} ibft mem:${vtoy_chain_mem_addr}:size:${vtoy_chain_mem_size}\n        fi\n        boot\n    else\n        echo \"chai"}, {"usage": "vtoy_chain_mem_addr\" ]; then\n        unset vtGrub2Mode\n        if vt_check_mode 3 \"$vt_chosen_name\"; then\n            set vtGrub2Mode=1\n        elif vt_str_begin \"$vt_volume_id\" \"HOLO_\"; then\n            if [ -d (loop)/loader/entries ]; then\n                set vtGrub2Mode=1\n            fi        \n        fi\n    \n        if [ -n \"$vtGrub2Mode\" ]; then\n            ventoy_acpi_param ${vtoy_chain_mem_addr", "context": "\n    fi\n    \n    vt_linux_chain_data \"${1}${chosen_path}\"\n    ventoy_debug_pause\n    \n    if [ -n \"$vtoy_chain_mem_addr\" ]; then\n        unset vtGrub2Mode\n        if vt_check_mode 3 \"$vt_chosen_name\";"}, {"usage": "vtoy_chain_mem_addr", "context": "      vt_pop_last_entry\n            ventoy_gui_console\n        else\n            ventoy_acpi_param ${vtoy_chain_mem_addr} 2048\n            linux16   $vtoy_path/ipxe.krn ${vtdebug_flag}  mem:${vtoy_chai"}, {"usage": "vtoy_chain_mem_addr", "context": "_param ${vtoy_chain_mem_addr} 2048\n            linux16   $vtoy_path/ipxe.krn ${vtdebug_flag}  mem:${vtoy_chain_mem_addr}:size:${vtoy_chain_mem_size}            \n            boot\n        fi\n    else\n  "}, {"usage": "vtoy_chain_mem_addr\" ]; then\n        #ventoy_acpi_param ${vtoy_chain_mem_addr", "context": "nction legacy_unix_menu_func {    \n    ventoy_unix_comm_proc $1 \"${chosen_path}\"\n    \n    if [ -n \"$vtoy_chain_mem_addr\" ]; then\n        #ventoy_acpi_param ${vtoy_chain_mem_addr} 2048\n        linux16 "}, {"usage": "vtoy_chain_mem_addr", "context": "acpi_param ${vtoy_chain_mem_addr} 2048\n        linux16   $vtoy_path/ipxe.krn ${vtdebug_flag}  mem:${vtoy_chain_mem_addr}:size:${vtoy_chain_mem_size}\n        boot\n    else\n        echo \"chain empty fai"}, {"usage": "vtoy_chain_mem_addr\" ]; then\n        if [ \"$grub_platform\" = \"pc\" ]; then\n            linux16   $vtoy_path/ipxe.krn ${vtdebug_flag", "context": "n\"\n        echo -e \" 这不是一个可启动的 WIM 文件。\\n\"\n    fi\n    \n    ventoy_debug_pause    \n    \n    if [ -n \"$vtoy_chain_mem_addr\" ]; then\n        if [ \"$grub_platform\" = \"pc\" ]; then\n            linux16   $vto"}, {"usage": "vtoy_chain_mem_addr", "context": " [ \"$grub_platform\" = \"pc\" ]; then\n            linux16   $vtoy_path/ipxe.krn ${vtdebug_flag}  mem:${vtoy_chain_mem_addr}:size:${vtoy_chain_mem_size}            \n        else\n            ventoy_cli_con"}, {"usage": "vtoy_chain_mem_addr", "context": "entoy_${VTOY_EFI_ARCH}.efi  env_param=${env_param} isoefi=${LoadIsoEfiDriver} ${vtdebug_flag} mem:${vtoy_chain_mem_addr}:size:${vtoy_chain_mem_size}\n            ventoy_gui_console\n        fi\n        b"}, {"usage": "vtoy_chain_mem_addr\" ]; then  \n            if [ \"$grub_platform\" = \"pc\" ]; then\n                vt_acpi_param ${vtoy_chain_mem_addr", "context": "ctor \"${1}\"\n        vt_raw_chain_data \"${1}\"\n\n        ventoy_debug_pause\n        \n        if [ -n \"$vtoy_chain_mem_addr\" ]; then  \n            if [ \"$grub_platform\" = \"pc\" ]; then\n                vt_a"}, {"usage": "vtoy_chain_mem_addr", "context": "_mem_addr} 512\n                linux16 $vtoy_path/ipxe.krn ${vtdebug_flag} bios80  sector512  mem:${vtoy_chain_mem_addr}:size:${vtoy_chain_mem_size}   \n                boot\n            else\n          "}, {"usage": "vtoy_chain_mem_addr", "context": "          else\n                if vt_check_secureboot_var; then\n                    vt_acpi_param ${vtoy_chain_mem_addr} 512\n                fi\n                ventoy_cli_console\n                chain"}, {"usage": "vtoy_chain_mem_addr", "context": "toy_path}/ventoy_${VTOY_EFI_ARCH}.efi sector512 env_param=${ventoy_env_param} ${vtdebug_flag} mem:${vtoy_chain_mem_addr}:size:${vtoy_chain_mem_size}\n                boot\n                ventoy_gui_con"}, {"usage": "vtoy_chain_mem_addr", "context": "n chain\n        vt_linux_chain_data \"${vtoy_iso_part}${vt_chosen_path}\"\n        ventoy_acpi_param ${vtoy_chain_mem_addr} 512\n        if [ \"$grub_platform\" = \"pc\" ]; then \n            linux16 $vtoy_pat"}, {"usage": "vtoy_chain_mem_addr", "context": "platform\" = \"pc\" ]; then \n            linux16 $vtoy_path/ipxe.krn ${vtdebug_flag}  sector512  mem:${vtoy_chain_mem_addr}:size:${vtoy_chain_mem_size}   \n            boot\n        else            \n      "}, {"usage": "vtoy_chain_mem_addr", "context": "nv_param=${env_param} isoefi=${LoadIsoEfiDriver} FirstTry=${FirstTryBootFile} ${vtdebug_flag} mem:${vtoy_chain_mem_addr}:size:${vtoy_chain_mem_size}\n            boot\n        fi\n    fi\n\n    loopback -d"}], "debug_flags": [{"position": 1076, "context": "g_pause {\n    if [ -n \"${vtdebug_flag}\" ]; then\n  "}, {"position": 5353, "context": "i\n    fi\n\n    if [ -n \"${vtdebug_flag}\" ]; then\n  "}, {"position": 5976, "context": "_initrd \n\n    if [ -n \"${vtdebug_flag}\" ]; then   "}, {"position": 6159, "context": "\"$1\"\n    \n    if [ -n \"${vtdebug_flag}\" ]; then\n  "}, {"position": 19220, "context": "\n    fi\n\n\n    if [ -n \"${vtdebug_flag}\" ]; then\n  "}, {"position": 21299, "context": "e\n            if [ -n \"${vtdebug_flag}\" ]; then\n  "}, {"position": 22688, "context": "iso_${ventoy_fs_probe} ${vtdebug_flag} mem:${vtoy_"}, {"position": 29473, "context": "ry=${FirstTryBootFile} ${vtdebug_flag} mem:${vtoy_"}, {"position": 31993, "context": "ry=${FirstTryBootFile} ${vtdebug_flag} mem:${vtoy_"}, {"position": 34205, "context": "fi=${LoadIsoEfiDriver} ${vtdebug_flag} mem:${vtoy_"}, {"position": 34856, "context": "i\n    fi\n\n    if [ -n \"${vtdebug_flag}\" ]; then\n  "}, {"position": 36795, "context": "\"$1\"\n    \n    if [ -n \"${vtdebug_flag}\" ]; then\n  "}, {"position": 42012, "context": "   $vtoy_path/ipxe.krn ${vtdebug_flag} mem:${vtoy_"}, {"position": 42138, "context": "   $vtoy_path/ipxe.krn ${vtdebug_flag} ibft mem:${"}, {"position": 46966, "context": "   $vtoy_path/ipxe.krn ${vtdebug_flag}  mem:${vtoy"}, {"position": 47377, "context": "   $vtoy_path/ipxe.krn ${vtdebug_flag}  mem:${vtoy"}, {"position": 54751, "context": "   $vtoy_path/ipxe.krn ${vtdebug_flag}  mem:${vtoy"}, {"position": 54995, "context": "fi=${LoadIsoEfiDriver} ${vtdebug_flag} mem:${vtoy_"}, {"position": 56318, "context": "aram} dotefi isoefi=on ${vtdebug_flag} mem:${vtoy_"}, {"position": 57058, "context": "fi=${LoadIsoEfiDriver} ${vtdebug_flag} mem:${vtoy_"}, {"position": 59842, "context": "16 $vtoy_path/ipxe.krn ${vtdebug_flag} bios80  sec"}, {"position": 60246, "context": "am=${ventoy_env_param} ${vtdebug_flag} mem:${vtoy_"}, {"position": 77838, "context": "16 $vtoy_path/ipxe.krn ${vtdebug_flag}  sector512 "}, {"position": 78120, "context": "ry=${FirstTryBootFile} ${vtdebug_flag} mem:${vtoy_"}, {"position": 79042, "context": "fi=${LoadIsoEfiDriver} ${vtdebug_flag} mem:${vtoy_"}, {"position": 85707, "context": "\nexport vtoy_path\nexport vtdebug_flag\nexport vtoy_"}], "memory_operations": []}, "flow_analysis": {"initialization_phase": {"function\\s+ventoy_acpi_param[^}]*\\{([^}]*)\\}": {"code": "function ventoy_acpi_param {  \n    if [ \"$VTOY_PARAM_NO_ACPI\" != \"1\" ]; then\n        vt_acpi_param \"$1\" \"$2\"\n    fi\n}", "analysis": "ACPI参数处理和条件检查"}, "if\\s+\\[\\s+\"\\$VTOY_PARAM_NO_ACPI\"[^}]*": {"code": "if [ \"$VTOY_PARAM_NO_ACPI\" != \"1\" ]; then\n        vt_acpi_param \"$1\" \"$2\"\n    fi\n", "analysis": "ACPI参数处理和条件检查"}}, "memory_allocation_phase": {"vtoy_chain_mem_addr.*?(\\d+)": {"match": "vtoy_chain_mem_addr} 512", "analysis": "内存分配和地址管理"}, "vt_load_img_memdisk.*?vtoy_iso_buf": {"match": "vt_load_img_memdisk \"${1}${2}\" vtoy_iso_buf", "analysis": "内存分配和地址管理"}}, "dmar_construction_phase": {}, "injection_phase": {}, "activation_phase": {}}, "dmar_structure": {"acpi_header": {"signature": "DMAR", "length": 0, "revision": 65, "checksum": 67, "raw_data": "444D4152000000004143504900000000000000000D000A0049004F004D004D005500B672"}, "drhd_entries": [], "rmrr_entries": [], "construction_logic": {}}, "replication_guide": "\n# VTd模拟功能复刻指南\n\n## 1. 核心原理理解\n\n### VTd模拟的本质\n- **目标**: 让不支持Intel VT-d的硬件能够\"欺骗\"操作系统和虚拟化软件\n- **方法**: 通过注入假的ACPI DMAR表，使系统认为硬件支持VT-d\n- **时机**: 在操作系统启动前的GRUB阶段完成注入\n\n### 技术栈\n1. **GRUB模块**: 负责ACPI表构建和内存管理\n2. **EFI应用**: 负责DMAR表注入和系统欺骗\n3. **内存管理**: 使用EBDA区域存储假表\n\n## 2. 实现步骤\n\n### 步骤1: GRUB ACPI模块开发\n```c\n// acpi_vtd.c - GRUB ACPI VTd模拟模块\n#include <grub/acpi.h>\n#include <grub/memory.h>\n\nstruct grub_acpi_dmar {\n    struct grub_acpi_table_header header;\n    grub_uint8_t host_address_width;\n    grub_uint8_t flags;\n    grub_uint8_t reserved[10];\n    // DRHD entries follow\n};\n\ngrub_err_t grub_acpi_create_dmar_table(void) {\n    struct grub_acpi_dmar *dmar;\n    grub_size_t table_size = sizeof(struct grub_acpi_dmar) + 64; // 基础大小\n    \n    // 1. 分配内存\n    dmar = grub_malloc(table_size);\n    if (!dmar) return GRUB_ERR_OUT_OF_MEMORY;\n    \n    // 2. 构建表头\n    grub_memcpy(dmar->header.signature, \"DMAR\", 4);\n    dmar->header.length = table_size;\n    dmar->header.revision = 1;\n    grub_memcpy(dmar->header.oemid, \"VTDEMU\", 6);\n    \n    // 3. 设置VT-d参数\n    dmar->host_address_width = 46; // 64位系统典型值\n    dmar->flags = 0x01; // INTR_REMAP支持\n    \n    // 4. 添加DRHD条目\n    add_drhd_entry(dmar, 0xFED90000); // Intel标准IOMMU基址\n    \n    // 5. 计算校验和\n    dmar->header.checksum = calculate_checksum(dmar, table_size);\n    \n    // 6. 注入到ACPI表链\n    return grub_acpi_add_table(dmar);\n}\n```\n\n### 步骤2: EFI DmarInsert功能\n```c\n// dmar_insert.c - EFI DMAR注入功能\n#include <Uefi.h>\n#include <Library/UefiLib.h>\n\nEFI_STATUS DmarInsert(VOID) {\n    EFI_ACPI_SDT_PROTOCOL *AcpiSdt;\n    EFI_ACPI_TABLE_PROTOCOL *AcpiTable;\n    UINTN TableKey;\n    \n    // 1. 获取ACPI协议\n    Status = gBS->LocateProtocol(&gEfiAcpiSdtProtocolGuid, NULL, (VOID**)&AcpiSdt);\n    if (EFI_ERROR(Status)) return Status;\n    \n    // 2. 构建DMAR表\n    ACPI_DMAR_TABLE *DmarTable = BuildDmarTable();\n    \n    // 3. 安装DMAR表\n    Status = AcpiTable->InstallAcpiTable(AcpiTable, DmarTable, \n                                        DmarTable->Header.Length, &TableKey);\n    \n    return Status;\n}\n\nACPI_DMAR_TABLE* BuildDmarTable(VOID) {\n    ACPI_DMAR_TABLE *Dmar;\n    \n    // 分配内存\n    Dmar = AllocatePool(sizeof(ACPI_DMAR_TABLE) + 256);\n    \n    // 构建表头\n    CopyMem(Dmar->Header.Signature, \"DMAR\", 4);\n    Dmar->Header.Length = sizeof(ACPI_DMAR_TABLE) + 256;\n    Dmar->Header.Revision = 1;\n    \n    // 检测CPU类型并构建相应的DRHD\n    if (IsIntelCpu()) {\n        BuildIntelDrhd(Dmar);\n    } else if (IsAmdCpu()) {\n        BuildAmdDrhd(Dmar); // AMD IOMMU模拟\n    }\n    \n    // 计算校验和\n    Dmar->Header.Checksum = CalculateAcpiChecksum(Dmar);\n    \n    return Dmar;\n}\n```\n\n### 步骤3: 内存管理和参数传递\n```bash\n# grub.cfg中的集成\nfunction vtd_emulation_init {\n    # 检查是否启用VTd模拟\n    if [ \"$VTOY_PARAM_NO_ACPI\" != \"1\" ]; then\n        # 分配内存用于DMAR表\n        vt_alloc_chain_memory 2048\n        \n        # 构建DMAR表\n        vt_build_dmar_table ${vtoy_chain_mem_addr}\n        \n        # 传递给EFI应用\n        set vtd_dmar_addr=${vtoy_chain_mem_addr}\n        export vtd_dmar_addr\n    fi\n}\n\nfunction vtd_inject_dmar {\n    if [ -n \"$vtd_dmar_addr\" ]; then\n        # 调用EFI应用注入DMAR表\n        chainloader ${vtoy_path}/vtd_injector.efi dmar_addr=${vtd_dmar_addr}\n        boot\n    fi\n}\n```\n\n## 3. 关键技术细节\n\n### DMAR表结构\n```c\ntypedef struct {\n    ACPI_TABLE_HEADER Header;           // 标准ACPI表头\n    UINT8 HostAddressWidth;            // 主机地址宽度\n    UINT8 Flags;                       // 标志位\n    UINT8 Reserved[10];                // 保留字段\n    \n    // 可变长度的重映射结构\n    DRHD_STRUCTURE Drhd[MAX_DRHD];     // DMA重映射硬件单元\n    RMRR_STRUCTURE Rmrr[MAX_RMRR];     // 保留内存区域\n    ATSR_STRUCTURE Atsr[MAX_ATSR];     // 根端口ATS能力\n} DMAR_TABLE;\n```\n\n### CPU检测逻辑\n```c\nBOOLEAN IsIntelCpu(VOID) {\n    UINT32 Eax, Ebx, Ecx, Edx;\n    CHAR8 VendorString[13];\n    \n    // 执行CPUID指令\n    AsmCpuid(0, &Eax, &Ebx, &Ecx, &Edx);\n    \n    // 构建厂商字符串\n    *(UINT32*)&VendorString[0] = Ebx;\n    *(UINT32*)&VendorString[4] = Edx;\n    *(UINT32*)&VendorString[8] = Ecx;\n    VendorString[12] = '\\0';\n    \n    return (CompareMem(VendorString, \"GenuineIntel\", 12) == 0);\n}\n```\n\n## 4. 部署和测试\n\n### 编译环境\n```bash\n# 安装GRUB开发环境\nsudo apt-get install grub-common grub-efi-amd64-dev\n\n# 安装EDK2 (EFI开发)\ngit clone https://github.com/tianocore/edk2.git\ncd edk2\nmake -C BaseTools\n```\n\n### 编译步骤\n```bash\n# 编译GRUB模块\ngrub-mkimage -O x86_64-efi -o grubx64_vtd.efi \\\n    -p /EFI/BOOT normal efi_gop efi_uga video_bochs video_cirrus \\\n    acpi vtd_emulation\n\n# 编译EFI应用\nbuild -a X64 -t GCC5 -b RELEASE -p VtdEmulationPkg/VtdEmulation.dsc\n```\n\n### 测试验证\n```bash\n# 在目标系统上检查VT-d支持\ndmesg | grep -i \"dmar\\|vt-d\\|iommu\"\ncat /proc/iomem | grep dmar\nlspci -v | grep -i iommu\n\n# 检查ACPI表\nsudo cat /sys/firmware/acpi/tables/DMAR | hexdump -C\n```\n\n## 5. 高级优化\n\n### 动态DMAR表生成\n- 根据实际硬件配置动态调整DMAR表内容\n- 支持多种CPU架构（Intel、AMD、ARM）\n- 实现更真实的硬件模拟\n\n### 反检测机制\n- 模拟真实的IOMMU寄存器访问\n- 实现假的DMA重映射功能\n- 绕过高级虚拟化检测工具\n\n### 性能优化\n- 最小化内存占用\n- 优化启动时间\n- 减少系统资源消耗\n\n## 6. 安全考虑\n\n### 风险评估\n- **系统稳定性**: 假VT-d可能导致系统不稳定\n- **安全隔离**: 无法提供真正的DMA保护\n- **检测风险**: 可能被安全软件检测\n\n### 缓解措施\n- 完善的错误处理机制\n- 兼容性测试覆盖\n- 可选的安全模式\n\n---\n\n**注意**: 此技术仅用于研究和教育目的，请遵守相关法律法规。\n"}