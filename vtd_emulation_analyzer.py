#!/usr/bin/env python3
"""
VTd模拟功能深度分析工具
VTd Emulation Deep Analysis Tool
"""

import os
import sys
import struct
import re
import json
from pathlib import Path
from typing import Dict, List, Any, Optional, Tuple

class VTdEmulationAnalyzer:
    """VTd模拟功能分析器"""
    
    def __init__(self, boot_efi_path: str, grub_cfg_path: str):
        self.boot_efi_path = boot_efi_path
        self.grub_cfg_path = grub_cfg_path
        self.boot_efi_data = None
        self.grub_cfg_content = ""

        self.load_files()
    
    def load_files(self):
        """加载文件"""
        try:
            with open(self.boot_efi_path, 'rb') as f:
                self.boot_efi_data = f.read()

            with open(self.grub_cfg_path, 'r', encoding='utf-8') as f:
                self.grub_cfg_content = f.read()

            print(f"✅ 已加载 BOOT.EFI ({len(self.boot_efi_data)} bytes)")
            print(f"✅ 已加载 grub.cfg ({len(self.grub_cfg_content)} chars)")
        except Exception as e:
            print(f"❌ 文件加载失败: {e}")
    
    def analyze_dmar_components(self) -> Dict[str, Any]:
        """分析DMAR相关组件"""
        print("\n🔍 分析DMAR相关组件...")
        
        dmar_analysis = {
            "dmar_insert_function": None,
            "dmar_table_structure": None,
            "acpi_signatures": [],
            "iommu_references": [],
            "cpu_vendor_checks": []
        }
        
        # 1. 查找DmarInsert函数
        dmar_insert_pos = self.boot_efi_data.find(b'DmarInsert')
        if dmar_insert_pos != -1:
            dmar_analysis["dmar_insert_function"] = {
                "offset": f"0x{dmar_insert_pos:08x}",
                "context": self._get_hex_context(dmar_insert_pos, 64)
            }
            print(f"  ✅ 发现DmarInsert函数 @ 0x{dmar_insert_pos:08x}")

        # 2. 查找DMAR表签名
        dmar_sig_positions = []
        pos = 0
        while True:
            pos = self.boot_efi_data.find(b'DMAR', pos)
            if pos == -1:
                break
            dmar_sig_positions.append(pos)
            pos += 1
        
        for pos in dmar_sig_positions:
            dmar_analysis["acpi_signatures"].append({
                "signature": "DMAR",
                "offset": f"0x{pos:08x}",
                "context": self._get_hex_context(pos, 32)
            })
        
        # 3. 查找ACPI相关
        acpi_pos = self.boot_efi_data.find(b'ACPI')
        if acpi_pos != -1:
            dmar_analysis["acpi_signatures"].append({
                "signature": "ACPI",
                "offset": f"0x{acpi_pos:08x}",
                "context": self._get_hex_context(acpi_pos, 32)
            })

        # 4. 查找IOMMU引用
        iommu_patterns = [b'IOMMU', b'I.O.M.M.U', b'iommu']
        for pattern in iommu_patterns:
            pos = self.boot_efi_data.find(pattern)
            if pos != -1:
                dmar_analysis["iommu_references"].append({
                    "pattern": pattern.decode('utf-8', errors='ignore'),
                    "offset": f"0x{pos:08x}",
                    "context": self._get_hex_context(pos, 32)
                })

        # 5. 查找CPU厂商检测
        cpu_vendors = [b'GenuineIntel', b'AuthenticAMD']
        for vendor in cpu_vendors:
            pos = self.boot_efi_data.find(vendor)
            if pos != -1:
                dmar_analysis["cpu_vendor_checks"].append({
                    "vendor": vendor.decode('ascii'),
                    "offset": f"0x{pos:08x}",
                    "context": self._get_hex_context(pos, 32)
                })
        
        return dmar_analysis
    
    def analyze_grub_acpi_functions(self) -> Dict[str, Any]:
        """分析GRUB中的ACPI相关函数"""
        print("\n🔍 分析GRUB ACPI功能...")
        
        grub_analysis = {
            "acpi_functions": [],
            "vtoy_chain_usage": [],
            "debug_flags": [],
            "memory_operations": []
        }
        
        # 1. 查找ACPI相关函数
        acpi_patterns = [
            r'ventoy_acpi_param\s+([^}]+)',
            r'vt_acpi_param\s+([^}]+)',
            r'vtoy_chain_mem_addr',
            r'vtoy_chain_mem_size'
        ]
        
        for pattern in acpi_patterns:
            matches = re.finditer(pattern, self.grub_cfg_content, re.MULTILINE)
            for match in matches:
                grub_analysis["acpi_functions"].append({
                    "pattern": pattern,
                    "match": match.group(0),
                    "context": self._get_grub_context(match.start(), 100)
                })
        
        # 2. 查找内存链操作
        chain_pattern = r'vtoy_chain_mem_addr[^}]*'
        matches = re.finditer(chain_pattern, self.grub_cfg_content)
        for match in matches:
            grub_analysis["vtoy_chain_usage"].append({
                "usage": match.group(0),
                "context": self._get_grub_context(match.start(), 200)
            })
        
        # 3. 查找调试标志
        debug_pattern = r'vtdebug_flag'
        matches = re.finditer(debug_pattern, self.grub_cfg_content)
        for match in matches:
            grub_analysis["debug_flags"].append({
                "position": match.start(),
                "context": self._get_grub_context(match.start(), 50)
            })
        
        return grub_analysis
    
    def analyze_vtd_emulation_flow(self) -> Dict[str, Any]:
        """分析VTd模拟流程"""
        print("\n🔍 分析VTd模拟完整流程...")
        
        flow_analysis = {
            "initialization_phase": {},
            "memory_allocation_phase": {},
            "dmar_construction_phase": {},
            "injection_phase": {},
            "activation_phase": {}
        }
        
        # 1. 初始化阶段分析
        init_patterns = [
            r'function\s+ventoy_acpi_param[^}]*\{([^}]*)\}',
            r'if\s+\[\s+"\$VTOY_PARAM_NO_ACPI"[^}]*'
        ]
        
        for pattern in init_patterns:
            matches = re.finditer(pattern, self.grub_cfg_content, re.DOTALL)
            for match in matches:
                flow_analysis["initialization_phase"][pattern] = {
                    "code": match.group(0)[:500],  # 限制长度
                    "analysis": "ACPI参数处理和条件检查"
                }
        
        # 2. 内存分配阶段
        memory_patterns = [
            r'vtoy_chain_mem_addr.*?(\d+)',
            r'vt_load_img_memdisk.*?vtoy_iso_buf'
        ]
        
        for pattern in memory_patterns:
            matches = re.finditer(pattern, self.grub_cfg_content)
            for match in matches:
                flow_analysis["memory_allocation_phase"][pattern] = {
                    "match": match.group(0),
                    "analysis": "内存分配和地址管理"
                }
        
        return flow_analysis
    
    def reverse_engineer_dmar_structure(self) -> Dict[str, Any]:
        """逆向工程DMAR表结构"""
        print("\n🔍 逆向分析DMAR表结构...")
        
        dmar_structure = {
            "acpi_header": {},
            "drhd_entries": [],
            "rmrr_entries": [],
            "construction_logic": {}
        }
        
        # 查找DMAR表构建逻辑
        dmar_pos = self.boot_efi_data.find(b'DMAR')
        if dmar_pos != -1:
            # 分析DMAR表头
            if dmar_pos + 36 <= len(self.boot_efi_data):
                header_data = self.boot_efi_data[dmar_pos:dmar_pos+36]
                
                try:
                    # ACPI表头结构
                    signature = header_data[0:4]
                    length = struct.unpack('<L', header_data[4:8])[0] if len(header_data) >= 8 else 0
                    revision = header_data[8] if len(header_data) > 8 else 0
                    checksum = header_data[9] if len(header_data) > 9 else 0
                    
                    dmar_structure["acpi_header"] = {
                        "signature": signature.decode('ascii', errors='ignore'),
                        "length": length,
                        "revision": revision,
                        "checksum": checksum,
                        "raw_data": header_data.hex().upper()
                    }
                except:
                    dmar_structure["acpi_header"] = {
                        "error": "Failed to parse header",
                        "raw_data": header_data.hex().upper()
                    }
        
        return dmar_structure
    
    def generate_vtd_replication_guide(self) -> str:
        """生成VTd模拟复刻指南"""
        guide = """
# VTd模拟功能复刻指南

## 1. 核心原理理解

### VTd模拟的本质
- **目标**: 让不支持Intel VT-d的硬件能够"欺骗"操作系统和虚拟化软件
- **方法**: 通过注入假的ACPI DMAR表，使系统认为硬件支持VT-d
- **时机**: 在操作系统启动前的GRUB阶段完成注入

### 技术栈
1. **GRUB模块**: 负责ACPI表构建和内存管理
2. **EFI应用**: 负责DMAR表注入和系统欺骗
3. **内存管理**: 使用EBDA区域存储假表

## 2. 实现步骤

### 步骤1: GRUB ACPI模块开发
```c
// acpi_vtd.c - GRUB ACPI VTd模拟模块
#include <grub/acpi.h>
#include <grub/memory.h>

struct grub_acpi_dmar {
    struct grub_acpi_table_header header;
    grub_uint8_t host_address_width;
    grub_uint8_t flags;
    grub_uint8_t reserved[10];
    // DRHD entries follow
};

grub_err_t grub_acpi_create_dmar_table(void) {
    struct grub_acpi_dmar *dmar;
    grub_size_t table_size = sizeof(struct grub_acpi_dmar) + 64; // 基础大小
    
    // 1. 分配内存
    dmar = grub_malloc(table_size);
    if (!dmar) return GRUB_ERR_OUT_OF_MEMORY;
    
    // 2. 构建表头
    grub_memcpy(dmar->header.signature, "DMAR", 4);
    dmar->header.length = table_size;
    dmar->header.revision = 1;
    grub_memcpy(dmar->header.oemid, "VTDEMU", 6);
    
    // 3. 设置VT-d参数
    dmar->host_address_width = 46; // 64位系统典型值
    dmar->flags = 0x01; // INTR_REMAP支持
    
    // 4. 添加DRHD条目
    add_drhd_entry(dmar, 0xFED90000); // Intel标准IOMMU基址
    
    // 5. 计算校验和
    dmar->header.checksum = calculate_checksum(dmar, table_size);
    
    // 6. 注入到ACPI表链
    return grub_acpi_add_table(dmar);
}
```

### 步骤2: EFI DmarInsert功能
```c
// dmar_insert.c - EFI DMAR注入功能
#include <Uefi.h>
#include <Library/UefiLib.h>

EFI_STATUS DmarInsert(VOID) {
    EFI_ACPI_SDT_PROTOCOL *AcpiSdt;
    EFI_ACPI_TABLE_PROTOCOL *AcpiTable;
    UINTN TableKey;
    
    // 1. 获取ACPI协议
    Status = gBS->LocateProtocol(&gEfiAcpiSdtProtocolGuid, NULL, (VOID**)&AcpiSdt);
    if (EFI_ERROR(Status)) return Status;
    
    // 2. 构建DMAR表
    ACPI_DMAR_TABLE *DmarTable = BuildDmarTable();
    
    // 3. 安装DMAR表
    Status = AcpiTable->InstallAcpiTable(AcpiTable, DmarTable, 
                                        DmarTable->Header.Length, &TableKey);
    
    return Status;
}

ACPI_DMAR_TABLE* BuildDmarTable(VOID) {
    ACPI_DMAR_TABLE *Dmar;
    
    // 分配内存
    Dmar = AllocatePool(sizeof(ACPI_DMAR_TABLE) + 256);
    
    // 构建表头
    CopyMem(Dmar->Header.Signature, "DMAR", 4);
    Dmar->Header.Length = sizeof(ACPI_DMAR_TABLE) + 256;
    Dmar->Header.Revision = 1;
    
    // 检测CPU类型并构建相应的DRHD
    if (IsIntelCpu()) {
        BuildIntelDrhd(Dmar);
    } else if (IsAmdCpu()) {
        BuildAmdDrhd(Dmar); // AMD IOMMU模拟
    }
    
    // 计算校验和
    Dmar->Header.Checksum = CalculateAcpiChecksum(Dmar);
    
    return Dmar;
}
```

### 步骤3: 内存管理和参数传递
```bash
# grub.cfg中的集成
function vtd_emulation_init {
    # 检查是否启用VTd模拟
    if [ "$VTOY_PARAM_NO_ACPI" != "1" ]; then
        # 分配内存用于DMAR表
        vt_alloc_chain_memory 2048
        
        # 构建DMAR表
        vt_build_dmar_table ${vtoy_chain_mem_addr}
        
        # 传递给EFI应用
        set vtd_dmar_addr=${vtoy_chain_mem_addr}
        export vtd_dmar_addr
    fi
}

function vtd_inject_dmar {
    if [ -n "$vtd_dmar_addr" ]; then
        # 调用EFI应用注入DMAR表
        chainloader ${vtoy_path}/vtd_injector.efi dmar_addr=${vtd_dmar_addr}
        boot
    fi
}
```

## 3. 关键技术细节

### DMAR表结构
```c
typedef struct {
    ACPI_TABLE_HEADER Header;           // 标准ACPI表头
    UINT8 HostAddressWidth;            // 主机地址宽度
    UINT8 Flags;                       // 标志位
    UINT8 Reserved[10];                // 保留字段
    
    // 可变长度的重映射结构
    DRHD_STRUCTURE Drhd[MAX_DRHD];     // DMA重映射硬件单元
    RMRR_STRUCTURE Rmrr[MAX_RMRR];     // 保留内存区域
    ATSR_STRUCTURE Atsr[MAX_ATSR];     // 根端口ATS能力
} DMAR_TABLE;
```

### CPU检测逻辑
```c
BOOLEAN IsIntelCpu(VOID) {
    UINT32 Eax, Ebx, Ecx, Edx;
    CHAR8 VendorString[13];
    
    // 执行CPUID指令
    AsmCpuid(0, &Eax, &Ebx, &Ecx, &Edx);
    
    // 构建厂商字符串
    *(UINT32*)&VendorString[0] = Ebx;
    *(UINT32*)&VendorString[4] = Edx;
    *(UINT32*)&VendorString[8] = Ecx;
    VendorString[12] = '\\0';
    
    return (CompareMem(VendorString, "GenuineIntel", 12) == 0);
}
```

## 4. 部署和测试

### 编译环境
```bash
# 安装GRUB开发环境
sudo apt-get install grub-common grub-efi-amd64-dev

# 安装EDK2 (EFI开发)
git clone https://github.com/tianocore/edk2.git
cd edk2
make -C BaseTools
```

### 编译步骤
```bash
# 编译GRUB模块
grub-mkimage -O x86_64-efi -o grubx64_vtd.efi \\
    -p /EFI/BOOT normal efi_gop efi_uga video_bochs video_cirrus \\
    acpi vtd_emulation

# 编译EFI应用
build -a X64 -t GCC5 -b RELEASE -p VtdEmulationPkg/VtdEmulation.dsc
```

### 测试验证
```bash
# 在目标系统上检查VT-d支持
dmesg | grep -i "dmar\\|vt-d\\|iommu"
cat /proc/iomem | grep dmar
lspci -v | grep -i iommu

# 检查ACPI表
sudo cat /sys/firmware/acpi/tables/DMAR | hexdump -C
```

## 5. 高级优化

### 动态DMAR表生成
- 根据实际硬件配置动态调整DMAR表内容
- 支持多种CPU架构（Intel、AMD、ARM）
- 实现更真实的硬件模拟

### 反检测机制
- 模拟真实的IOMMU寄存器访问
- 实现假的DMA重映射功能
- 绕过高级虚拟化检测工具

### 性能优化
- 最小化内存占用
- 优化启动时间
- 减少系统资源消耗

## 6. 安全考虑

### 风险评估
- **系统稳定性**: 假VT-d可能导致系统不稳定
- **安全隔离**: 无法提供真正的DMA保护
- **检测风险**: 可能被安全软件检测

### 缓解措施
- 完善的错误处理机制
- 兼容性测试覆盖
- 可选的安全模式

---

**注意**: 此技术仅用于研究和教育目的，请遵守相关法律法规。
"""
        return guide
    
    def _get_hex_context(self, pos: int, length: int) -> str:
        """获取十六进制上下文"""
        start = max(0, pos - length // 2)
        end = min(len(self.boot_efi_data), pos + length // 2)
        data = self.boot_efi_data[start:end]
        return ' '.join(f'{b:02x}' for b in data)
    
    def _get_grub_context(self, pos: int, length: int) -> str:
        """获取GRUB配置上下文"""
        start = max(0, pos - length // 2)
        end = min(len(self.grub_cfg_content), pos + length // 2)
        return self.grub_cfg_content[start:end]
    
    def generate_full_analysis_report(self) -> Dict[str, Any]:
        """生成完整分析报告"""
        print("\n📊 生成完整VTd模拟分析报告...")
        
        report = {
            "file_info": {
                "boot_efi_size": len(self.boot_efi_data),
                "grub_cfg_size": len(self.grub_cfg_content)
            },
            "dmar_analysis": self.analyze_dmar_components(),
            "grub_analysis": self.analyze_grub_acpi_functions(),
            "flow_analysis": self.analyze_vtd_emulation_flow(),
            "dmar_structure": self.reverse_engineer_dmar_structure(),
            "replication_guide": self.generate_vtd_replication_guide()
        }
        
        return report

def main():
    """主函数"""
    print("=== VTd模拟功能深度分析工具 ===")
    print("=== VTd Emulation Deep Analysis Tool ===\\n")
    
    boot_efi_path = "EFI/BOOT/BOOT.EFI"
    grub_cfg_path = "grub/grub.cfg"

    if not Path(boot_efi_path).exists():
        print(f"❌ 文件不存在: {boot_efi_path}")
        return

    if not Path(grub_cfg_path).exists():
        print(f"❌ 文件不存在: {grub_cfg_path}")
        return

    # 创建分析器
    analyzer = VTdEmulationAnalyzer(boot_efi_path, grub_cfg_path)
    
    # 执行完整分析
    report = analyzer.generate_full_analysis_report()
    
    # 保存报告
    with open('vtd_emulation_analysis_report.json', 'w', encoding='utf-8') as f:
        json.dump(report, f, indent=2, ensure_ascii=False)
    
    # 保存复刻指南
    with open('vtd_replication_guide.md', 'w', encoding='utf-8') as f:
        f.write(report["replication_guide"])
    
    print(f"\\n📁 分析完成！")
    print(f"  - 详细报告: vtd_emulation_analysis_report.json")
    print(f"  - 复刻指南: vtd_replication_guide.md")
    
    # 显示关键发现
    dmar_analysis = report["dmar_analysis"]
    if dmar_analysis["dmar_insert_function"]:
        print(f"\\n🎯 关键发现:")
        print(f"  - DmarInsert函数: {dmar_analysis['dmar_insert_function']['offset']}")
        print(f"  - DMAR签名数量: {len(dmar_analysis['acpi_signatures'])}")
        print(f"  - IOMMU引用数量: {len(dmar_analysis['iommu_references'])}")
        print(f"  - CPU厂商检测: {len(dmar_analysis['cpu_vendor_checks'])}")

if __name__ == "__main__":
    main()
