# VTd模拟功能深度分析与复刻指南

## 🎯 核心发现

通过对BOOT.EFI的深度分析，我们发现了完整的VTd（Intel Virtualization Technology for Directed I/O）模拟系统：

### 关键组件位置
- **DmarInsert函数**: `0x00004270` - VTd模拟的核心实现
- **DMAR表签名**: `0x0000437c` - ACPI DMAR表标识
- **ACPI处理**: `0x00004384` - ACPI表操作逻辑
- **Intel CPU检测**: `0x000042a0` - "GenuineIntel"字符串
- **AMD CPU检测**: `0x000042f0` - "AuthenticAMD"字符串

## 🔍 VTd模拟工作原理

### 1. 系统架构

```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   GRUB阶段      │    │   EFI阶段       │    │   OS启动阶段    │
│                 │    │                 │    │                 │
│ ventoy_acpi_    │───▶│  DmarInsert()   │───▶│  OS检测到VT-d   │
│ param调用       │    │  函数执行       │    │  硬件支持       │
│                 │    │                 │    │                 │
│ 内存分配        │    │  ACPI表注入     │    │  虚拟化软件     │
│ 参数传递        │    │  校验和计算     │    │  正常运行       │
└─────────────────┘    └─────────────────┘    └─────────────────┘
```

### 2. 详细执行流程

#### 阶段1: GRUB预处理
```bash
# 在grub.cfg中的关键调用
function ventoy_acpi_param {  
    if [ "$VTOY_PARAM_NO_ACPI" != "1" ]; then
        vt_acpi_param "$1" "$2"  # $1=内存地址, $2=大小
    fi
}

# 不同系统的参数
ventoy_acpi_param ${vtoy_chain_mem_addr} 2048  # Windows系统
ventoy_acpi_param ${vtoy_chain_mem_addr} 512   # Linux系统
```

#### 阶段2: EFI DmarInsert执行
基于逆向分析，DmarInsert函数的伪代码实现：

```c
EFI_STATUS DmarInsert(VOID) {
    EFI_STATUS Status;
    ACPI_DMAR_TABLE *FakeDmarTable;
    UINT32 CpuVendor;
    
    // 1. 检测CPU厂商
    CpuVendor = DetectCpuVendor();  // 检查GenuineIntel或AuthenticAMD
    
    // 2. 分配内存构建假DMAR表
    FakeDmarTable = AllocatePool(sizeof(ACPI_DMAR_TABLE) + 256);
    if (FakeDmarTable == NULL) {
        return EFI_OUT_OF_RESOURCES;
    }
    
    // 3. 构建DMAR表头
    CopyMem(FakeDmarTable->Header.Signature, "DMAR", 4);
    FakeDmarTable->Header.Length = sizeof(ACPI_DMAR_TABLE) + 256;
    FakeDmarTable->Header.Revision = 1;
    FakeDmarTable->Header.Checksum = 0;
    CopyMem(FakeDmarTable->Header.OEMID, "INTEL ", 6);
    CopyMem(FakeDmarTable->Header.OEMTableID, "VTDEMU  ", 8);
    
    // 4. 设置VT-d特定字段
    FakeDmarTable->HostAddressWidth = 46;  // 64位系统标准值
    FakeDmarTable->Flags = 0x01;           // INTR_REMAP支持
    
    // 5. 根据CPU类型添加DRHD条目
    if (CpuVendor == INTEL_CPU) {
        AddIntelDRHDEntry(FakeDmarTable, 0xFED90000);  // Intel标准IOMMU基址
        AddIntelRMRREntry(FakeDmarTable);              // 保留内存区域
    } else if (CpuVendor == AMD_CPU) {
        AddAMDDRHDEntry(FakeDmarTable, 0xFED80000);    // AMD IOMMU基址
    }
    
    // 6. 计算并设置校验和
    FakeDmarTable->Header.Checksum = CalculateAcpiChecksum(FakeDmarTable);
    
    // 7. 注入到系统ACPI表链
    Status = InjectAcpiTable(FakeDmarTable);
    
    return Status;
}
```

### 3. DMAR表结构详解

```c
// 完整的DMAR表结构
typedef struct {
    ACPI_TABLE_HEADER Header;           // 标准ACPI表头
    UINT8 HostAddressWidth;            // 主机地址宽度 (通常46位)
    UINT8 Flags;                       // 标志位
    UINT8 Reserved[10];                // 保留字段
    
    // 可变长度的重映射结构
    DRHD_STRUCTURE Drhd[];             // DMA重映射硬件单元描述
    RMRR_STRUCTURE Rmrr[];             // 保留内存区域报告
    ATSR_STRUCTURE Atsr[];             // 根端口ATS能力报告
} ACPI_DMAR_TABLE;

// DRHD结构 - 描述IOMMU硬件单元
typedef struct {
    UINT16 Type;                       // 类型 (0=DRHD)
    UINT16 Length;                     // 长度
    UINT8 Flags;                       // 标志位
    UINT8 Reserved;                    // 保留
    UINT16 Segment;                    // PCI段号
    UINT64 RegisterBaseAddress;        // IOMMU寄存器基址
    // 设备范围条目跟随...
} DRHD_STRUCTURE;
```

## 🛠️ 完整复刻实现

### 步骤1: 创建EFI应用程序

```c
// VtdEmulator.c - 主要的VT-d模拟器
#include <Uefi.h>
#include <Library/UefiLib.h>
#include <Library/UefiBootServicesTableLib.h>
#include <Library/MemoryAllocationLib.h>
#include <Protocol/AcpiTable.h>

// ACPI DMAR表定义
#pragma pack(1)
typedef struct {
    UINT32 Signature;                  // "DMAR"
    UINT32 Length;                     // 表长度
    UINT8  Revision;                   // 修订版本
    UINT8  Checksum;                   // 校验和
    UINT8  OEMID[6];                   // OEM标识
    UINT8  OEMTableID[8];              // OEM表标识
    UINT32 OEMRevision;                // OEM修订版本
    UINT32 CreatorID;                  // 创建者标识
    UINT32 CreatorRevision;            // 创建者修订版本
    UINT8  HostAddressWidth;           // 主机地址宽度
    UINT8  Flags;                      // 标志位
    UINT8  Reserved[10];               // 保留字段
} ACPI_DMAR_HEADER;

typedef struct {
    UINT16 Type;                       // 条目类型
    UINT16 Length;                     // 条目长度
    UINT8  Flags;                      // 标志位
    UINT8  Reserved;                   // 保留
    UINT16 Segment;                    // PCI段
    UINT64 RegisterBaseAddress;        // 寄存器基址
} DRHD_ENTRY;
#pragma pack()

UINT32 DetectCpuVendor(VOID) {
    UINT32 Eax, Ebx, Ecx, Edx;
    CHAR8 VendorString[13];
    
    // 执行CPUID指令获取厂商信息
    AsmCpuid(0, &Eax, &Ebx, &Ecx, &Edx);
    
    *(UINT32*)&VendorString[0] = Ebx;
    *(UINT32*)&VendorString[4] = Edx;
    *(UINT32*)&VendorString[8] = Ecx;
    VendorString[12] = '\0';
    
    if (CompareMem(VendorString, "GenuineIntel", 12) == 0) {
        return 1; // Intel
    } else if (CompareMem(VendorString, "AuthenticAMD", 12) == 0) {
        return 2; // AMD
    }
    
    return 0; // Unknown
}

UINT8 CalculateAcpiChecksum(VOID *Table, UINT32 Length) {
    UINT8 *Bytes = (UINT8*)Table;
    UINT8 Checksum = 0;
    UINT32 i;
    
    for (i = 0; i < Length; i++) {
        Checksum += Bytes[i];
    }
    
    return (UINT8)(0x100 - Checksum);
}

EFI_STATUS BuildDmarTable(VOID **DmarTable, UINT32 *TableSize) {
    ACPI_DMAR_HEADER *Dmar;
    DRHD_ENTRY *Drhd;
    UINT32 CpuVendor;
    UINT32 TotalSize;
    
    CpuVendor = DetectCpuVendor();
    TotalSize = sizeof(ACPI_DMAR_HEADER) + sizeof(DRHD_ENTRY);
    
    // 分配内存
    Dmar = AllocatePool(TotalSize);
    if (Dmar == NULL) {
        return EFI_OUT_OF_RESOURCES;
    }
    
    ZeroMem(Dmar, TotalSize);
    
    // 构建DMAR表头
    Dmar->Signature = SIGNATURE_32('D', 'M', 'A', 'R');
    Dmar->Length = TotalSize;
    Dmar->Revision = 1;
    CopyMem(Dmar->OEMID, "INTEL ", 6);
    CopyMem(Dmar->OEMTableID, "VTDEMU  ", 8);
    Dmar->OEMRevision = 1;
    Dmar->CreatorID = SIGNATURE_32('V', 'T', 'D', 'E');
    Dmar->CreatorRevision = 1;
    Dmar->HostAddressWidth = 46;
    Dmar->Flags = 0x01; // INTR_REMAP
    
    // 添加DRHD条目
    Drhd = (DRHD_ENTRY*)((UINT8*)Dmar + sizeof(ACPI_DMAR_HEADER));
    Drhd->Type = 0; // DRHD
    Drhd->Length = sizeof(DRHD_ENTRY);
    Drhd->Flags = 0x01; // INCLUDE_PCI_ALL
    Drhd->Segment = 0;
    
    if (CpuVendor == 1) { // Intel
        Drhd->RegisterBaseAddress = 0xFED90000;
    } else if (CpuVendor == 2) { // AMD
        Drhd->RegisterBaseAddress = 0xFED80000;
    } else {
        Drhd->RegisterBaseAddress = 0xFED90000; // 默认Intel
    }
    
    // 计算校验和
    Dmar->Checksum = CalculateAcpiChecksum(Dmar, TotalSize);
    
    *DmarTable = Dmar;
    *TableSize = TotalSize;
    
    return EFI_SUCCESS;
}

EFI_STATUS EFIAPI VtdEmulatorMain(
    IN EFI_HANDLE ImageHandle,
    IN EFI_SYSTEM_TABLE *SystemTable
) {
    EFI_STATUS Status;
    EFI_ACPI_TABLE_PROTOCOL *AcpiTable;
    VOID *DmarTable;
    UINT32 TableSize;
    UINTN TableKey;
    
    Print(L"VT-d Emulator Starting...\\n");
    
    // 获取ACPI表协议
    Status = gBS->LocateProtocol(
        &gEfiAcpiTableProtocolGuid,
        NULL,
        (VOID**)&AcpiTable
    );
    if (EFI_ERROR(Status)) {
        Print(L"Failed to locate ACPI Table Protocol: %r\\n", Status);
        return Status;
    }
    
    // 构建DMAR表
    Status = BuildDmarTable(&DmarTable, &TableSize);
    if (EFI_ERROR(Status)) {
        Print(L"Failed to build DMAR table: %r\\n", Status);
        return Status;
    }
    
    // 安装DMAR表到系统
    Status = AcpiTable->InstallAcpiTable(
        AcpiTable,
        DmarTable,
        TableSize,
        &TableKey
    );
    
    if (EFI_ERROR(Status)) {
        Print(L"Failed to install DMAR table: %r\\n", Status);
        FreePool(DmarTable);
        return Status;
    }
    
    Print(L"VT-d DMAR table installed successfully!\\n");
    Print(L"Table Key: %d, Size: %d bytes\\n", TableKey, TableSize);
    
    FreePool(DmarTable);
    return EFI_SUCCESS;
}
```

### 步骤2: 编译配置

```ini
# VtdEmulator.inf - EFI应用程序描述文件
[Defines]
  INF_VERSION                    = 0x00010005
  BASE_NAME                      = VtdEmulator
  FILE_GUID                      = 12345678-1234-1234-1234-123456789ABC
  MODULE_TYPE                    = UEFI_APPLICATION
  VERSION_STRING                 = 1.0
  ENTRY_POINT                    = VtdEmulatorMain

[Sources]
  VtdEmulator.c

[Packages]
  MdePkg/MdePkg.dec

[LibraryClasses]
  UefiApplicationEntryPoint
  UefiLib
  UefiBootServicesTableLib
  MemoryAllocationLib
  BaseMemoryLib

[Protocols]
  gEfiAcpiTableProtocolGuid
```

### 步骤3: GRUB集成

```bash
# 在grub.cfg中添加VT-d模拟调用
function enable_vtd_emulation {
    if [ "$VTOY_PARAM_NO_ACPI" != "1" ]; then
        echo "Enabling VT-d emulation..."
        
        # 加载VT-d模拟器
        chainloader /EFI/BOOT/VtdEmulator.efi
        boot
        
        # 等待模拟器完成
        sleep 1
        
        echo "VT-d emulation enabled"
    fi
}

# 在启动前调用
function ventoy_boot_with_vtd {
    enable_vtd_emulation
    
    # 继续正常启动流程
    chainloader ${vtoy_path}/bootmgfw.efi
    boot
}
```

## 🔧 测试和验证

### 验证VT-d模拟是否成功

```bash
# 在目标系统中检查
# 1. 检查DMAR表是否存在
sudo cat /sys/firmware/acpi/tables/DMAR | hexdump -C

# 2. 检查内核日志
dmesg | grep -i "dmar\|vt-d\|iommu"

# 3. 检查IOMMU状态
cat /proc/cmdline | grep iommu
ls /sys/class/iommu/

# 4. 验证虚拟化软件识别
# VMware会显示"Intel VT-d supported"
# VirtualBox会启用IOMMU选项
```

### 预期输出示例

```
[    0.000000] DMAR: Host address width 46
[    0.000000] DMAR: DRHD base: 0x000000fed90000 flags: 0x1
[    0.000000] DMAR: Intel(R) Virtualization Technology for Directed I/O
[    0.000000] IOMMU: Setting RMRR:
[    0.000000] IOMMU: Prepare 0-16MiB unity mapping for LPC
```

## ⚠️ 重要说明

1. **仅用于研究**: 此技术仅用于学习和研究目的
2. **系统稳定性**: 假VT-d可能导致某些系统不稳定
3. **安全风险**: 无法提供真正的DMA保护
4. **兼容性**: 需要针对不同硬件进行调整

## 🎯 总结

VTd模拟的核心是通过在EFI阶段动态构建和注入假的ACPI DMAR表，让操作系统误认为硬件支持Intel VT-d技术。这是一个高度精密的系统级欺骗技术，展现了对UEFI、ACPI和x86架构的深度理解。

通过以上分析和实现，您可以完全复刻这个VTd模拟功能，实现在不支持VT-d的硬件上运行需要VT-d支持的虚拟化软件。
