
# VTd模拟功能复刻指南

## 1. 核心原理理解

### VTd模拟的本质
- **目标**: 让不支持Intel VT-d的硬件能够"欺骗"操作系统和虚拟化软件
- **方法**: 通过注入假的ACPI DMAR表，使系统认为硬件支持VT-d
- **时机**: 在操作系统启动前的GRUB阶段完成注入

### 技术栈
1. **GRUB模块**: 负责ACPI表构建和内存管理
2. **EFI应用**: 负责DMAR表注入和系统欺骗
3. **内存管理**: 使用EBDA区域存储假表

## 2. 实现步骤

### 步骤1: GRUB ACPI模块开发
```c
// acpi_vtd.c - GRUB ACPI VTd模拟模块
#include <grub/acpi.h>
#include <grub/memory.h>

struct grub_acpi_dmar {
    struct grub_acpi_table_header header;
    grub_uint8_t host_address_width;
    grub_uint8_t flags;
    grub_uint8_t reserved[10];
    // DRHD entries follow
};

grub_err_t grub_acpi_create_dmar_table(void) {
    struct grub_acpi_dmar *dmar;
    grub_size_t table_size = sizeof(struct grub_acpi_dmar) + 64; // 基础大小
    
    // 1. 分配内存
    dmar = grub_malloc(table_size);
    if (!dmar) return GRUB_ERR_OUT_OF_MEMORY;
    
    // 2. 构建表头
    grub_memcpy(dmar->header.signature, "DMAR", 4);
    dmar->header.length = table_size;
    dmar->header.revision = 1;
    grub_memcpy(dmar->header.oemid, "VTDEMU", 6);
    
    // 3. 设置VT-d参数
    dmar->host_address_width = 46; // 64位系统典型值
    dmar->flags = 0x01; // INTR_REMAP支持
    
    // 4. 添加DRHD条目
    add_drhd_entry(dmar, 0xFED90000); // Intel标准IOMMU基址
    
    // 5. 计算校验和
    dmar->header.checksum = calculate_checksum(dmar, table_size);
    
    // 6. 注入到ACPI表链
    return grub_acpi_add_table(dmar);
}
```

### 步骤2: EFI DmarInsert功能
```c
// dmar_insert.c - EFI DMAR注入功能
#include <Uefi.h>
#include <Library/UefiLib.h>

EFI_STATUS DmarInsert(VOID) {
    EFI_ACPI_SDT_PROTOCOL *AcpiSdt;
    EFI_ACPI_TABLE_PROTOCOL *AcpiTable;
    UINTN TableKey;
    
    // 1. 获取ACPI协议
    Status = gBS->LocateProtocol(&gEfiAcpiSdtProtocolGuid, NULL, (VOID**)&AcpiSdt);
    if (EFI_ERROR(Status)) return Status;
    
    // 2. 构建DMAR表
    ACPI_DMAR_TABLE *DmarTable = BuildDmarTable();
    
    // 3. 安装DMAR表
    Status = AcpiTable->InstallAcpiTable(AcpiTable, DmarTable, 
                                        DmarTable->Header.Length, &TableKey);
    
    return Status;
}

ACPI_DMAR_TABLE* BuildDmarTable(VOID) {
    ACPI_DMAR_TABLE *Dmar;
    
    // 分配内存
    Dmar = AllocatePool(sizeof(ACPI_DMAR_TABLE) + 256);
    
    // 构建表头
    CopyMem(Dmar->Header.Signature, "DMAR", 4);
    Dmar->Header.Length = sizeof(ACPI_DMAR_TABLE) + 256;
    Dmar->Header.Revision = 1;
    
    // 检测CPU类型并构建相应的DRHD
    if (IsIntelCpu()) {
        BuildIntelDrhd(Dmar);
    } else if (IsAmdCpu()) {
        BuildAmdDrhd(Dmar); // AMD IOMMU模拟
    }
    
    // 计算校验和
    Dmar->Header.Checksum = CalculateAcpiChecksum(Dmar);
    
    return Dmar;
}
```

### 步骤3: 内存管理和参数传递
```bash
# grub.cfg中的集成
function vtd_emulation_init {
    # 检查是否启用VTd模拟
    if [ "$VTOY_PARAM_NO_ACPI" != "1" ]; then
        # 分配内存用于DMAR表
        vt_alloc_chain_memory 2048
        
        # 构建DMAR表
        vt_build_dmar_table ${vtoy_chain_mem_addr}
        
        # 传递给EFI应用
        set vtd_dmar_addr=${vtoy_chain_mem_addr}
        export vtd_dmar_addr
    fi
}

function vtd_inject_dmar {
    if [ -n "$vtd_dmar_addr" ]; then
        # 调用EFI应用注入DMAR表
        chainloader ${vtoy_path}/vtd_injector.efi dmar_addr=${vtd_dmar_addr}
        boot
    fi
}
```

## 3. 关键技术细节

### DMAR表结构
```c
typedef struct {
    ACPI_TABLE_HEADER Header;           // 标准ACPI表头
    UINT8 HostAddressWidth;            // 主机地址宽度
    UINT8 Flags;                       // 标志位
    UINT8 Reserved[10];                // 保留字段
    
    // 可变长度的重映射结构
    DRHD_STRUCTURE Drhd[MAX_DRHD];     // DMA重映射硬件单元
    RMRR_STRUCTURE Rmrr[MAX_RMRR];     // 保留内存区域
    ATSR_STRUCTURE Atsr[MAX_ATSR];     // 根端口ATS能力
} DMAR_TABLE;
```

### CPU检测逻辑
```c
BOOLEAN IsIntelCpu(VOID) {
    UINT32 Eax, Ebx, Ecx, Edx;
    CHAR8 VendorString[13];
    
    // 执行CPUID指令
    AsmCpuid(0, &Eax, &Ebx, &Ecx, &Edx);
    
    // 构建厂商字符串
    *(UINT32*)&VendorString[0] = Ebx;
    *(UINT32*)&VendorString[4] = Edx;
    *(UINT32*)&VendorString[8] = Ecx;
    VendorString[12] = '\0';
    
    return (CompareMem(VendorString, "GenuineIntel", 12) == 0);
}
```

## 4. 部署和测试

### 编译环境
```bash
# 安装GRUB开发环境
sudo apt-get install grub-common grub-efi-amd64-dev

# 安装EDK2 (EFI开发)
git clone https://github.com/tianocore/edk2.git
cd edk2
make -C BaseTools
```

### 编译步骤
```bash
# 编译GRUB模块
grub-mkimage -O x86_64-efi -o grubx64_vtd.efi \
    -p /EFI/BOOT normal efi_gop efi_uga video_bochs video_cirrus \
    acpi vtd_emulation

# 编译EFI应用
build -a X64 -t GCC5 -b RELEASE -p VtdEmulationPkg/VtdEmulation.dsc
```

### 测试验证
```bash
# 在目标系统上检查VT-d支持
dmesg | grep -i "dmar\|vt-d\|iommu"
cat /proc/iomem | grep dmar
lspci -v | grep -i iommu

# 检查ACPI表
sudo cat /sys/firmware/acpi/tables/DMAR | hexdump -C
```

## 5. 高级优化

### 动态DMAR表生成
- 根据实际硬件配置动态调整DMAR表内容
- 支持多种CPU架构（Intel、AMD、ARM）
- 实现更真实的硬件模拟

### 反检测机制
- 模拟真实的IOMMU寄存器访问
- 实现假的DMA重映射功能
- 绕过高级虚拟化检测工具

### 性能优化
- 最小化内存占用
- 优化启动时间
- 减少系统资源消耗

## 6. 安全考虑

### 风险评估
- **系统稳定性**: 假VT-d可能导致系统不稳定
- **安全隔离**: 无法提供真正的DMA保护
- **检测风险**: 可能被安全软件检测

### 缓解措施
- 完善的错误处理机制
- 兼容性测试覆盖
- 可选的安全模式

---

**注意**: 此技术仅用于研究和教育目的，请遵守相关法律法规。
